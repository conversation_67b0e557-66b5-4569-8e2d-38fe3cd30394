"use client";

import styles from "../login/page.module.css";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import Input from "@/components/inputs/input";
import Button from "@/components/buttons/Button";
import Link from "next/link";
import { resetPassword } from "@/services/api";
import toast from "react-hot-toast";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Home />
    </Suspense>
  );
}

function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();

  //States
  const [email, setEmail] = useState("");

  //Functions
  const handleRecover = async () => {
    const response = await resetPassword(email);

    if (response.status === 200) {
      toast.success("Email inviata con successo");
      router.push("/auth/reset-password?email=" + email);
    }
  };

  //Effects
  useEffect(() => {}, []);

  return (
    <MobilePage>
      <h1 className={styles.title}>Reimposta Password</h1>

      <form action="" className={styles.form}>
        <div className="w-full flex flex-col gap-4">
          <div
            className={styles.link4}
            style={{ textAlign: "center", padding: "0px 30px" }}
          >
            Inserisci l&apos;indirizzo email che hai utilizzato per creare il
            tuo account
          </div>
          <Input
            type="email"
            placeholder="E-mail"
            value={email}
            onChange={setEmail}
          />

          <div className="flex justify-center">
            <Button
              text="Invia codice"
              onClick={() => {
                handleRecover();
              }}
              color="white"
              backgroundColor="var(--blue)"
              fontSize="15px"
            />
          </div>
        </div>
      </form>

      <div className={styles.link2}>
        Ricordi la password?{" "}
        <Link className={styles.link} href="/auth/login">
          Accedi
        </Link>
      </div>
    </MobilePage>
  );
}
