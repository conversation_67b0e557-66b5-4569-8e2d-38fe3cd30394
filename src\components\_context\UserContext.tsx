"use client";
import React, { createContext, useState, useCallback, useRef } from "react";
import { viewProfile } from "@/services/api";

interface StaffProperty {
  property_id: string;
  property_name: string;
  permissions: string[];
  is_active: boolean;
  is_onboarded: boolean;
}

interface UserContextType {
  name: string | null;
  image: string | null;
  isCustomer: boolean;
  hasBillingProfile: boolean;
  staffProperties: StaffProperty[];
  email: string | null;
  phone: string | null;
  isNotOwnerPermission: boolean;
  hasContract: boolean; // Based on isCustomer
  profileLoaded: boolean;
  fetchProfile: (force?: boolean) => Promise<void>;
}

export const UserContext = createContext<UserContextType>({
  name: null,
  image: null,
  isCustomer: false,
  hasBillingProfile: false,
  staffProperties: [],
  email: null,
  phone: null,
  isNotOwnerPermission: false,
  hasContract: false,
  profileLoaded: false,
  fetchProfile: async () => {},
});

export const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const [name, setName] = useState<string | null>(null);
  const [image, setImage] = useState<string | null>(null);
  const [isCustomer, setIsCustomer] = useState<boolean>(false);
  const [hasBillingProfile, setHasBillingProfile] = useState<boolean>(false);
  const [staffProperties, setStaffProperties] = useState<StaffProperty[]>([]);
  const [email, setEmail] = useState<string | null>(null);
  const [phone, setPhone] = useState<string | null>(null);
  const [isNotOwnerPermission, setIsNotOwnerPermission] =
    useState<boolean>(false);

  // Track if data has been loaded to prevent unnecessary API calls
  const [profileLoaded, setProfileLoaded] = useState<boolean>(false);

  // Prevent multiple simultaneous API calls
  const profileLoadingRef = useRef<boolean>(false);

  const fetchProfile = useCallback(
    async (force: boolean = false) => {
      // Prevent multiple simultaneous calls and unnecessary fetches
      if (profileLoadingRef.current || (!force && profileLoaded)) {
        return;
      }

      profileLoadingRef.current = true;
      try {
        const call = await viewProfile();
        if (call.user) {
          setName(call.user.name);
          setImage(call.image);
          setIsCustomer(call.is_customer);
          setHasBillingProfile(call.has_billing_profile);

          const staffProps = call.staff_properties || [];
          setStaffProperties(staffProps);

          // Check if user does NOT have owner permission for any property
          // Returns true if the user has no properties with 'owner' permission
          const notOwner: boolean =
            staffProps.length > 0 &&
            !staffProps.some((property: StaffProperty): boolean =>
              property.permissions.includes("owner"),
            );
          setIsNotOwnerPermission(notOwner);

          setEmail(call.user.email);
          setPhone(call.user.phone);
          setProfileLoaded(true);
        }
      } catch (error) {
        console.error("Error fetching profile:", error);
      } finally {
        profileLoadingRef.current = false;
      }
    },
    [profileLoaded],
  );

  // Contract status is based on isCustomer flag
  const hasContract = isCustomer;

  return (
    <UserContext.Provider
      value={{
        name,
        image,
        isCustomer,
        hasBillingProfile,
        staffProperties,
        email,
        phone,
        isNotOwnerPermission,
        hasContract,
        profileLoaded,
        fetchProfile,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};
