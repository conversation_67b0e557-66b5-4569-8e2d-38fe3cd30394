"use client";

import { useContext, useCallback } from "react";
import { UserContext } from "@/components/_context/UserContext";
import { downloadContract, generateContract } from "@/services/api";
import toast from "react-hot-toast";

export const useContract = () => {
  const {
    hasContract, // Based on isCustomer
    hasBillingProfile,
    isCustomer,
    fetchProfile,
  } = useContext(UserContext);

  const handleDownloadContract = useCallback(async () => {
    try {
      const response = await downloadContract();
      if (response.success && response.status === 200) {
        toast.success("Contratto scaricato con successo");
        return true;
      } else if (response.status === 404) {
        toast.error("Contratto non disponibile");
        return false;
      } else {
        toast.error(response.error || "Errore nel download del contratto");
        return false;
      }
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Errore di connessione");
      return false;
    }
  }, []);

  const handleGenerateContract = useCallback(async () => {
    try {
      const response = await generateContract();
      if (response.success && response.status === 201) {
        // Refresh profile to update isCustomer status
        await fetchProfile(true);
        return true;
      } else if (response.status === 400) {
        toast.error(
          response.error || "Completa prima le informazioni di fatturazione",
        );
        return false;
      } else if (response.status === 409) {
        toast.error(response.error || "Contratto già esistente");
        return false;
      } else {
        toast.error(response.error || "Errore nella generazione del contratto");
        return false;
      }
    } catch (error) {
      console.error("Generate error:", error);
      toast.error("Errore di connessione");
      return false;
    }
  }, [fetchProfile]);

  const getContractStatusSummary = useCallback(() => {
    // Contract status is determined by isCustomer flag
    if (hasContract && hasBillingProfile) {
      return {
        status: "available",
        message: "Contratto disponibile",
        canDownload: true,
        canGenerate: false,
        canSign: false,
      };
    }

    // If user has billing profile but no contract, they can generate one
    if (hasBillingProfile && !hasContract) {
      return {
        status: "pending",
        message: "Genera il contratto",
        canDownload: false,
        canGenerate: true,
        canSign: false,
      };
    }

    // User needs to complete billing profile first
    return {
      status: "incomplete",
      message: "Completa il profilo di fatturazione",
      canDownload: false,
      canGenerate: false,
      canSign: false,
    };
  }, [hasContract, hasBillingProfile]);

  return {
    // State
    contract: null, // No longer tracking contract object, only status via isCustomer
    isLoading: false,
    hasContract,
    hasBillingProfile,
    isCustomer,

    // Actions
    downloadContract: handleDownloadContract,
    generateContract: handleGenerateContract,
    refreshContract: () => fetchProfile(true), // Refresh profile to update isCustomer

    // Computed
    statusSummary: getContractStatusSummary(),
  };
};

export default useContract;
