import React, { useState } from "react";
import {
  isImageFile,
  getFileIcon,
  getFileColor,
  formatFileSize,
  getTruncatedFileName,
} from "@/utils/fileUtils";

interface PendingAttachmentProps {
  file: File;
  onRemove: () => void;
  size?: "small" | "medium";
}

export const PendingAttachment: React.FC<PendingAttachmentProps> = ({
  file,
  onRemove,
  size = "medium",
}) => {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const isImage = isImageFile(file.name, file.type) && !imageError;
  const fileColor = getFileColor(file.name);
  const truncatedName = getTruncatedFileName(
    file.name,
    size === "small" ? 12 : 16,
  );

  const sizeConfig = {
    small: {
      container: "w-12 h-12",
      image: "w-12 h-12",
      icon: "text-sm",
      text: "text-xs",
      removeButton: "w-4 h-4 text-xs",
    },
    medium: {
      container: "w-16 h-16",
      image: "w-16 h-16",
      icon: "text-lg",
      text: "text-xs",
      removeButton: "w-5 h-5 text-sm",
    },
  };

  const config = sizeConfig[size];

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      className="relative group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Remove Button */}
      <button
        onClick={onRemove}
        className={`
          ${config.removeButton} absolute -top-1 -right-1 z-10
          bg-red-500 hover:bg-red-600 text-white rounded-full
          flex items-center justify-center cursor-pointer transition-all duration-200
          shadow-md hover:shadow-lg transform hover:scale-110
        `}
        title={`Rimuovi ${file.name}`}
        aria-label={`Rimuovi allegato ${file.name}`}
      >
        ×
      </button>

      {/* File Preview */}
      <div
        className={`
          ${config.container} rounded-lg border-2 border-dashed border-gray-300
          bg-gray-50 hover:bg-gray-100 transition-all duration-200
          flex flex-col items-center justify-center relative overflow-hidden
          ${isHovered ? "shadow-md" : "shadow-sm"}
        `}
        title={`${file.name} (${formatFileSize(file.size)})`}
      >
        {isImage ? (
          <img
            src={URL.createObjectURL(file)}
            alt={file.name}
            className={`${config.image} object-cover rounded-lg`}
            onError={handleImageError}
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-1">
            <div className={`${config.icon} mb-1`} style={{ color: fileColor }}>
              {getFileIcon(file.name)}
            </div>
            {size === "medium" && (
              <div
                className={`${config.text} text-center text-gray-700 max-w-full`}
              >
                <div className="truncate leading-tight">{truncatedName}</div>
                <div className="text-gray-500" style={{ fontSize: "10px" }}>
                  {formatFileSize(file.size)}
                </div>
              </div>
            )}
          </div>
        )}

        {/* File Extension Badge */}
        {!isImage && (
          <div
            className="absolute bottom-0 right-0 px-1 py-0.5 rounded-tl text-white shadow-sm"
            style={{
              backgroundColor: fileColor,
              fontSize: size === "small" ? "7px" : "8px",
              fontWeight: "bold",
            }}
          >
            {file.name.split(".").pop()?.toUpperCase()}
          </div>
        )}

        {/* Upload Progress Overlay (for future use) */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 rounded-lg transition-opacity duration-200" />
      </div>
    </div>
  );
};

interface PendingAttachmentListProps {
  files: File[];
  onRemoveFile: (index: number) => void;
  layout?: "horizontal" | "grid";
  size?: "small" | "medium";
  maxDisplay?: number;
}

export const PendingAttachmentList: React.FC<PendingAttachmentListProps> = ({
  files,
  onRemoveFile,
  layout = "horizontal",
  size = "medium",
  maxDisplay,
}) => {
  const displayFiles = maxDisplay ? files.slice(0, maxDisplay) : files;
  const remainingCount =
    maxDisplay && files.length > maxDisplay ? files.length - maxDisplay : 0;

  const layoutClasses = {
    horizontal: "flex flex-row gap-2 overflow-x-auto pb-1",
    grid: "grid grid-cols-4 gap-2",
  };

  if (files.length === 0) return null;

  const totalSize = files.reduce((acc, file) => acc + file.size, 0);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-3 shadow-sm">
      {/* Header */}
      <div className="flex justify-between items-center mb-2">
        <div className="text-sm font-medium text-gray-700">
          {files.length} allegat{files.length === 1 ? "o" : "i"} selezionat
          {files.length === 1 ? "o" : "i"}
        </div>
        <div className="text-xs text-gray-500">{formatFileSize(totalSize)}</div>
      </div>

      {/* File List */}
      <div className={layoutClasses[layout]}>
        {displayFiles.map((file, index) => (
          <PendingAttachment
            key={`${file.name}-${file.size}-${index}`}
            file={file}
            onRemove={() => onRemoveFile(index)}
            size={size}
          />
        ))}

        {/* Show remaining count */}
        {remainingCount > 0 && (
          <div
            className={`
            ${size === "small" ? "w-12 h-12" : "w-16 h-16"}
            rounded-lg bg-gray-100 border-2 border-dashed border-gray-300
            flex items-center justify-center
          `}
          >
            <div className="text-center">
              <div className="text-sm">📎</div>
              <div className="text-xs text-gray-600">+{remainingCount}</div>
            </div>
          </div>
        )}
      </div>

      {/* File Type Summary */}
      {files.length > 1 && (
        <div className="mt-2 text-xs text-gray-500">
          {files.filter((f) => isImageFile(f.name, f.type)).length > 0 && (
            <span className="mr-2">
              🖼️ {files.filter((f) => isImageFile(f.name, f.type)).length}{" "}
              immagini
            </span>
          )}
          {files.filter((f) => !isImageFile(f.name, f.type)).length > 0 && (
            <span>
              📄 {files.filter((f) => !isImageFile(f.name, f.type)).length}{" "}
              documenti
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default PendingAttachment;
