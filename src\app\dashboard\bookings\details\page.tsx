"use client";
import React, { useContext, useEffect, useMemo, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import { useRouter } from "next/navigation";
import HeaderPage from "@/components/_globals/headerPage";
import {
  Calendar,
  Clock,
  CreditCard,
  Home,
  MapPin,
  MessageSquare,
  Phone,
  User,
  Users,
  Laptop,
  ClipboardList,
  XCircle,
  LogIn,
  Check,
  Copy,
  UserPlus,
  Shield,
  BarChart3,
} from "lucide-react";
import BookingCancellationModal from "@/components/modals/BookingCancellationModal";
import { PropertyContext } from "@/components/_context/PropertyContext";
import ChekinWidget from "@/components/integrations/ChekinWidget";
import { ChekinStatusPanel, ChekinStatusIndicator } from "@/components/chekin";
import { ApiChekinStatusPanel } from "@/components/chekin/ApiChekinStatusPanel";
import { BookingChekinStatus } from "@/components/chekin/BookingChekinStatus";
import { StatusBadge } from "@/components/status-badge";
import toast from "react-hot-toast";
import { apiClient } from "@/services/apiClient";

const statusConfig = {
  new: {
    color: "#2196F3",
    bgColor: "#E3F2FD",
    icon: "sparkles",
    label: "Nuova",
  },
  modified: {
    color: "#FF9800",
    bgColor: "#FFF3E0",
    icon: "pencil",
    label: "Modificata",
  },
  request: {
    color: "#9C27B0",
    bgColor: "#F3E5F5",
    icon: "clock",
    label: "Richiesta",
  },
  cancelled: {
    color: "#F44336",
    bgColor: "#FFEBEE",
    icon: "x",
    label: "Cancellata",
  },
  completed: {
    color: "#4CAF50",
    bgColor: "#E8F5E9",
    icon: "check",
    label: "Completata",
  },
} as const;

interface PropertyData {
  hotel_id: string;
  name: string;
  is_domorent: boolean;
}

interface Customer {
  first_name: string;
  last_name: string;
  email: string;
  telephone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zip_code: string;
}

interface ReservationData {
  id: string;
  guest_name: string;
  total_price: string;
  net_price: string;
  deposit: string;
  checkin_date: string;
  checkout_date: string;
  number_of_guests: number;
  number_of_adults: number;
  number_of_children: number;
  number_of_infants: number;
  payment_type: string;
  calculation_type?: string; // Added for Domorent detection

  // New API fields - Common
  total_tax?: string;
  booked_at?: string;
  modified_at?: string;

  // Heibooky fields (null for Domorent)
  base_price?: string;
  ota_commission: string | null;
  heibooky_commission: string | null;
  payment_charge: string | null;
  payment_fee?: string;
  liabilities_recovered?: string;
  activation_fee_applied?: string;
  vat_22?: string;
  iva_amount: string | null;
  substitute_tax_21?: string;
  owner_tax: string | null;
  net_total_for_owner: string | null;
  total_owner_payout?: string;
  subtotal_before_substitute_tax?: string;

  // Domorent fields (null for non-Domorent)
  cleaning_cost: string | null;
  taxable_domorent: string | null;
  tot_platform_commission: string | null;
  owner_net_transfer: string | null;
  stamp_duty?: string | null;
  payment_fee_1_5?: number | string | null;
  subtotal_after_deductions?: number | string | null; // TOTAL before Flat Tax
  flat_tax_21?: number | string | null;
  net_total?: number | string | null; // Final NET TOTAL

  // Common fields
  commission_amount: string; // Legacy field
  extra_fees: object;
  taxes: object;
  payment_due: string;
  remarks: string | null;
  addons: object;
}

interface BookingDataType {
  id: string;
  is_manual: boolean;
  status: keyof typeof statusConfig;
  whatsapp_link?: string;
  customer: Customer;
  reservation_data: ReservationData; // authoritative dates source
  checkin_date?: string; // legacy optional
  checkout_date?: string; // legacy optional
  channel_code: number;
  payment_processed: boolean;
  property: string;
  property_data: PropertyData;
  chekin_synced?: boolean;
  chekin_reservation_id?: string | null;
  chekin_signup_form_link?: string | null;
  chekin_links?: {
    signup_form_link?: string;
    upselling_link?: string;
    payment_link?: string;
    checkin_online_link?: string;
    lock_link?: string;
    general_link?: string;
  };
  // New email fields
  chekin_email_sent?: boolean;
  chekin_email_sent_at?: string | null;
  chekin_email_send_count?: number;
  chekin_email_last_send_date?: string | null;
}

const Page = () => {
  const [bookingData, setBookingData] = useState<BookingDataType | null>(null);
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [showChekinWidget, setShowChekinWidget] = useState(false);
  const router = useRouter();
  const { properties } = useContext(PropertyContext);

  const getBookingDetails = () => {
    const booking = localStorage.getItem("selectedBooking-heibooky");
    if (booking) {
      return JSON.parse(booking);
    }
    return null;
  };

  const hydratePropertyData = (raw: any) => {
    if (!raw) return raw;
    const propId = typeof raw.property === "string" ? raw.property : raw.property?.id;
    const prop = properties?.find((p: any) => p?.id === propId);
    const property_data = {
      hotel_id: prop?.hotel_id || prop?.id || raw?.property_data?.hotel_id || "",
      name: prop?.name || raw?.property_data?.name || "",
      is_domorent: Boolean(prop?.is_domorent ?? raw?.property_data?.is_domorent),
    };
    return { ...raw, property_data } as BookingDataType;
  };

  const refreshBookingData = () => {
    const data = getBookingDetails();
    if (data) {
      const hydrated = hydratePropertyData(data);
      setBookingData(hydrated);
      // keep storage updated so reloads preserve hydration
      try {
        localStorage.setItem("selectedBooking-heibooky", JSON.stringify(hydrated));
      } catch {}
    }
  };
  
  // Function to fetch fresh booking data from API
  const fetchFreshBookingData = async () => {
    if (!bookingData?.id) return;
    
    try {
      const response = await apiClient.get(`/booking/${bookingData.id}/`);
      if (response.ok) {
        const freshData = await response.json();
        const hydrated = hydratePropertyData(freshData);
        setBookingData(hydrated);
        
        // Update localStorage with fresh data
        try {
          localStorage.setItem("selectedBooking-heibooky", JSON.stringify(hydrated));
        } catch {}
      }
    } catch (error) {
      console.error('Error fetching fresh booking data:', error);
    }
  };
  
  // Function to navigate back to bookings dashboard
  const handleNavigateToBookings = () => {
    router.push('/dashboard/bookings');
  };

  const handleCancellationSuccess = () => {
    // Refresh the booking data after successful cancellation
    refreshBookingData();
    setShowCancellationModal(false);
  };

  // Pure date helpers (YYYY-MM-DD)
  const parseISODate = (ds: string) => {
    const m = /^([0-9]{4})-([0-9]{2})-([0-9]{2})$/.exec(ds);
    if (!m) return null;
    const [_, y, mo, d] = m;
    return new Date(Date.UTC(Number(y), Number(mo) - 1, Number(d)));
  };
  const calculateNights = (
    checkinDate: string,
    checkoutDate: string,
  ): number => {
    if (!checkinDate || !checkoutDate) return 0;
    const ci = parseISODate(checkinDate);
    const co = parseISODate(checkoutDate);
    if (!ci || !co) return 0;
    const diff = (co.getTime() - ci.getTime()) / 86400000;
    return diff > 0 ? Math.round(diff) : 0;
  };
  const formatPureDate = (dateString?: string): string => {
    if (!dateString) return "-";
    const m = /^([0-9]{4})-([0-9]{2})-([0-9]{2})$/.exec(dateString.trim());
    if (!m) return dateString; // fallback to raw if not pure
    const [, y, mo, d] = m;
    return `${d}/${mo}/${y}`;
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return "-";
    const dateOnlyRegex = /^\d{4}-\d{2}-\d{2}$/;
    let d: Date;
    if (dateOnlyRegex.test(dateString)) {
      // Treat as pure date (no timezone shift)
      const [y, m, day] = dateString.split("-").map(Number);
      d = new Date(Date.UTC(y, m - 1, day));
    } else {
      d = new Date(dateString);
    }
    if (isNaN(d.getTime())) return "-";
    return d.toLocaleDateString("it-IT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Date-only formatter kept for backward compatibility of name, now returns date without time
  const formatDateTime = (dateString?: string | null): string => {
    return dateString ? formatDate(dateString) : "-";
  };

  const renderKeyValueCosts = (obj: any, title: string) => {
    if (!obj || typeof obj !== "object" || Object.keys(obj).length === 0)
      return null;
    return (
      <div className="mt-3">
        <h5 className="font-medium text-[#133157] text-xs uppercase tracking-wide mb-2">
          {title}
        </h5>
        <div className="space-y-1 text-sm">
          {Object.entries(obj).map(([key, val]: [string, any]) => {
            const label =
              (val && typeof val === "object" && (val.label || val.name)) ||
              key.replace(/_/g, " ");
            const raw =
              val && typeof val === "object"
                ? (val.price ?? val.amount ?? val.value)
                : val;
            const num = parseFloat(String(raw));
            if (Number.isNaN(num)) return null;
            const display = Math.abs(num).toFixed(2); // always show positive value
            return (
              <div className="flex justify-between" key={key}>
                <span className="text-gray-600 capitalize">{label}</span>
                <span className="text-[#133157]">€{display}</span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  useEffect(() => {
    const data = getBookingDetails();
    if (data) {
      const hydrated = hydratePropertyData(data);
      setBookingData(hydrated);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [properties]);

  const renderStatusIcon = (status: string) => {
    switch (status) {
      case "new":
        return <div className="w-5 h-5 text-white">✨</div>;
      case "modified":
        return <div className="w-5 h-5 text-white">✏️</div>;
      case "request":
        return <Clock className="w-5 h-5 text-white" />;
      case "cancelled":
        return <div className="w-5 h-5 text-white">❌</div>;
      case "completed":
        return <div className="w-5 h-5 text-white">✓</div>;
      default:
        return null;
    }
  };

  // Function to render Heibooky pricing breakdown
  const renderHeibookyPricing = (reservation: ReservationData) => {
    // Helper function to safely convert to number and format as currency
    const toNumber = (value: any): number => {
      if (value === null || value === undefined || value === "") return 0;
      const num = typeof value === "number" ? value : parseFloat(String(value));
      return isNaN(num) ? 0 : num;
    };

    const formatCurrency = (value: any): string => {
      return `€${toNumber(value).toFixed(2)}`;
    };

    const otaCommission = toNumber(reservation.ota_commission);
    const heibookyCommission = toNumber(reservation.heibooky_commission);
    const paymentFee = toNumber(
      reservation.payment_fee || reservation.payment_charge,
    );
    const activationFee = toNumber(reservation.activation_fee_applied);
    const liabilitiesRecovered = toNumber(reservation.liabilities_recovered);
    const vat = toNumber(reservation.iva_amount || reservation.vat_22);
    const substituteTax = toNumber(
      reservation.substitute_tax_21 || reservation.owner_tax,
    );
    const ownerTotal = toNumber(
      reservation.total_owner_payout || reservation.net_total_for_owner,
    );

    return (
      <div className="space-y-3">
        <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-[#133157] shadow-sm">
          {/* Header with Client Price and Calculation Type */}
          <div className="mb-4 pb-3 border-b border-blue-200">
            <div className="flex justify-between items-center mb-2">
              <span className="text-lg font-bold text-[#133157]">
                Prezzo Cliente:
              </span>
              <span className="text-lg font-bold text-[#133157]">
                €{reservation.net_price}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-600">
                Tipo di Calcolo:
              </span>
              <span className="text-sm font-medium text-[#133157]">
                Heibooky Property
              </span>
            </div>
          </div>

          <h4 className="font-semibold text-[#133157] mb-3">
            Riepilogo Finanziario Heibooky
          </h4>

          {/* Detailed Breakdown */}
          <div className="space-y-2 text-sm mb-4">
            <h5 className="font-medium text-[#133157] text-xs uppercase tracking-wide mb-3">
              Dettaglio Commissioni e Tasse
            </h5>

            {otaCommission > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">• Commissione OTA</span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(otaCommission)}
                </span>
              </div>
            )}

            {heibookyCommission > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">• Commissione Heibooky</span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(heibookyCommission)}
                </span>
              </div>
            )}

            {paymentFee > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">• Costo Pagamento</span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(paymentFee)}
                </span>
              </div>
            )}

            {activationFee > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">
                  • Commissione di Attivazione
                </span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(activationFee)}
                </span>
              </div>
            )}

            {liabilitiesRecovered > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">• Passivo</span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(liabilitiesRecovered)}
                </span>
              </div>
            )}

            {vat > 0 && (
              <div className="flex justify-between items-center py-1">
                <span className="text-gray-600">• IVA (22%)</span>
                <span className="text-gray-800 font-medium">
                  {formatCurrency(vat)}
                </span>
              </div>
            )}

            {/* Subtotal Before Substitute Tax */}
            <div className="border-t border-blue-200 pt-2 mt-4">
              <div className="bg-[#133157] text-white p-2 rounded-lg flex justify-between items-center">
                <span className="font-bold text-lg">Subtotale</span>
                <span className="font-bold text-xl">
                  {formatCurrency(reservation.subtotal_before_substitute_tax)}
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center py-1">
              <span className="text-gray-600">• Imposta Sostitutiva</span>
              <span className="text-gray-800 font-medium">
                {formatCurrency(substituteTax)}
              </span>
            </div>
          </div>

          {/* Final Owner Total */}
          <div className="border-t border-blue-200 pt-3 mt-4">
            <div className="bg-[#133157] text-white p-3 rounded-lg flex justify-between items-center">
              <span className="font-bold text-lg">TOTALE Proprietario:</span>
              <span className="font-bold text-xl">
                {formatCurrency(ownerTotal)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Function to render Domorent pricing breakdown
  const renderDomorentPricing = (reservation: ReservationData) => {
    const isDomorentCalculation =
      reservation.calculation_type === "Domorent Property";
    if (!isDomorentCalculation) return renderHeibookyPricing(reservation);

    const toNumber = (v: any): number | null => {
      if (v === null || v === undefined || v === "") return null;
      const n = typeof v === "number" ? v : parseFloat(String(v));
      return isNaN(n) ? null : n;
    };
    const fmtCurrency = (v: any) => {
      const n = toNumber(v);
      if (n === null) return "—";
      return new Intl.NumberFormat("it-IT", {
        style: "currency",
        currency: "EUR",
      }).format(n);
    };

    const pd: any = (reservation as any).payment_details || {};
    const mapped = {
      paymentType: reservation.payment_type || pd.payment_type,
      calculationType: reservation.calculation_type || pd.calculation_type,
      basePrice: toNumber(reservation.base_price) ?? toNumber(pd.base_price),
      cleaningCost: toNumber(reservation.cleaning_cost) ?? toNumber(pd.cleaning_cost),
      otaCommission:
        toNumber(reservation.ota_commission) ?? toNumber(pd.ota_commission),
      domorentCommission:
        toNumber(reservation.heibooky_commission) ??
        toNumber(pd.domorent_commission),
      paymentFee:
        toNumber(reservation.payment_fee_1_5) ?? toNumber(pd.payment_fee_1_5),
      vat22: toNumber(reservation.vat_22) ?? toNumber(pd.vat_22),
      totalAfterDeductions:
        toNumber(reservation.subtotal_after_deductions) ??
        toNumber(pd.total_new),
      flatTax21:
        toNumber(reservation.flat_tax_21 ?? reservation.owner_tax) ??
        toNumber(pd.flat_tax_21),
      netTotal:
        toNumber(reservation.net_total ?? reservation.owner_net_transfer) ??
        toNumber(pd.net_total),
    };

    if (
      mapped.totalAfterDeductions !== null &&
      mapped.flatTax21 !== null &&
      mapped.netTotal !== null
    ) {
      const expected = mapped.totalAfterDeductions - mapped.flatTax21;
      if (Math.abs(expected - mapped.netTotal) > 0.02) {
        console.warn("Domorent pricing discrepancy", {
          expected,
          netTotal: mapped.netTotal,
          reservation,
        });
      }
    }

    const Row = ({
      label,
      value,
      emphasize,
      highlight,
      isBlue,
    }: {
      label: string;
      value: any;
      emphasize?: boolean;
      highlight?: boolean;
      isBlue?: boolean;
    }) => (
      <div
        className={`flex justify-between items-center py-1 ${highlight ? "bg-[#133157] text-white px-3 rounded-md mt-2" : isBlue ? "bg-blue-500 text-white px-3 rounded-md mt-2" : ""}`}
      >
        <span
          className={`text-sm ${highlight || isBlue ? "font-bold" : "text-gray-600"}`}
        >
          {label}
        </span>
        <span
          className={`${highlight || isBlue ? "font-bold text-lg" : emphasize ? "font-semibold" : "text-gray-800"} text-sm`}
        >
          {value}
        </span>
      </div>
    );

    return (
      <div className="space-y-3">
        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200 shadow-sm">
          <h4 className="font-semibold text-[#133157] mb-3">
            Dettagli Pagamento (Domorent)
          </h4>
          <div className="space-y-1 text-sm">
            <Row label="Tipo Pagamento" value={mapped.paymentType || "—"} />
            <Row label="Tipo Calcolo" value={mapped.calculationType || "—"} />
            <div className="h-px bg-purple-200 my-2" />
            <Row
              label="Prezzo Base"
              value={fmtCurrency(reservation.net_price)}
            />
            {mapped.cleaningCost !== null && (
              <Row
                label="Costo Pulizia"
                value={fmtCurrency(mapped.cleaningCost)}
              />
            )}
            {mapped.otaCommission !== null && (
              <Row
                label="Commissione OTA"
                value={fmtCurrency(mapped.otaCommission)}
              />
            )}
            {mapped.domorentCommission !== null && (
              <Row
                label="Commissione Domorent"
                value={fmtCurrency(mapped.domorentCommission)}
              />
            )}
            {mapped.paymentFee !== null && (
              <Row
                label="Costo Pagamento 1,5%"
                value={fmtCurrency(mapped.paymentFee)}
              />
            )}
            {mapped.vat22 !== null && (
              <Row label="IVA 22%" value={fmtCurrency(mapped.vat22)} />
            )}
            <Row label="Marca da Bollo" value="2,00 €" />
            <div className="h-px bg-purple-200 my-2" />
            <Row
              label="TOTALE"
              value={fmtCurrency(mapped.totalAfterDeductions)}
              emphasize
              highlight
            />
            {mapped.flatTax21 !== null && (
              <Row
                label="Imposta Sostitutiva 21%"
                value={fmtCurrency(mapped.flatTax21)}
              />
            )}
          </div>
        </div>
      </div>
    );
  };

  // Function to render legacy pricing (for manual bookings or old data)
  const renderLegacyPricing = (reservation: ReservationData) => (
    <div className="space-y-3">
      <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400 shadow-sm">
        <h4 className="font-semibold text-[#133157] mb-3">
          Riepilogo Finanziario (Manuale)
        </h4>

        {/* Summary Section */}
        <div className="bg-white p-3 rounded-lg mb-3 border border-gray-100">
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-700">Prezzo Cliente</span>
              <span className="font-medium text-[#133157]">
                €{reservation.net_price}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-700">Totale Proprietario</span>
              <span className="font-medium text-green-600">
                €{reservation.total_price}
              </span>
            </div>
          </div>
        </div>

        <div className="space-y-2 text-sm">
          <h5 className="font-medium text-[#133157] text-xs uppercase tracking-wide mb-2">
            Dettaglio
          </h5>
          {reservation.commission_amount &&
            parseFloat(reservation.commission_amount) > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Commissione Totale</span>
                <span className="text-gray-800">
                  €{reservation.commission_amount}
                </span>
              </div>
            )}
          <div className="border-t border-gray-200 pt-2 mt-3">
            <div className="flex justify-between font-medium">
              <span className="text-gray-700">Calcolo Manuale</span>
              <span className="text-green-600">
                €
                {(
                  parseFloat(reservation.net_price) -
                  parseFloat(reservation.commission_amount)
                ).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Handle WhatsApp chat
  const handleWhatsAppChat = () => {
    if (bookingData?.whatsapp_link) {
      try {
        window.open(bookingData.whatsapp_link, "_blank", "noopener,noreferrer");
      } catch (error) {
        console.error("Error opening WhatsApp link:", error);
        // Fallback: copy to clipboard or show error message
        if (navigator.clipboard) {
          navigator.clipboard.writeText(bookingData.whatsapp_link);
          toast.success("Link WhatsApp copiato negli appunti");
        }
      }
    }
  };

  // Handle copying Chekin links
  const handleCopyLink = async (link: string, linkType: string) => {
    try {
      await navigator.clipboard.writeText(link);
      // You could add a toast notification here instead of alert
      toast.success(`${linkType} copiato negli appunti`);
    } catch (error) {
      console.error("Failed to copy link:", error);
      toast.error("Impossibile copiare il link");
    }
  };

  // Helper function to safely get status config
  const getStatusConfig = (status: string) => {
    if (status && statusConfig[status as keyof typeof statusConfig]) {
      return statusConfig[status as keyof typeof statusConfig];
    }
    // Return default if status is invalid
    return {
      color: "#999",
      bgColor: "#f0f0f0",
      icon: "circle",
      label: "Sconosciuto",
    };
  };

  // Derive Chekin status from booking data
  const getChekinStatus = () => {
    if (!bookingData) return null;
    
    return {
      guest_created: !!bookingData.chekin_synced,
      guest_creation_date: bookingData.chekin_synced ? bookingData.reservation_data.booked_at : undefined,
      police_connected: !!bookingData.chekin_synced, // Assume connected if synced
      police_registration_status: (bookingData.chekin_synced ? 'complete' : 'pending') as 'complete' | 'pending' | 'error',
      stat_connected: !!bookingData.chekin_synced, // Assume connected if synced  
      stat_registration_status: (bookingData.chekin_synced ? 'complete' : 'pending') as 'complete' | 'pending' | 'error',
      has_active_guest: !!bookingData.chekin_synced,
      latest_status_update: bookingData.reservation_data.modified_at || bookingData.reservation_data.booked_at
    };
  };

  // Derive display reservation id (prefix before underscore) with robust fallbacks
  const getDisplayReservationId = (): string => {
    const reservationId = bookingData?.reservation_data?.id;
    if (typeof reservationId === "string" && reservationId.length > 0) {
      const prefix = reservationId.split("_")[0];
      if (prefix && prefix.trim().length >= 4) return prefix.trim();
    }
    if (bookingData?.id) return bookingData.id.slice(0, 8);
    return "N/D";
  };

  return (
    <MobilePageStart isNavbar customPaddingBottom="10px">
      <HeaderPage
        title="Dettagli Prenotazione"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push("/dashboard/bookings")}
      />
      <div className="w-full h-full flex flex-col overflow-hidden bg-gray-50">
        <div className="flex-1 overflow-y-auto p-3 gap-4 space-y-4 pb-20 min-h-0">
          {bookingData && (
            <>
              {/* Status and Booking ID Banner */}
              <div
                className="bg-white rounded-lg p-4 shadow-sm border-l-4"
                style={{ borderLeftColor: "#133157" }}
              >
                <div className="flex justify-between items-center">
                  <div className="flex flex-col">
                    <span className="text-sm text-gray-500">
                      Prenotazione ID
                    </span>
                    <span
                      className="font-bold text-lg text-[#133157]"
                      aria-label="Reservation Reference"
                    >
                      #{getDisplayReservationId()}
                    </span>
                  </div>
                  <div
                    className="flex items-center gap-2 px-4 py-2 rounded-full text-white"
                    style={{
                      backgroundColor: getStatusConfig(bookingData.status)
                        .color,
                    }}
                  >
                    {renderStatusIcon(bookingData.status)}
                    <span className="font-medium">
                      {getStatusConfig(bookingData.status).label}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2 flex-wrap">
                {bookingData.is_manual ? (
                  <div className="flex items-center gap-1 px-3 py-1.5 bg-gray-100 rounded-full text-sm text-gray-700 border">
                    <ClipboardList className="w-4 h-4" />
                    <span className="font-medium">Manuale</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1 px-3 py-1.5 bg-blue-100 rounded-full text-sm text-blue-700 border border-blue-200">
                    <Laptop className="w-4 h-4" />
                    <span className="font-medium">Canale</span>
                  </div>
                )}
              </div>
              {/* Property Card with Check-in/out */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="bg-[#133157] text-white p-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Home className="w-5 h-5" />
                      <h3 className="font-bold">
                        {bookingData.property_data?.name}
                      </h3>
                    </div>
                    {(() => {
                      const ci = bookingData.reservation_data?.checkin_date;
                      const co = bookingData.reservation_data?.checkout_date;
                      if (!ci || !co) return null;
                      const nights = calculateNights(ci, co);
                      return (
                        <div
                          className="flex items-center gap-2 bg-[#febd49] text-[#133157] px-3 py-1 rounded-full text-sm font-semibold"
                          aria-label={`Durata soggiorno: ${nights} nott${nights === 1 ? "e" : "i"}`}
                        >
                          <Calendar className="w-4 h-4" />
                          <span>
                            {nights} nott{nights === 1 ? "e" : "i"}
                          </span>
                        </div>
                      );
                    })()}
                  </div>
                </div>
                <div className="p-4">
                  {(() => {
                    const checkinRaw =
                      bookingData.reservation_data?.checkin_date;
                    const checkoutRaw =
                      bookingData.reservation_data?.checkout_date;
                    if (!checkinRaw || !checkoutRaw) {
                      return (
                        <div className="text-sm text-red-600">
                          Date soggiorno non disponibili
                        </div>
                      );
                    }
                    const checkin = formatPureDate(checkinRaw);
                    const checkout = formatPureDate(checkoutRaw);
                    const nights = calculateNights(checkinRaw, checkoutRaw);
                    return (
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center justify-between gap-3 flex-wrap">
                          <div className="flex flex-col min-w-[140px]">
                            <span className="text-gray-500 text-xs uppercase tracking-wide flex items-center gap-1">
                              <Calendar className="w-3.5 h-3.5 text-[#133157]" />{" "}
                              Check-in
                            </span>
                            <span
                              className="font-medium text-[#133157]"
                              data-testid="checkin-date"
                            >
                              {checkin}
                            </span>
                          </div>
                          <div className="flex flex-col min-w-[140px] text-right">
                            <span className="text-gray-500 text-xs uppercase tracking-wide flex items-center gap-1 justify-end">
                              <Calendar className="w-3.5 h-3.5 text-[#133157]" />{" "}
                              Check-out
                            </span>
                            <span
                              className="font-medium text-[#133157]"
                              data-testid="checkout-date"
                            >
                              {checkout}
                            </span>
                          </div>
                        </div>
                        <div className="h-px bg-gray-100 mt-1" />
                        <div className="text-xs text-gray-500">
                          Permanenza dal{" "}
                          <span className="font-medium text-[#133157]">
                            {checkin}
                          </span>{" "}
                          al{" "}
                          <span className="font-medium text-[#133157]">
                            {checkout}
                          </span>
                          {nights > 0 && (
                            <>
                              {" "}
                              &middot;{" "}
                              <span className="font-medium">
                                {nights} nott{nights === 1 ? "e" : "i"}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>

              {/* Booking Timeline */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <Clock className="w-5 h-5 text-[#133157]" />
                  <h3 className="font-bold text-lg">Cronologia Prenotazione</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <label className="text-gray-500 text-sm">
                      Prenotata il
                    </label>
                    <p className="font-medium mt-1">
                      {formatDateTime(bookingData.reservation_data.booked_at)}
                    </p>
                  </div>
                  {bookingData.status === "modified" &&
                    bookingData.reservation_data.modified_at && (
                      <div className="bg-gray-50 p-3 rounded-lg">
                        <label className="text-gray-500 text-sm">
                          Ultima Modifica
                        </label>
                        <p className="font-medium mt-1">
                          {formatDateTime(
                            bookingData.reservation_data.modified_at,
                          )}
                        </p>
                      </div>
                    )}
                </div>
              </div>

              {/* Customer Details */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <User className="w-5 h-5 text-[#133157]" />
                  <h3 className="font-bold text-lg">Dettagli Cliente</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {bookingData.reservation_data.guest_name && (
                    <div className="flex flex-col">
                      <label className="text-gray-500 text-sm">
                        Ospite Principale
                      </label>
                      <p className="font-medium">
                        {bookingData.reservation_data.guest_name}
                      </p>
                    </div>
                  )}
                  <div className="flex flex-col">
                    <label className="text-gray-500 text-sm">
                      Nome Completo
                    </label>
                    <p className="font-medium">
                      {bookingData.customer.first_name}{" "}
                      {bookingData.customer.last_name}
                    </p>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <Phone className="w-4 h-4 text-[#133157]" />
                      <label className="text-gray-500 text-sm">Telefono</label>
                    </div>
                    <p>{bookingData.customer.telephone}</p>
                  </div>
                  <div className="flex flex-col">
                    <label className="text-gray-500 text-sm">Email</label>
                    <p className="text-[#133157]">
                      {bookingData.customer.email}
                    </p>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4 text-[#133157]" />
                      <label className="text-gray-500 text-sm">Indirizzo</label>
                    </div>
                    <p>
                      {bookingData.customer.address},{" "}
                      {bookingData.customer.city}
                    </p>
                    <p>
                      {bookingData.customer.country}{" "}
                      {bookingData.customer.zip_code}
                    </p>
                  </div>
                </div>
              </div>

              {/* WhatsApp Contact Button */}
              {bookingData.whatsapp_link && (
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                        <img
                          src="/icons/wa.png"
                          alt="WhatsApp"
                          className="w-7 h-7"
                        />
                      </div>
                      <div>
                        <h3 className="font-bold text-lg text-gray-800">
                          Contatta su WhatsApp
                        </h3>
                        <p className="text-sm text-gray-500">
                          Chatta direttamente con{" "}
                          {bookingData.customer.first_name}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={handleWhatsAppChat}
                      className="bg-[#25D366] hover:bg-[#20C157] text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 transition-colors shadow-md hover:shadow-lg"
                    >
                      <img
                        src="/icons/wa.png"
                        alt="WhatsApp"
                        className="w-5 h-5"
                      />
                      <span>Chatta</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Stay Details */}
              <div className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-3">
                  <Users className="w-5 h-5 text-[#133157]" />
                  <h3 className="font-bold text-lg">Dettagli Soggiorno</h3>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <label className="text-gray-500 text-sm">Ospiti</label>
                    <div className="flex gap-2 mt-1">
                      <span className="bg-[#133157] text-white px-2 py-1 rounded text-sm">
                        {bookingData.reservation_data.number_of_adults} Adulti
                      </span>
                      {bookingData.reservation_data.number_of_children > 0 && (
                        <span className="bg-[#febd49] text-[#133157] px-2 py-1 rounded text-sm">
                          {bookingData.reservation_data.number_of_children}{" "}
                          Bambini
                        </span>
                      )}
                      {bookingData.reservation_data.number_of_infants > 0 && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                          {bookingData.reservation_data.number_of_infants}{" "}
                          Neonati
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Details */}
              <div className="bg-white rounded-lg p-4 shadow-sm mb-4">
                <div className="flex items-center gap-2 mb-3">
                  <CreditCard className="w-5 h-5 text-[#133157]" />
                  <h3 className="font-bold text-lg">Dettagli Pagamento</h3>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg mb-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">Tipo Pagamento</span>
                    <span className="font-medium">
                      {bookingData.reservation_data.payment_type}
                    </span>
                  </div>
                </div>

                {/* Basic pricing info - only show for manual bookings or Domorent properties */}
                {(bookingData.is_manual ||
                  (bookingData.property_data?.is_domorent &&
                    bookingData.reservation_data.calculation_type ===
                      "Domorent Property")) && (
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Prezzo Cliente</span>
                      <span className="font-medium">
                        €{bookingData.reservation_data.net_price}
                      </span>
                    </div>

                    {parseFloat(bookingData.reservation_data.deposit) > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">Deposito</span>
                        <span>€{bookingData.reservation_data.deposit}</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Property Type Indicator */}
                {!bookingData.is_manual && (
                  <div className="bg-gray-100 p-3 rounded-lg mb-4 border">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Tipo di Calcolo:
                      </span>
                      <div className="flex items-center gap-2">
                        {bookingData.property_data?.is_domorent ? (
                          <div className="flex items-center gap-1 px-3 py-1 bg-purple-100 rounded-full text-sm text-purple-700 border border-purple-200">
                            <Laptop className="w-4 h-4" />
                            <span className="font-medium">
                              Domorent Property
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 px-3 py-1 bg-blue-100 rounded-full text-sm text-blue-700 border border-blue-200">
                            <Laptop className="w-4 h-4" />
                            <span className="font-medium">
                              Heibooky Property
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Dynamic pricing breakdown based on property type */}
                {(() => {
                  if (!bookingData.is_manual) {
                    const isDomorent =
                      bookingData.property_data?.is_domorent &&
                      bookingData.reservation_data.calculation_type ===
                        "Domorent Property";
                    if (isDomorent) {
                      console.log("Rendering Domorent pricing breakdown");
                      return renderDomorentPricing(
                        bookingData.reservation_data,
                      );
                    }
                    console.log("Rendering Heibooky pricing breakdown");
                    return renderHeibookyPricing(bookingData.reservation_data);
                  }
                  console.log("Rendering Legacy/Manual pricing breakdown");
                  return renderLegacyPricing(bookingData.reservation_data);
                })()}

                {/* Addons and Taxes detailed breakdown if available */}
                {renderKeyValueCosts(
                  bookingData.reservation_data.addons,
                  "Supplementi / Servizi Aggiuntivi",
                )}
                {renderKeyValueCosts(
                  bookingData.reservation_data.taxes,
                  "Tasse",
                )}

                {/* Final total - only show for manual bookings or Domorent properties */}
                {(bookingData.is_manual ||
                  (bookingData.property_data?.is_domorent &&
                    bookingData.reservation_data.calculation_type ===
                      "Domorent Property")) && (
                  <div className="bg-[#133157] text-white p-3 rounded-lg flex justify-between items-center mt-4">
                    <span className="font-bold">Totale Proprietario</span>
                    <span className="font-bold text-xl">
                      €{bookingData.reservation_data.total_price}
                    </span>
                  </div>
                )}
              </div>

              {/* Notes Section */}
              {bookingData.reservation_data.remarks ? (
                <div className="bg-white rounded-lg p-4 shadow-sm mb-4">
                  <div className="flex items-center gap-2 mb-3">
                    <MessageSquare className="w-5 h-5 text-[#133157]" />
                    <h3 className="font-bold text-lg">Note e Richieste</h3>
                  </div>
                  <div className="bg-[#febd49] bg-opacity-10 p-3 rounded-lg border-l-4 border-[#febd49] overflow-hidden">
                    <p className="break-words whitespace-pre-wrap overflow-wrap-anywhere">
                      {bookingData.reservation_data.remarks}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="mb-4"></div>
              )}

              {/* Cancellation Button */}
              {bookingData.status !== "cancelled" &&
                bookingData.status !== "completed" && (
                  <button
                    onClick={() => setShowCancellationModal(true)}
                    className="w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg shadow-sm flex items-center justify-center gap-2 transition-colors mb-4"
                  >
                    <XCircle className="w-5 h-5" />
                    <span className="font-medium">
                      {bookingData.is_manual
                        ? "Annulla prenotazione"
                        : "Richiedi Cancellazione"}
                    </span>
                  </button>
                )}
              {/* Chekin Online Check-in Button */}
              {bookingData && (
                <>
                  {!bookingData.property_data?.is_domorent ? (
                    <div className="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4 shadow-sm mb-4 border border-indigo-100">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center">
                            <LogIn className="w-6 h-6 text-indigo-600" />
                          </div>
                          <div>
                            <h3 className="font-bold text-lg text-gray-800">
                              Check-in Online
                            </h3>
                            <p className="text-sm text-gray-600">
                              Gestisci la registrazione ospiti con Chekin
                            </p>
                            {(!bookingData.chekin_synced || !bookingData.chekin_reservation_id) && (
                              <p className="text-xs text-amber-600 font-medium">
                                ⚠️ Prenotazione non ancora sincronizzata con Chekin
                              </p>
                            )}
                          </div>
                        </div>
                        {bookingData.chekin_synced && bookingData.chekin_reservation_id ? (
                          <button
                            onClick={() => setShowChekinWidget(true)}
                            className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg font-medium flex items-center gap-2 transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                          >
                            <LogIn className="w-5 h-5" />
                            <span>Apri Chekin</span>
                          </button>
                        ) : (
                          <div className="bg-gray-300 text-gray-500 px-6 py-3 rounded-lg font-medium flex items-center gap-2 cursor-not-allowed">
                            <LogIn className="w-5 h-5" />
                            <span>In attesa di sincronizzazione</span>
                          </div>
                        )}
                      </div>

                      {/* Status Indicators - Only show when data is available */}
                      <BookingChekinStatus 
                        bookingId={bookingData.id}
                        propertyId={bookingData.property}
                        bookingData={bookingData}
                        onNavigateToBookings={handleNavigateToBookings}
                        className="mb-4"
                      />

                      <div className="mt-3 pt-3 border-t border-indigo-100">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-600">
                          <div className="flex items-center gap-1">
                            <div className={`w-1.5 h-1.5 rounded-full ${bookingData.chekin_synced ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
                            <span>{bookingData.chekin_synced ? 'Sincronizzato Chekin' : 'Sincronizzazione Chekin'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full"></div>
                            <span>Conformità normativa</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-1.5 h-1.5 bg-purple-400 rounded-full"></div>
                            <span>ID: #{bookingData.chekin_reservation_id ? 'Chekin-' + bookingData.chekin_reservation_id?.substring(0, 8) : bookingData.reservation_data?.id?.split('_')[0] || bookingData.id}</span>
                          </div>
                        </div>

                        {/* Signup Form Link Section - Only show when synced and link is available */}
                        {bookingData.chekin_synced && (bookingData.chekin_signup_form_link || bookingData.chekin_links?.signup_form_link) && (
                          <div className="mt-4 pt-3 border-t border-indigo-100">
                            <div className="flex items-center justify-between bg-indigo-50 rounded-lg p-3">
                              <div className="flex items-center gap-2">
                                <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                                  <User className="w-4 h-4 text-indigo-600" />
                                </div>
                                <div>
                                  <h4 className="text-sm font-medium text-indigo-900">
                                    Link Modulo Registrazione
                                  </h4>
                                  <p className="text-xs text-indigo-600">
                                    Condividi con l'ospite per la registrazione
                                  </p>
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  const signupLink = bookingData.chekin_signup_form_link || bookingData.chekin_links?.signup_form_link;
                                  if (signupLink) {
                                    handleCopyLink(signupLink, "Link modulo registrazione");
                                  }
                                }}
                                className="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-2 rounded-lg text-xs font-medium flex items-center gap-1.5 transition-colors"
                              >
                                <Copy className="w-3.5 h-3.5" />
                                <span>Copia Link</span>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 shadow-sm mb-4 border border-gray-200">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                          <LogIn className="w-6 h-6 text-gray-400" />
                        </div>
                        <div>
                          <h3 className="font-bold text-lg text-gray-600">
                            Check-in Online Non Disponibile
                          </h3>
                          <p className="text-sm text-gray-500">
                            Il check-in online con Chekin non è disponibile per le proprietà Domorent
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </>
          )}
        </div>
      </div>

      {/* Cancellation Modal */}
      {bookingData && (
        <BookingCancellationModal
          isOpen={showCancellationModal}
          onClose={() => setShowCancellationModal(false)}
          bookingId={bookingData.id}
          isManual={bookingData.is_manual}
          onSuccess={handleCancellationSuccess}
        />
      )}

      {/* Chekin Widget Modal */}
      {showChekinWidget && bookingData?.chekin_reservation_id && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl h-[80vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-bold text-[#133157]">Check-in Online</h2>
              <button
                onClick={() => setShowChekinWidget(false)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex-1 overflow-hidden">
              <ChekinWidget
                propertyId={bookingData.property}
                reservationId={bookingData.chekin_reservation_id}
                isReservationDetailsMode={true}
                className="w-full h-full"
              />
            </div>
          </div>
        </div>
      )}
    </MobilePageStart>
  );
};

export default Page;
