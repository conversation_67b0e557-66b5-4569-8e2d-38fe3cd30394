"use client";
import React, { useContext, useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import Button from "@/components/buttons/Button";
import InputCounter from "@/components/inputs/inputCounter";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import { useRouter } from "next/navigation";
import InputSelectLabel from "@/components/inputs/inputSelectLabel";
import {
  roomCreateRoom,
  roomsListAllRoomsProperty,
  roomUpdateRoom,
} from "@/services/api";
import toast from "react-hot-toast";
import Card from "@/components/cards/Card";
import { ChevronRight, Info } from "lucide-react";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import { roomType } from "@/models/modelRoomType";
import Loader1 from "@/components/loaders/Loader1";
import { PropertyContext } from "@/components/_context/PropertyContext";
import Tooltip from "@/components/_globals/tooltip";
import BedTypeSelector from "@/components/inputs/BedTypeSelector";
import { showNavbarSafeToast } from "@/components/toast/NavbarSafeToast";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();
  const { properties } = useContext(PropertyContext);

  const [rooms, setRooms] = useState([]);
  const [roomData, setRoomData] = useState<any>(null);
  const [newRoom, setNewRoom] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isMultiProperty, setIsMultiProperty] = useState(false);

  // Validation state
  const [validationErrors, setValidationErrors] = useState<{
    [key: string]: string;
  }>({});
  const [hasValidationErrors, setHasValidationErrors] = useState(false);

  // Validation functions
  const validateOccupancy = (
    maxOccupancy: number,
    maxChildOccupancy: number,
  ) => {
    if (maxOccupancy < maxChildOccupancy) {
      const errorMessage = `Il numero massimo di ospiti (${maxOccupancy}) deve essere maggiore o uguale al numero massimo di bambini (${maxChildOccupancy})`;
      showNavbarSafeToast({
        message: errorMessage,
        type: "error",
        duration: 6000,
      });
      return false;
    }
    return true;
  };

  const validateRequiredFields = (data: any) => {
    const errors: { [key: string]: string } = {};

    if (!data.room_type) {
      errors.room_type = "Il tipo di stanza è obbligatorio";
    }

    if (!data.room_rate || parseFloat(data.room_rate) <= 0) {
      errors.room_rate = "Il prezzo della stanza deve essere maggiore di 0";
    }

    if (!data.max_occupancy || data.max_occupancy <= 0) {
      errors.max_occupancy =
        "Il numero massimo di ospiti deve essere maggiore di 0";
    }

    if (!data.size_measurement || parseFloat(data.size_measurement) <= 0) {
      errors.size_measurement = "I metri quadrati devono essere maggiori di 0";
    }

    if (!data.description || data.description.trim().length === 0) {
      errors.description = "La descrizione della stanza è obbligatoria";
    }

    // Validate occupancy constraint
    if (
      data.max_occupancy &&
      data.max_child_occupancy &&
      data.max_occupancy < data.max_child_occupancy
    ) {
      errors.occupancy_constraint = `Il numero massimo di ospiti deve essere maggiore o uguale al numero massimo di bambini`;
    }

    setValidationErrors(errors);
    const hasErrors = Object.keys(errors).length > 0;
    setHasValidationErrors(hasErrors);

    if (hasErrors) {
      const errorMessages = Object.values(errors);
      showNavbarSafeToast({
        message: errorMessages.join("\n"),
        type: "error",
        duration: 8000,
      });
    }

    return !hasErrors;
  };

  const clearValidationError = (field: string) => {
    if (validationErrors[field]) {
      const newErrors = { ...validationErrors };
      delete newErrors[field];
      setValidationErrors(newErrors);
      setHasValidationErrors(Object.keys(newErrors).length > 0);
    }
  };

  const handleFetchRoomData = async () => {
    setIsLoading(true);

    // Check if is multiproperty
    const property = properties.find(
      (property: any) => property.id === params.propertyId,
    );
    const is_multi_unit = property?.is_multi_unit ?? false;
    setIsMultiProperty(is_multi_unit);

    try {
      const call = await roomsListAllRoomsProperty(params.propertyId);

      if (!is_multi_unit && call[0]) {
        setRoomData(call[0]);
      }
      if (!is_multi_unit && !call[0]) {
        setNewRoom({
          room_type: "",
          room_rate: 0,
          max_occupancy: 0,
          max_child_occupancy: 0,
          size_measurement: "",
          description: "",
          beds: [],
          bed_config: {},
          quantity: 1,
          bathroom_quantity: 1,
        });
      }

      setRooms(call);
    } catch (error) {
      console.error("Error fetching room data:", error);
      toast.error("Errore nel caricamento delle stanze");
    }

    setIsLoading(false);
  };

  const handleUpdateRoomData = async () => {
    // Validate before submitting
    if (!validateRequiredFields(roomData)) {
      return false;
    }

    // Additional occupancy validation
    if (
      !validateOccupancy(
        parseInt(roomData.max_occupancy),
        parseInt(roomData.max_child_occupancy),
      )
    ) {
      return false;
    }

    try {
      const call = await roomUpdateRoom(
        roomData.id,
        params.propertyId,
        roomData.room_type,
        roomData.room_rate,
        parseInt(roomData.max_occupancy),
        parseInt(roomData.max_child_occupancy),
        roomData.size_measurement,
        roomData.description,
        1, // Fixed quantity to 1
        roomData.bathroom_quantity || 1,
        roomData.bed_config,
      );

      if (call.status !== 200) {
        console.error("Error response:", call);
        showNavbarSafeToast({
          message: "Errore nel caricamento dei dati della stanza",
          type: "error",
        });
        return false;
      } else {
        showNavbarSafeToast({
          message: "Dati della stanza aggiornati con successo",
          type: "success",
        });
        // Navigate back to property overview
        router.push(`/dashboard/property/${params.propertyId}`);
        return true;
      }
    } catch (error) {
      console.error("Exception in handleUpdateRoomData:", error);
      showNavbarSafeToast({
        message: "Si è verificato un errore durante l'aggiornamento",
        type: "error",
      });
      return false;
    }
  };

  const handleCreateNewRoom = async () => {
    // Validate before submitting
    if (!validateRequiredFields(newRoom)) {
      return false;
    }

    // Additional occupancy validation
    if (
      !validateOccupancy(
        newRoom.max_occupancy,
        newRoom.max_child_occupancy || 0,
      )
    ) {
      return false;
    }

    try {
      const call = await roomCreateRoom(
        params.propertyId,
        newRoom.room_type.toString(),
        newRoom.room_rate,
        newRoom.max_occupancy,
        newRoom.max_child_occupancy || 0,
        newRoom.size_measurement,
        "sqm",
        newRoom.description,
        1, // Fixed quantity to 1
        newRoom.bathroom_quantity || 1,
        newRoom.bed_config,
      );

      if (call.status === 201) {
        setNewRoom(null);
        showNavbarSafeToast({
          message: "Stanza creata con successo",
          type: "success",
        });
        // Navigate back to property overview
        router.push(`/dashboard/property/${params.propertyId}`);
        return true;
      } else {
        console.error("Error response:", call);
        showNavbarSafeToast({
          message: "Errore nella creazione della stanza",
          type: "error",
        });
        return false;
      }
    } catch (error) {
      console.error("Exception in handleCreateNewRoom:", error);
      showNavbarSafeToast({
        message: "Si è verificato un errore durante la creazione",
        type: "error",
      });
      return false;
    }
  };

  const handleBackNavigation = () => {
    if (!isMultiProperty) {
      router.back();
      return;
    }

    if (newRoom) {
      setNewRoom(null);
      return;
    }

    if (roomData) {
      setRoomData(null);
      return;
    }
    router.back();
  };

  useEffect(() => {
    handleFetchRoomData();
  }, []);

  return (
    <MobilePageStart>
      <HeaderPage
        title={!roomData ? "Lista Stanze" : "Dettagli stanza"}
        actionLeftIcon={handleBackNavigation}
        actionRightIcon={() => router.push(`/dashboard`)}
      />

      <div className="flex justify-center w-full">
        {isLoading ? (
          <div className="flex items-center justify-center h-[400px] w-full">
            <Loader1 />
          </div>
        ) : (
          <div className="w-full max-w-md px-4">
            {!newRoom && !roomData ? (
              <div className="mt-4 flex flex-col gap-6">
                {rooms && rooms.length > 0 && (
                  <div className="flex flex-col gap-3 w-full">
                    {rooms.map((room, index: number) => (
                      <Card
                        key={index}
                        style={{
                          width: "100%",
                          padding: "20px 10px",
                          display: "flex",
                          alignItems: "flex-start",
                          justifyContent: "space-between",
                          flexDirection: "row",
                        }}
                        onClick={() => {
                          setRoomData(room);
                        }}
                      >
                        <p>Stanza {index + 1}</p>
                        <ChevronRight width={20} />
                      </Card>
                    ))}
                  </div>
                )}

                {!isMultiProperty && rooms.length === 0 && (
                  <Button
                    text="Crea Stanza"
                    color="white"
                    backgroundColor="var(--accent)"
                    onClick={() => {
                      setNewRoom({
                        room_type: "",
                        room_rate: 0,
                        max_occupancy: 0,
                        max_child_occupancy: 0,
                        size_measurement: "",
                        description: "",
                        beds: [],
                        bed_config: {},
                        quantity: 1,
                        bathroom_quantity: 1,
                      });
                    }}
                  />
                )}

                {isMultiProperty && (
                  <Button
                    text={
                      rooms.length === 0
                        ? "Crea prima stanza"
                        : "Crea stanza addizionale"
                    }
                    color="white"
                    backgroundColor="var(--accent)"
                    onClick={() => {
                      setNewRoom({
                        room_type: "",
                        room_rate: 0,
                        max_occupancy: 0,
                        max_child_occupancy: 0,
                        size_measurement: "",
                        description: "",
                        beds: [],
                        bed_config: {},
                        quantity: 1,
                        bathroom_quantity: 1,
                      });
                    }}
                  />
                )}
              </div>
            ) : !newRoom && roomData ? (
              <div className="mt-4 flex flex-col gap-6 w-full">
                <div className="text-center mb-2">
                  <p className="text-base font-medium">Configurazione Stanza</p>
                  <p className="text-sm text-gray-500">
                    Per favore assicurarti che le informazioni sottostanti siano
                    accurate
                  </p>
                </div>

                <div className="flex flex-col gap-5 w-full">
                  <InputSelectLabel
                    label="Tipo Stanza"
                    placeholder="Seleziona"
                    onSelect={(value) => {
                      clearValidationError("room_type");
                      setRoomData({
                        ...roomData,
                        room_type: value,
                      });
                    }}
                    value={
                      roomType.find(
                        (value) => value.value === roomData.room_type,
                      )?.label ?? ""
                    }
                    options={roomType}
                  />
                  {validationErrors.room_type && (
                    <p className="text-red-500 text-xs mt-1 font-medium">
                      {validationErrors.room_type}
                    </p>
                  )}

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-1">
                      <label className="text-sm font-medium">
                        Prezzo Stanza
                      </label>
                      <div
                        className="relative cursor-pointer"
                        onMouseEnter={() => setShowTooltip(true)}
                        onMouseLeave={() => setShowTooltip(false)}
                        onClick={() => setShowTooltip(!showTooltip)}
                      >
                        <Info className="w-4 h-4 text-gray-500" />
                        {showTooltip && (
                          <Tooltip text="Il prezzo giornaliero standard che apparirà sul calendario." />
                        )}
                      </div>
                    </div>
                    <InputLabelDescription
                      placeholder="(€)"
                      value={roomData.room_rate}
                      error={validationErrors.room_rate}
                      onChange={(value) => {
                        if (/^\d*\.?\d*$/.test(value)) {
                          clearValidationError("room_rate");
                          setRoomData({
                            ...roomData,
                            room_rate: value,
                          });
                        }
                      }}
                    />
                  </div>
                  <div className="flex flex-row justify-between text-[12px]">
                    <p>
                      Utile netto previsto (potrebbe essere applicata l'IVA).
                    </p>
                    <p className="text-red-700">
                      {" "}
                      ~ {(roomData.room_rate * 0.73).toFixed(2)}€
                    </p>
                  </div>

                  <InputCounter
                    label="Numero Massimo Ospiti"
                    value={Number(roomData.max_occupancy)}
                    minimum={1}
                    error={
                      validationErrors.max_occupancy ||
                      validationErrors.occupancy_constraint
                    }
                    hasError={
                      !!validationErrors.max_occupancy ||
                      !!validationErrors.occupancy_constraint
                    }
                    onChange={(value) => {
                      clearValidationError("max_occupancy");
                      clearValidationError("occupancy_constraint");

                      if (value === 1) {
                        setRoomData({
                          ...roomData,
                          max_occupancy: value,
                          max_child_occupancy: 0,
                        });
                      } else {
                        // Auto-adjust max_child_occupancy if it would exceed the new max_occupancy
                        if (
                          roomData.max_child_occupancy &&
                          value <= roomData.max_child_occupancy
                        ) {
                          const newMaxChildOccupancy = Math.max(0, value - 1);
                          setRoomData({
                            ...roomData,
                            max_occupancy: value,
                            max_child_occupancy: newMaxChildOccupancy,
                          });

                          if (
                            newMaxChildOccupancy !==
                            roomData.max_child_occupancy
                          ) {
                            showNavbarSafeToast({
                              message: `Numero massimo bambini ridotto automaticamente a ${newMaxChildOccupancy} per rispettare il limite di ${value} ospiti`,
                              type: "info",
                              duration: 4000,
                            });
                          }
                        } else {
                          setRoomData({
                            ...roomData,
                            max_occupancy: value,
                          });
                        }
                      }
                    }}
                  />

                  {Number(roomData.max_occupancy) > 1 && (
                    <InputCounter
                      label="Numero Massimo Bambini"
                      value={Number(roomData.max_child_occupancy)}
                      error={
                        validationErrors.max_child_occupancy ||
                        validationErrors.occupancy_constraint
                      }
                      hasError={
                        !!validationErrors.max_child_occupancy ||
                        !!validationErrors.occupancy_constraint
                      }
                      onChange={(value) => {
                        clearValidationError("max_child_occupancy");
                        clearValidationError("occupancy_constraint");

                        // Auto-adjust max_occupancy if needed
                        if (value >= roomData.max_occupancy) {
                          const newMaxOccupancy = value + 1;
                          setRoomData({
                            ...roomData,
                            max_occupancy: newMaxOccupancy,
                            max_child_occupancy: value,
                          });

                          showNavbarSafeToast({
                            message: `Numero massimo ospiti aumentato automaticamente a ${newMaxOccupancy} per permettere ${value} bambini`,
                            type: "info",
                            duration: 4000,
                          });
                        } else {
                          setRoomData({
                            ...roomData,
                            max_child_occupancy: value,
                          });
                        }
                      }}
                    />
                  )}

                  <InputLabelDescription
                    label="Metri Quadrati"
                    placeholder="Es. 80"
                    value={roomData.size_measurement}
                    error={validationErrors.size_measurement}
                    onChange={(value) => {
                      if (/^\d*\.?\d*$/.test(value)) {
                        clearValidationError("size_measurement");
                        setRoomData({
                          ...roomData,
                          size_measurement: value,
                        });
                      }
                    }}
                  />

                  <InputLabelDescription
                    label="Descrizione Stanza"
                    placeholder="Raccontaci qualcosa di più sulla stanza"
                    isTextArea
                    value={roomData.description}
                    error={validationErrors.description}
                    onChange={(value) => {
                      clearValidationError("description");
                      setRoomData({
                        ...roomData,
                        description: value,
                      });
                    }}
                  />

                  <BedTypeSelector
                    bed_config={roomData.bed_config || {}}
                    bathroom_quantity={roomData.bathroom_quantity || 1}
                    roomType={roomData.room_type}
                    onChange={(bed_config, _quantity, bathroom_quantity) => {
                      setRoomData({
                        ...roomData,
                        bed_config,
                        bathroom_quantity,
                      });
                    }}
                  />
                </div>

                <div className="mt-4 mb-8">
                  <ButtonLoading
                    color="white"
                    backgroundColor={
                      hasValidationErrors ? "#9ca3af" : "var(--blue)"
                    }
                    text="Salva"
                    onClick={
                      hasValidationErrors
                        ? () => {
                            showNavbarSafeToast({
                              message: "Correggi gli errori prima di salvare",
                              type: "error",
                              duration: 4000,
                            });
                            return Promise.resolve();
                          }
                        : handleUpdateRoomData
                    }
                    deactive={hasValidationErrors}
                  />
                </div>
              </div>
            ) : (
              <div className="mt-4 flex flex-col gap-6 w-full">
                <div className="text-center mb-2">
                  <p className="text-base font-medium">Nuova Stanza</p>
                  <p className="text-sm text-gray-500">
                    Per favore aassicurarti che le informazioni sottostanti
                    siano accurate
                  </p>
                </div>

                <div className="flex flex-col gap-5 w-full">
                  <InputSelectLabel
                    label="Tipo Stanza"
                    placeholder="Seleziona"
                    onSelect={(value) => {
                      clearValidationError("room_type");
                      setNewRoom({
                        ...newRoom,
                        room_type: value,
                      });
                    }}
                    value={
                      roomType.find(
                        (value) => value.value === newRoom.room_type,
                      )?.label ?? ""
                    }
                    options={roomType}
                  />
                  {validationErrors.room_type && (
                    <p className="text-red-500 text-xs mt-1 font-medium">
                      {validationErrors.room_type}
                    </p>
                  )}

                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-1">
                      <label className="text-sm font-medium">
                        Prezzo Stanza
                      </label>
                      <div
                        className="relative cursor-pointer"
                        onMouseEnter={() => setShowTooltip(true)}
                        onMouseLeave={() => setShowTooltip(false)}
                        onClick={() => setShowTooltip(!showTooltip)}
                      >
                        <Info className="w-4 h-4 text-gray-500" />
                        {showTooltip && (
                          <Tooltip text="Il prezzo giornaliero standard che apparirà sul calendario." />
                        )}
                      </div>
                    </div>
                    <InputLabelDescription
                      placeholder="(€)"
                      value={newRoom.room_rate}
                      error={validationErrors.room_rate}
                      onChange={(value) => {
                        if (/^\d*\.?\d*$/.test(value)) {
                          clearValidationError("room_rate");
                          const numValue = parseFloat(value) || 0;
                          setNewRoom({
                            ...newRoom,
                            room_rate: value,
                            guest_price: (numValue * 0.73).toFixed(2),
                          });
                        }
                      }}
                    />
                  </div>
                  <div className="flex flex-row justify-between text-[12px]">
                    <p>
                      Utile netto previsto (potrebbe essere applicata l'IVA).
                    </p>
                    <p className="text-red-700">
                      {" "}
                      ~ {newRoom.guest_price || "0.00"}€
                    </p>
                  </div>

                  <InputCounter
                    label="Numero Massimo Ospiti"
                    value={Number(newRoom.max_occupancy)}
                    minimum={1}
                    error={
                      validationErrors.max_occupancy ||
                      validationErrors.occupancy_constraint
                    }
                    hasError={
                      !!validationErrors.max_occupancy ||
                      !!validationErrors.occupancy_constraint
                    }
                    onChange={(value) => {
                      clearValidationError("max_occupancy");
                      clearValidationError("occupancy_constraint");

                      if (value === 1) {
                        setNewRoom({
                          ...newRoom,
                          max_occupancy: value,
                          max_child_occupancy: 0,
                        });
                      } else {
                        // Auto-adjust max_child_occupancy if it would exceed the new max_occupancy
                        if (
                          newRoom.max_child_occupancy &&
                          value <= newRoom.max_child_occupancy
                        ) {
                          const newMaxChildOccupancy = Math.max(0, value - 1);
                          setNewRoom({
                            ...newRoom,
                            max_occupancy: value,
                            max_child_occupancy: newMaxChildOccupancy,
                          });

                          if (
                            newMaxChildOccupancy !== newRoom.max_child_occupancy
                          ) {
                            showNavbarSafeToast({
                              message: `Numero massimo bambini ridotto automaticamente a ${newMaxChildOccupancy} per rispettare il limite di ${value} ospiti`,
                              type: "info",
                              duration: 4000,
                            });
                          }
                        } else {
                          setNewRoom({
                            ...newRoom,
                            max_occupancy: value,
                          });
                        }
                      }
                    }}
                  />

                  {Number(newRoom.max_occupancy) > 1 && (
                    <InputCounter
                      label="Numero Massimo Bambini"
                      value={Number(newRoom.max_child_occupancy)}
                      error={
                        validationErrors.max_child_occupancy ||
                        validationErrors.occupancy_constraint
                      }
                      hasError={
                        !!validationErrors.max_child_occupancy ||
                        !!validationErrors.occupancy_constraint
                      }
                      onChange={(value) => {
                        clearValidationError("max_child_occupancy");
                        clearValidationError("occupancy_constraint");

                        // Auto-adjust max_occupancy if needed
                        if (value >= newRoom.max_occupancy) {
                          const newMaxOccupancy = value + 1;
                          setNewRoom({
                            ...newRoom,
                            max_occupancy: newMaxOccupancy,
                            max_child_occupancy: value,
                          });

                          showNavbarSafeToast({
                            message: `Numero massimo ospiti aumentato automaticamente a ${newMaxOccupancy} per permettere ${value} bambini`,
                            type: "info",
                            duration: 4000,
                          });
                        } else {
                          setNewRoom({
                            ...newRoom,
                            max_child_occupancy: value,
                          });
                        }
                      }}
                    />
                  )}

                  <InputLabelDescription
                    label="Metri Quadrati"
                    placeholder="Es. 80"
                    value={newRoom.size_measurement}
                    error={validationErrors.size_measurement}
                    onChange={(value) => {
                      if (/^\d*\.?\d*$/.test(value)) {
                        clearValidationError("size_measurement");
                        setNewRoom({
                          ...newRoom,
                          size_measurement: value,
                        });
                      }
                    }}
                  />

                  <InputLabelDescription
                    label="Descrizione Stanza"
                    placeholder="Raccontaci qualcosa di più sulla stanza"
                    isTextArea
                    value={newRoom.description}
                    error={validationErrors.description}
                    onChange={(value) => {
                      clearValidationError("description");
                      setNewRoom({
                        ...newRoom,
                        description: value,
                      });
                    }}
                  />

                  <BedTypeSelector
                    bed_config={newRoom.bed_config || {}}
                    bathroom_quantity={newRoom.bathroom_quantity || 1}
                    roomType={newRoom.room_type}
                    onChange={(bed_config, _quantity, bathroom_quantity) => {
                      setNewRoom({
                        ...newRoom,
                        bed_config,
                        quantity: 1, // Always set quantity to 1
                        bathroom_quantity,
                      });
                    }}
                  />
                </div>

                <div className="mt-4 mb-8">
                  <ButtonLoading
                    color="white"
                    backgroundColor={
                      hasValidationErrors ? "#9ca3af" : "var(--blue)"
                    }
                    text="Salva"
                    onClick={
                      hasValidationErrors
                        ? () => {
                            showNavbarSafeToast({
                              message: "Correggi gli errori prima di salvare",
                              type: "error",
                              duration: 4000,
                            });
                            return Promise.resolve();
                          }
                        : handleCreateNewRoom
                    }
                    deactive={hasValidationErrors}
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </MobilePageStart>
  );
}

export default Page;
