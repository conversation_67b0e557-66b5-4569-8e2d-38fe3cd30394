"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = React.useState({
    sink: false,
    cooktop: false,
    oven: false,
    freezer: false,
    dishes: false,
    kettle: false,
    coffee: false,
    toaster: false,
    detergent: false,
    mixer: false,
    cloth: false,
    water: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: "Sink",
        category: "Kitchen",
        is_available: data.sink ?? false,
      },
      {
        name: "Cooktop",
        category: "Kitchen",
        is_available: data.cooktop ?? false,
      },
      {
        name: "Oven",
        category: "Kitchen",
        is_available: data.oven ?? false,
      },
      {
        name: "Freezer",
        category: "Kitchen",
        is_available: data.freezer ?? false,
      },
      {
        name: "Dishes",
        category: "Kitchen",
        is_available: data.dishes ?? false,
      },
      {
        name: "Kettle",
        category: "Kitchen",
        is_available: data.kettle ?? false,
      },
      {
        name: "Coffee",
        category: "Kitchen",
        is_available: data.coffee ?? false,
      },
      {
        name: "Toaster",
        category: "Kitchen",
        is_available: data.toaster ?? false,
      },
      {
        name: "Detergent",
        category: "Kitchen",
        is_available: data.detergent ?? false,
      },
      {
        name: "Mixer",
        category: "Kitchen",
        is_available: data.mixer ?? false,
      },
      {
        name: "Cloth",
        category: "Kitchen",
        is_available: data.cloth ?? false,
      },
      {
        name: "Water",
        category: "Kitchen",
        is_available: data.water ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };
  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;

    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Cucina"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Lavandino"
            value={data.sink}
            onChange={(value) => {
              setData({
                ...data,
                sink: value,
              });
              handleUpdateAmenities({
                ...data,
                sink: value,
              });
            }}
          />

          <InputOnOff
            title="Piano Cottura"
            value={data.cooktop}
            onChange={(value) => {
              setData({
                ...data,
                cooktop: value,
              });
              handleUpdateAmenities({
                ...data,
                cooktop: value,
              });
            }}
          />

          <InputOnOff
            title="Forno"
            value={data.oven}
            onChange={(value) => {
              setData({
                ...data,
                oven: value,
              });
              handleUpdateAmenities({
                ...data,
                oven: value,
              });
            }}
          />

          <InputOnOff
            title="Freezer"
            value={data.freezer}
            onChange={(value) => {
              setData({
                ...data,
                freezer: value,
              });
              handleUpdateAmenities({
                ...data,
                freezer: value,
              });
            }}
          />

          <InputOnOff
            title="Piatti"
            value={data.dishes}
            onChange={(value) => {
              setData({
                ...data,
                dishes: value,
              });
              handleUpdateAmenities({
                ...data,
                dishes: value,
              });
            }}
          />

          <InputOnOff
            title="Teiera"
            value={data.kettle}
            onChange={(value) => {
              setData({
                ...data,
                kettle: value,
              });
              handleUpdateAmenities({
                ...data,
                kettle: value,
              });
            }}
          />

          <InputOnOff
            title="Caffè"
            value={data.coffee}
            onChange={(value) => {
              setData({
                ...data,
                coffee: value,
              });
              handleUpdateAmenities({
                ...data,
                coffee: value,
              });
            }}
          />

          <InputOnOff
            title="Tostapane"
            value={data.toaster}
            onChange={(value) => {
              setData({
                ...data,
                toaster: value,
              });
              handleUpdateAmenities({
                ...data,
                toaster: value,
              });
            }}
          />

          <InputOnOff
            title="Detergente"
            description="Per tutta la durata del soggiorno"
            value={data.detergent}
            onChange={(value) => {
              setData({
                ...data,
                detergent: value,
              });
              handleUpdateAmenities({
                ...data,
                detergent: value,
              });
            }}
          />

          <InputOnOff
            title="Frullatore"
            value={data.mixer}
            onChange={(value) => {
              setData({
                ...data,
                mixer: value,
              });
              handleUpdateAmenities({
                ...data,
                mixer: value,
              });
            }}
          />

          <InputOnOff
            title="Stracci"
            value={data.cloth}
            onChange={(value) => {
              setData({
                ...data,
                cloth: value,
              });
              handleUpdateAmenities({
                ...data,
                cloth: value,
              });
            }}
          />

          <InputOnOff
            title="Acqua"
            value={data.water}
            onChange={(value) => {
              setData({
                ...data,
                water: value,
              });
              handleUpdateAmenities({
                ...data,
                water: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
