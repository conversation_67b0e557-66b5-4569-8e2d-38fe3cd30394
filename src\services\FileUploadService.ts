import { SupportChatWebSocket } from "./SupportChatWebSocket";

export interface FileUploadProgress {
  fileName: string;
  progress: number;
  status: "pending" | "uploading" | "completed" | "error";
  messageId?: string;
}

export interface FileUploadServiceProps {
  chatId: string;
  webSocketService?: SupportChatWebSocket;
  onProgressUpdate?: (progress: FileUploadProgress) => void;
  onUploadComplete?: (fileId: string, messageId: string, url: string) => void;
  onError?: (error: string, fileName: string) => void;
}

export class FileUploadService {
  private _chatId: string;
  private webSocketService: SupportChatWebSocket | undefined;
  private onProgressUpdate:
    | ((progress: FileUploadProgress) => void)
    | undefined;
  private onUploadComplete:
    | ((fileId: string, messageId: string, url: string) => void)
    | undefined;
  private onError: ((error: string, fileName: string) => void) | undefined;
  private activeUploads: Map<string, FileUploadProgress> = new Map();

  // Expose chatId as public getter for external components
  get chatId(): string {
    return this._chatId;
  }

  constructor(props: FileUploadServiceProps) {
    this._chatId = props.chatId;
    this.webSocketService = props.webSocketService;
    this.onProgressUpdate = props.onProgressUpdate;
    this.onUploadComplete = props.onUploadComplete;
    this.onError = props.onError;

    // Setup WebSocket handlers for file upload progress if available
    if (this.webSocketService) {
      this.setupWebSocketHandlers();
    }
  }

  private setupWebSocketHandlers() {
    if (!this.webSocketService) return;

    // Handle file upload progress notifications from WebSocket
    this.webSocketService.onUploadProgress = (messageId, progressPercent) => {
      // Find uploads associated with this message ID
      const entries = Array.from(this.activeUploads.entries());
      const uploadEntries = entries.filter(
        ([_, progress]) => progress.messageId === messageId,
      );

      if (uploadEntries.length > 0) {
        // Update all associated uploads with the progress
        uploadEntries.forEach(([uploadId, progress]) => {
          const updatedProgress: FileUploadProgress = {
            ...progress,
            progress: progressPercent,
            status: progressPercent >= 100 ? "completed" : "uploading",
          };

          this.activeUploads.set(uploadId, updatedProgress);
          this.onProgressUpdate?.(updatedProgress);

          // Clean up completed uploads after a delay
          if (progressPercent >= 100) {
            setTimeout(() => {
              this.activeUploads.delete(uploadId);
            }, 5000);
          }
        });
      }
    };

    // Handle file upload completion notifications from WebSocket
    this.webSocketService.onFileUploaded = (messageId, attachments) => {
      // Find uploads associated with this message ID
      const entries = Array.from(this.activeUploads.entries());
      const uploadEntries = entries.filter(
        ([_, progress]) => progress.messageId === messageId,
      );

      if (uploadEntries.length > 0 && attachments.length > 0) {
        // Match uploads with attachments by filename if possible
        uploadEntries.forEach(([uploadId, progress]) => {
          // Find a matching attachment by filename
          const attachment = attachments.find(
            (att) => att.file_name === progress.fileName,
          );

          if (attachment) {
            // Mark as completed
            const completedProgress: FileUploadProgress = {
              ...progress,
              progress: 100,
              status: "completed",
            };

            this.activeUploads.set(uploadId, completedProgress);
            this.onProgressUpdate?.(completedProgress);

            // Notify of completion
            this.onUploadComplete?.(
              attachment.id,
              messageId,
              attachment.file_url,
            );

            // Clean up after a delay
            setTimeout(() => {
              this.activeUploads.delete(uploadId);
            }, 5000);
          }
        });
      }
    };
  }

  public async uploadFile(file: File, messageId?: string): Promise<string> {
    // Create a unique ID for this upload
    const uploadId = `upload-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Initialize upload tracking
    const progress: FileUploadProgress = {
      fileName: file.name,
      progress: 0,
      status: "pending",
      messageId,
    };

    this.activeUploads.set(uploadId, progress);
    this.onProgressUpdate?.(progress);

    try {
      // Create FormData for the upload
      const formData = new FormData();
      formData.append("file", file);

      if (messageId) {
        formData.append("message_id", messageId);
      }

      formData.append("chat_id", this._chatId);

      // Start upload and track progress with XHR
      return await this.performUpload(uploadId, formData);
    } catch (error) {
      // Update status to error
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error during upload";
      this.activeUploads.set(uploadId, { ...progress, status: "error" });
      this.onError?.(errorMessage, file.name);

      throw error;
    }
  }

  private performUpload(uploadId: string, formData: FormData): Promise<string> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const progress = this.activeUploads.get(uploadId);

      if (!progress) {
        reject(new Error("Upload not found"));
        return;
      }

      // Update progress as the upload proceeds
      xhr.upload.addEventListener("progress", (event) => {
        if (event.lengthComputable) {
          const progressPercent = Math.round(
            (event.loaded / event.total) * 100,
          );
          const updatedProgress: FileUploadProgress = {
            ...progress,
            progress: progressPercent,
            status: "uploading",
          };

          this.activeUploads.set(uploadId, updatedProgress);
          this.onProgressUpdate?.(updatedProgress);

          // If WebSocket is available, could notify through WebSocket as well
          // This would allow other clients to see the upload progress
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          // Upload completed successfully
          try {
            const response = JSON.parse(xhr.responseText);

            const completedProgress: FileUploadProgress = {
              ...progress,
              progress: 100,
              status: "completed",
            };

            this.activeUploads.set(uploadId, completedProgress);
            this.onProgressUpdate?.(completedProgress);

            // Clean up after a delay
            setTimeout(() => {
              this.activeUploads.delete(uploadId);
            }, 5000);

            // Call complete callback with file info from response
            if (response.file_id && response.message_id && response.file_url) {
              this.onUploadComplete?.(
                response.file_id,
                response.message_id,
                response.file_url,
              );
              resolve(response.file_id);
            } else {
              reject(new Error("Invalid server response"));
            }
          } catch (error) {
            reject(new Error("Invalid server response format"));
          }
        } else {
          // Handle HTTP error
          const errorProgress: FileUploadProgress = {
            ...progress,
            status: "error",
          };

          this.activeUploads.set(uploadId, errorProgress);
          this.onProgressUpdate?.(errorProgress);

          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener("error", () => {
        const errorProgress: FileUploadProgress = {
          ...progress,
          status: "error",
        };

        this.activeUploads.set(uploadId, errorProgress);
        this.onProgressUpdate?.(errorProgress);

        reject(new Error("Network error during upload"));
      });

      xhr.addEventListener("abort", () => {
        const errorProgress: FileUploadProgress = {
          ...progress,
          status: "error",
        };

        this.activeUploads.set(uploadId, errorProgress);
        this.onProgressUpdate?.(errorProgress);

        reject(new Error("Upload aborted"));
      });

      // Open and send the request
      const uploadUrl = `${window.location.origin}/api/chat/attachments/upload`;
      xhr.open("POST", uploadUrl, true);
      xhr.send(formData);
    });
  }

  public getActiveUploads(): FileUploadProgress[] {
    return Array.from(this.activeUploads.values());
  }

  public cancelUpload(fileName: string): boolean {
    // Find the upload by file name
    const entries = Array.from(this.activeUploads.entries());
    const uploadEntry = entries.find(
      ([_, progress]) => progress.fileName === fileName,
    );

    if (uploadEntry) {
      const [uploadId, progress] = uploadEntry;

      // Mark as error/cancelled
      const cancelledProgress: FileUploadProgress = {
        ...progress,
        status: "error",
        progress: 0,
      };

      this.activeUploads.set(uploadId, cancelledProgress);
      this.onProgressUpdate?.(cancelledProgress);

      // Clean up after a delay
      setTimeout(() => {
        this.activeUploads.delete(uploadId);
      }, 1000);

      return true;
    }

    return false;
  }

  public updateChatId(chatId: string) {
    this._chatId = chatId;
  }

  // Add cleanup method to properly handle resource cleanup
  public cleanup(): void {
    // Clear all active uploads
    this.activeUploads.clear();

    // Remove WebSocket handlers if they were set
    if (this.webSocketService) {
      this.webSocketService.onUploadProgress = undefined;
      this.webSocketService.onFileUploaded = undefined;
    }

    // Clear callbacks
    this.onProgressUpdate = undefined;
    this.onUploadComplete = undefined;
    this.onError = undefined;
  }
}
