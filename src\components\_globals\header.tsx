import React, { useContext } from "react";
import styles from "./globals.module.css";
import { AuthContext } from "../_context/AuthContext";

interface HeaderProps {
  children: React.ReactNode;
}

function Header({ children }: HeaderProps) {
  const { setIsSidepanelOpen, isSidepanelOpen } = useContext(AuthContext);

  const toggle = () => {
    setIsSidepanelOpen(!isSidepanelOpen);
  };

  return <section className={styles.header}>{children}</section>;
}

export default Header;
