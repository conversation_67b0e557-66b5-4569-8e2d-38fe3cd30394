import React from "react";
import Modal from "@/components/_globals/modal";
import Title from "@/components/titles/Title";
import Button from "@/components/buttons/Button";
import { Lock, DollarSign, Calendar } from "lucide-react";
import { useRouter } from "next/navigation";

interface BookingBlockDayModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date;
  blockData: {
    id: number;
    start_date: string;
    end_date: string;
    is_active?: boolean;
    reason?: string;
  };
  property: string;
  onManageBlock: () => void;
}

const BookingBlockDayModal = ({
  isOpen,
  onClose,
  selectedDate,
  blockData,
  property,
  onManageBlock,
}: BookingBlockDayModalProps) => {
  const router = useRouter();

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("it-IT", {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    });
  };

  // Format date for URL params
  const formatDateForUrl = (date: Date) => {
    const d = new Date(date);
    const formattedDate = d.toLocaleDateString("en-GB", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
    return formattedDate.split("/").reverse().join("-"); // Convert "DD/MM/YYYY" to "YYYY-MM-DD"
  };

  const handleCreatePricing = () => {
    const dateStr = formatDateForUrl(selectedDate);
    router.push(
      `/dashboard/calendar/pricing?start_date=${dateStr}&end_date=${dateStr}&property=${property}`,
    );
    onClose();
  };

  const handleManageBookingBlock = () => {
    // Close this modal and trigger the manage block action
    onManageBlock();
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-full flex flex-col items-center">
        {/* Lock Icon */}
        <div
          className="w-16 h-16 rounded-full flex items-center justify-center mb-6"
          style={{ backgroundColor: "#fcbd4c20" }}
        >
          <Lock size={32} style={{ color: "#fcbd4c" }} />
        </div>

        {/* Centered Title */}
        <div className="w-full mb-6">
          <Title
            title="Giorno Bloccato"
            subtitle={`${formatDate(selectedDate)}`}
            style={{ textAlign: "center" }}
          />
        </div>

        {/* Information Container */}
        <div className="w-full max-w-lg mx-auto p-6 my-4 bg-white rounded-xl border border-gray-100 shadow-sm">
          {/* Block Info */}
          <div className="flex items-center mb-5 pb-4 border-b border-gray-100">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
              style={{ backgroundColor: "#113158" }}
            >
              <Calendar size={22} color="white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium" style={{ color: "#113158" }}>
                Blocco Attivo
              </p>
              <p className="font-semibold text-gray-800 text-base">
                {new Date(blockData.start_date).toLocaleDateString("it-IT")} -{" "}
                {new Date(blockData.end_date).toLocaleDateString("it-IT")}
              </p>
              {blockData.reason && (
                <p className="text-sm text-gray-600 mt-1">{blockData.reason}</p>
              )}
            </div>
          </div>

          {/* Available Actions */}
          <div className="text-center">
            <p className="text-gray-700 mb-4 font-medium">
              Questo giorno fa parte di un blocco prenotazione attivo. Scegli
              un'azione:
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col w-full gap-3 px-6 mt-4">
          {/* Create Pricing Button */}
          <Button
            text="Crea Stagione per questo Giorno"
            color="white"
            backgroundColor="var(--blue)"
            onClick={handleCreatePricing}
            icon={<DollarSign size={18} />}
          />

          {/* Manage Block Button */}
          <Button
            text="Gestisci Blocco Prenotazione"
            color="white"
            backgroundColor="#fcbd4c"
            onClick={handleManageBookingBlock}
            icon={<Lock size={18} />}
          />

          {/* Close Button */}
          <Button
            text="Chiudi"
            color="#113158"
            backgroundColor="#f8f9fa"
            onClick={onClose}
          />
        </div>
      </div>
    </Modal>
  );
};

export default BookingBlockDayModal;
