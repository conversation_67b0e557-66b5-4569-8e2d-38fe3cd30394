"use client";

import React, { useState, useEffect } from "react";
import { bedTypes } from "@/models/modelBedTypes";
import { isSingleRoomType } from "@/models/modelRoomType";
import {
  roomSpaceTypes,
  defaultRoomSpaceType,
} from "@/models/modelRoomSpaceType";
import InputSelectLabel from "./inputSelectLabel";
import InputCounter from "./inputCounter";
import { Bed, Plus, Trash2, Bath, Sofa } from "lucide-react";

interface Bed {
  bed_type_id: string | number; // Can be string in UI, but will be converted to number for API
  bed_count: number;
}

interface Room {
  beds: Bed[];
  isConfigured: boolean;
  roomSpaceType: string; // 'bedroom' or 'living_room'
}

interface RoomConfig {
  beds: Bed[];
  roomSpaceType: string;
}

interface BedConfig {
  [key: string]: RoomConfig;
}

interface BedTypeSelectorProps {
  bed_config?: BedConfig;
  onChange: (
    bed_config: BedConfig,
    quantity?: number,
    bathroom_quantity?: number,
  ) => void;
  bathroom_quantity?: number;
  roomType?: string; // Current room type value
}

const BedTypeSelector: React.FC<BedTypeSelectorProps> = ({
  bed_config = {},
  onChange,
  bathroom_quantity = 1,
  roomType = "", // Current room type
}) => {
  // Check if the current room type is restricted to a single room
  const isSingleRoom = isSingleRoomType(roomType);

  // Convert bed_config to room structure for initial state
  const initializeRooms = () => {
    // If we have a bed_config, use it to initialize rooms
    if (Object.keys(bed_config).length > 0) {
      return Object.entries(bed_config).map(([_, roomConfig]) => ({
        beds: roomConfig.beds.map((bed: Bed) => ({
          bed_type_id: bed.bed_type_id,
          bed_count: bed.bed_count,
        })),
        isConfigured: true,
        roomSpaceType: roomConfig.roomSpaceType || defaultRoomSpaceType,
      }));
    }

    // Start with one empty room if no beds or bed_config
    return [
      {
        beds: [],
        isConfigured: false,
        roomSpaceType: defaultRoomSpaceType,
      },
    ];
  };

  const [rooms, setRooms] = useState<Room[]>(initializeRooms);
  const [bathroomQuantity, setBathroomQuantity] =
    useState<number>(bathroom_quantity);

  // Keep track of previous room type to detect changes
  const [prevRoomType, setPrevRoomType] = useState<string>(roomType);

  useEffect(() => {
    // Check if room type has changed
    if (roomType !== prevRoomType) {
      setPrevRoomType(roomType);

      // Reset bed configuration when room type changes
      const defaultBeds: Bed[] = [{ bed_type_id: "1", bed_count: 1 }];
      const defaultRooms: Room[] = [
        {
          beds: defaultBeds,
          isConfigured: true,
          roomSpaceType: defaultRoomSpaceType,
        },
      ];
      setRooms(defaultRooms);

      // Create default bed_config
      const defaultBedConfig: BedConfig = {
        room1: {
          beds: defaultBeds.map((bed) => ({
            bed_type_id:
              typeof bed.bed_type_id === "string"
                ? parseInt(bed.bed_type_id as string)
                : bed.bed_type_id,
            bed_count: bed.bed_count,
          })),
          roomSpaceType: defaultRoomSpaceType,
        },
      };

      // Notify parent of the reset
      onChange(defaultBedConfig, 1, bathroomQuantity);
      return;
    }

    // Update rooms if bed_config props change
    if (
      Object.keys(bed_config).length > 0 &&
      (rooms.length === 0 || rooms[0].beds.length === 0)
    ) {
      setRooms(initializeRooms());
    }

    // Update bathroom quantity if prop changes
    setBathroomQuantity(bathroom_quantity);
  }, [bed_config, bathroom_quantity, roomType, prevRoomType]);

  // Calculate total beds across all rooms (for backward compatibility)
  const getAllBeds = (): Bed[] => {
    // Flatten all beds from all rooms into a single array
    const allBeds = rooms.flatMap((room) => room.beds);

    // Ensure bed_type_id is a number (not a string)
    const formattedBeds = allBeds.map((bed) => ({
      bed_type_id:
        typeof bed.bed_type_id === "string"
          ? parseInt(bed.bed_type_id as string)
          : bed.bed_type_id,
      bed_count: bed.bed_count,
    }));

    return formattedBeds;
  };

  // Convert rooms to bed_config format
  const getRoomConfig = (): BedConfig => {
    const config: BedConfig = {};

    rooms.forEach((room, index) => {
      // Skip empty rooms
      if (room.beds.length === 0) return;

      // Format bed_type_id as number
      const formattedBeds = room.beds.map((bed) => ({
        bed_type_id:
          typeof bed.bed_type_id === "string"
            ? parseInt(bed.bed_type_id as string)
            : bed.bed_type_id,
        bed_count: bed.bed_count,
      }));

      config[`room${index + 1}`] = {
        beds: formattedBeds,
        roomSpaceType: room.roomSpaceType || defaultRoomSpaceType,
      };
    });

    return config;
  };

  // Add a new room with a default bed
  const addRoom = () => {
    const newRoom: Room = {
      beds: [{ bed_type_id: "1", bed_count: 1 }],
      isConfigured: false,
      roomSpaceType: defaultRoomSpaceType,
    };

    const updatedRooms = [...rooms, newRoom];
    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();
    onChange(roomConfig, updatedRooms.length, bathroomQuantity);
  };

  // Add a bed to a specific room
  const addBedToRoom = (roomIndex: number) => {
    const newBed: Bed = { bed_type_id: "1", bed_count: 1 };
    const updatedRooms = [...rooms];
    updatedRooms[roomIndex].beds.push(newBed);
    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();
    onChange(roomConfig, rooms.length, bathroomQuantity);
  };

  // Remove a bed from a specific room
  const removeBedFromRoom = (roomIndex: number, bedIndex: number) => {
    const updatedRooms = [...rooms];
    updatedRooms[roomIndex].beds.splice(bedIndex, 1);

    // If room has no beds, remove the room unless it's the last one
    if (updatedRooms[roomIndex].beds.length === 0 && updatedRooms.length > 1) {
      updatedRooms.splice(roomIndex, 1);
    }

    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    // Update parent with all beds and new room quantity
    onChange(roomConfig, updatedRooms.length, bathroomQuantity);
  };

  // Remove an entire room
  const removeRoom = (roomIndex: number) => {
    let updatedRooms: Room[];

    if (rooms.length <= 1) {
      // Don't remove the last room, just clear its beds
      updatedRooms = [...rooms];
      updatedRooms[0].beds = [];
    } else {
      updatedRooms = [...rooms];
      updatedRooms.splice(roomIndex, 1);
    }

    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    // Update parent with all beds and new room quantity
    onChange(roomConfig, Math.max(1, updatedRooms.length), bathroomQuantity);
  };

  // Update bed type in a specific room
  const updateBedType = (
    roomIndex: number,
    bedIndex: number,
    bed_type_id: string,
  ) => {
    const updatedRooms = [...rooms];
    updatedRooms[roomIndex].beds[bedIndex].bed_type_id = bed_type_id;
    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    // Update parent with all beds
    onChange(roomConfig, rooms.length, bathroomQuantity);
  };

  // Update bed count in a specific room
  const updateBedCount = (
    roomIndex: number,
    bedIndex: number,
    bed_count: number,
  ) => {
    const updatedRooms = [...rooms];
    updatedRooms[roomIndex].beds[bedIndex].bed_count = bed_count;
    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    // Update parent with all beds
    onChange(roomConfig, rooms.length, bathroomQuantity);
  };

  const updateBathroomQuantity = (value: number) => {
    setBathroomQuantity(value);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    onChange(roomConfig, rooms.length, value);
  };

  // Update room space type
  const updateRoomSpaceType = (roomIndex: number, roomSpaceType: string) => {
    const updatedRooms = [...rooms];
    updatedRooms[roomIndex].roomSpaceType = roomSpaceType;
    setRooms(updatedRooms);

    // Get all beds from all rooms and create bed_config
    const allBeds = getAllBeds();
    const roomConfig = getRoomConfig();

    // Update parent with all beds
    onChange(roomConfig, rooms.length, bathroomQuantity);
  };

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-3">
        <p className="text-sm font-medium">Configurazione Camere</p>
        {/* Only show the "+" button if the room type is not restricted to a single room and there's at least one configured room */}
        {!isSingleRoom && (
          <button
            onClick={addRoom}
            className="flex items-center justify-center p-2 bg-[#113158] text-white rounded-full"
            type="button"
            aria-label="Aggiungi camera"
          >
            <Plus size={16} />
          </button>
        )}
      </div>

      {rooms.map((room, roomIndex) => (
        <div
          key={roomIndex}
          className="bg-white rounded-lg p-4 mb-4 border border-gray-200"
        >
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              {room.roomSpaceType === "living_room" ? (
                <Sofa size={18} className="text-[#113158] mr-2" />
              ) : (
                <Bed size={18} className="text-[#113158] mr-2" />
              )}
              <p className="text-sm font-medium">
                {room.roomSpaceType === "living_room" ? "Soggiorno" : "Camera"}{" "}
                {roomIndex + 1}
              </p>
            </div>
            {rooms.length > 1 && (
              <button
                onClick={() => removeRoom(roomIndex)}
                className="p-1 text-red-500 hover:bg-red-50 rounded-full"
                type="button"
                aria-label="Rimuovi camera"
              >
                <Trash2 size={16} />
              </button>
            )}
          </div>

          <div className="mb-4">
            <InputSelectLabel
              label="Tipo di stanza"
              placeholder="Seleziona"
              value={
                roomSpaceTypes.find((type) => type.value === room.roomSpaceType)
                  ?.label || ""
              }
              options={roomSpaceTypes}
              onSelect={(value) => updateRoomSpaceType(roomIndex, value)}
            />
          </div>

          <p className="text-sm text-gray-500 mb-4">
            {room.roomSpaceType === "living_room"
              ? "Quanti divani letto possono utilizzare gli ospiti in questo soggiorno?"
              : "Quanti letti possono utilizzare gli ospiti in questa camera?"}
          </p>

          {room.beds.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-6 bg-gray-50 rounded-lg border border-dashed border-gray-300">
              {room.roomSpaceType === "living_room" ? (
                <Sofa size={32} className="text-gray-400 mb-2" />
              ) : (
                <Bed size={32} className="text-gray-400 mb-2" />
              )}
              <p className="text-sm text-gray-500">
                {room.roomSpaceType === "living_room"
                  ? "Nessun divano letto aggiunto"
                  : "Nessun letto aggiunto"}
              </p>
              <button
                onClick={() => addBedToRoom(roomIndex)}
                className="mt-3 px-4 py-2 bg-[#113158] text-white text-sm rounded-lg"
                type="button"
              >
                {room.roomSpaceType === "living_room"
                  ? "Aggiungi divano letto"
                  : "Aggiungi letto"}
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {room.beds.map((bed, bedIndex) => (
                <div
                  key={bedIndex}
                  className="flex flex-col p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      {room.roomSpaceType === "living_room" ? (
                        <Sofa size={18} className="text-[#113158] mr-2" />
                      ) : (
                        <Bed size={18} className="text-[#113158] mr-2" />
                      )}
                      <span className="text-sm font-medium">
                        {room.roomSpaceType === "living_room"
                          ? `Divano letto ${bedIndex + 1}`
                          : `Letto ${bedIndex + 1}`}
                      </span>
                    </div>
                    <button
                      onClick={() => removeBedFromRoom(roomIndex, bedIndex)}
                      className="p-1 text-red-500 hover:bg-red-50 rounded-full"
                      type="button"
                      aria-label="Rimuovi letto"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <InputSelectLabel
                        label="Tipo di letto"
                        placeholder="Seleziona"
                        value={
                          bedTypes.find(
                            (type) =>
                              type.value ===
                              (typeof bed.bed_type_id === "number"
                                ? bed.bed_type_id.toString()
                                : bed.bed_type_id),
                          )?.label || ""
                        }
                        options={bedTypes}
                        onSelect={(value) =>
                          updateBedType(roomIndex, bedIndex, value)
                        }
                      />
                    </div>
                    <div>
                      <InputCounter
                        label="Quantità"
                        value={bed.bed_count}
                        minimum={1}
                        maximum={10}
                        onChange={(value) =>
                          updateBedCount(roomIndex, bedIndex, value)
                        }
                      />
                    </div>
                  </div>
                </div>
              ))}

              <div className="mt-4">
                <button
                  onClick={() => addBedToRoom(roomIndex)}
                  className="flex items-center justify-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm"
                  type="button"
                >
                  <Plus size={14} className="mr-1" />
                  {room.roomSpaceType === "living_room"
                    ? "Aggiungi divano letto"
                    : "Aggiungi letto"}
                </button>
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Bathroom counter */}
      <div className="bg-white rounded-lg p-4 mb-4">
        <div className="flex items-center mb-3">
          <Bath size={18} className="text-[#113158] mr-2" />
          <p className="text-sm font-medium">Bagni</p>
        </div>
        <InputCounter
          label="Numero di bagni"
          value={bathroomQuantity}
          minimum={1}
          maximum={10}
          onChange={updateBathroomQuantity}
        />
      </div>
    </div>
  );
};

export default BedTypeSelector;
