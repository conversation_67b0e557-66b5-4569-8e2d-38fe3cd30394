"use client";
import React, {
  useEffect,
  useState,
  useRef,
  useContext,
  useCallback,
} from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import { useRouter } from "next/navigation";
import { sendChatMessage } from "@/services/api";
import HeaderPage from "@/components/_globals/headerPage";
import CardAlert from "@/components/cards/CardAlert";
import toast from "react-hot-toast";
import { useSupportChat } from "@/hooks/useSupportChat";
import { isImageFile, downloadFile } from "@/utils/fileUtils";
import { SupportMessage, PendingMessage } from "@/types/support";
import { SupportChatErrorBoundary } from "@/components/chat/SupportChatErrorBoundary";
import { AttachmentList } from "@/components/chat/AttachmentPreview";
import { ImagePreviewModal } from "@/components/chat/ImagePreviewModal";
import { TypingIndicator } from "@/components/chat/TypingIndicator";
import { ConnectionStatusIndicator } from "@/components/chat/ConnectionStatusIndicator";
import { EnhancedMessageInput } from "@/components/chat/EnhancedMessageInput";
import { AuthContext } from "@/components/_context/AuthContext";
import { getAccessToken } from "@/services/tokenService";
import Loader1 from "@/components/loaders/Loader1";
import {
  FileUploadService,
  FileUploadProgress,
} from "@/services/FileUploadService";
import { motion, AnimatePresence } from "framer-motion";

function Page() {
  // Existing state
  const router = useRouter();
  const { isAuthenticated, isLoading } = useContext(AuthContext);
  const [message, setMessage] = useState<string>("");
  const [attachments, setAttachments] = useState<File[] | null>(null);
  const [pendingMessages, setPendingMessages] = useState<PendingMessage[]>([]);
  const [sentAttachmentUrls, setSentAttachmentUrls] = useState<{
    [key: string]: string;
  }>({});
  const [previewImage, setPreviewImage] = useState<{
    attachment: SupportMessage["attachments"][0];
    url: string;
  } | null>(null);
  const [showTemplateQuestions, setShowTemplateQuestions] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // New ref to track previous messages for comparison
  const previousMessagesRef = useRef<SupportMessage[]>([]);

  // New state for file uploads
  const [fileUploads, setFileUploads] = useState<FileUploadProgress[]>([]);
  const fileUploadServiceRef = useRef<FileUploadService | null>(null);
  // Get user token - only after authentication is complete
  const userToken = isAuthenticated && !isLoading ? getAccessToken() : null;
  // Handle message confirmation from server
  const handleMessageConfirmed = useCallback(
    (clientMessageId: string, serverMessageId: string) => {
      console.log(
        `Message confirmed by server: ${clientMessageId} → ${serverMessageId}`,
      );

      // Update the pending message status to confirmed
      setPendingMessages((prev) => {
        // Find the pending message that matches this client ID
        const pendingMessage = prev.find(
          (pm) => pm.clientMessageId === clientMessageId,
        );
        if (!pendingMessage) {
          return prev; // No matching pending message found
        }
        return prev.map((pm) =>
          pm.clientMessageId === clientMessageId
            ? { ...pm, status: "confirmed", serverMessageId }
            : pm,
        );
      });
    },
    [],
  ); // No dependencies needed

  const {
    messages,
    connectionStatus,
    sendMessage: sendWebSocketMessage,
    loadMore,
    hasMore,
    isLoadingMore,
    chatId,
    isInitializing,
    typingStatus,
    handleUserTyping,
    markMessagesAsRead,
    wsRef, // Get WebSocket reference for FileUploadService
  } = useSupportChat({
    token: userToken || "",
    enabled: isAuthenticated && !isLoading && !!userToken,
    onMessageConfirmed: handleMessageConfirmed,
  });

  // Define the progress update handler with useCallback to avoid recreating on every render
  const handleProgressUpdate = useCallback((progress: FileUploadProgress) => {
    setFileUploads((prev) => {
      // Check if this progress update already exists to avoid duplicates
      const exists = prev.some(
        (p) =>
          p.fileName === progress.fileName &&
          p.messageId === progress.messageId &&
          p.progress === progress.progress,
      );
      if (exists) return prev;
      return [...prev, progress];
    });
  }, []);

  // Initialize FileUploadService when WebSocket is ready
  useEffect(() => {
    if (
      wsRef.current &&
      connectionStatus.isConnected &&
      !fileUploadServiceRef.current &&
      chatId
    ) {
      fileUploadServiceRef.current = new FileUploadService({
        chatId: chatId,
        webSocketService: wsRef.current,
        onProgressUpdate: handleProgressUpdate,
      });
    }

    return () => {
      // Just set to null since FileUploadService doesn't have a cleanup method
      fileUploadServiceRef.current = null;
    };
  }, [wsRef, connectionStatus.isConnected, chatId, handleProgressUpdate]);
  // Monitor messages and clean up pending messages when they appear in the main message list
  useEffect(() => {
    // Skip if no messages
    if (messages.length === 0) return;

    // Compare with previous messages to avoid unnecessary updates
    const hasNewMessages =
      messages.length !== previousMessagesRef.current.length ||
      messages.some(
        (msg, index) => previousMessagesRef.current[index]?.id !== msg.id,
      );

    if (hasNewMessages) {
      // Update our ref to the current messages
      previousMessagesRef.current = [...messages];

      // Check if any pending messages have corresponding messages in the main list
      setPendingMessages((currentPending) => {
        if (currentPending.length === 0) return currentPending;

        return currentPending.filter((pending) => {
          // Keep the message if it doesn't have a server ID yet
          if (!pending.serverMessageId) return true;

          // Check if this pending message has appeared in the main message list
          const isInMainList = messages.some(
            (msg) => msg.id === pending.serverMessageId,
          );

          // If it's in the main list, we can remove it from pending
          return !isInMainList;
        });
      });
    }
  }, [messages]); // Only depend on messages array
  // Separate effect for auto-scrolling
  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive or pending messages change
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages.length, pendingMessages.length]); // Only depend on the length of arrays

  // Effect to mark messages as read when user is actively viewing the chat
  useEffect(() => {
    // Mark messages as read when the component mounts and when new messages arrive
    if (connectionStatus.isConnected && messages.length > 0) {
      const timer = setTimeout(() => {
        markMessagesAsRead();
      }, 2000); // 2 second delay to ensure user has seen the messages

      return () => clearTimeout(timer);
    }
  }, [connectionStatus.isConnected, messages.length, markMessagesAsRead]);

  // Effect to hide template questions when messages exist
  useEffect(() => {
    if (messages.length > 0) {
      setShowTemplateQuestions(false);
    }
  }, [messages.length]);
  // Cleanup blob URLs when component unmounts or pending messages change
  useEffect(() => {
    return () => {
      // Cleanup blob URLs for pending messages
      pendingMessages.forEach((pendingMsg) => {
        if (pendingMsg.attachments) {
          pendingMsg.attachments.forEach(() => {
            // URLs are created in the render function and cleaned up automatically
            // when the component unmounts or when the URLs are no longer referenced
          });
        }
      });
    };
  }, []); // Remove dependency on pendingMessages to avoid circular updates

  // Handle file upload with WebSocket progress tracking
  const handleFileUpload = useCallback(
    async (file: File, messageId?: string): Promise<boolean> => {
      if (!fileUploadServiceRef.current) {
        console.error("FileUploadService not initialized");
        return false;
      }

      try {
        await fileUploadServiceRef.current.uploadFile(file, messageId);
        return true;
      } catch (error) {
        console.error("File upload failed:", error);
        toast.error(`Errore durante l'upload del file: ${file.name}`);
        return false;
      }
    },
    [],
  );

  // Enhanced message sending with proper WebSocket and file handling
  const sendMessage = async (
    messageText: string,
    attachments: File[] | null,
  ) => {
    // Don't send empty messages (unless they have attachments)
    if (
      (!messageText || !messageText.trim()) &&
      (!attachments || attachments.length === 0)
    ) {
      return;
    }

    // Generate pending message ID
    const pendingId = `pending-${Date.now()}`;

    // Create pending message object for optimistic UI
    const pendingMessage: PendingMessage = {
      id: pendingId,
      message: messageText.trim(),
      attachments: attachments || [],
      timestamp: new Date().toISOString(),
      status: "sending",
    };

    // Add to pending messages immediately
    setPendingMessages((prev) => [...prev, pendingMessage]);

    // Create attachment URLs for display
    const currentAttachmentUrls: { [key: string]: string } = {};
    if (attachments) {
      attachments.forEach((file) => {
        const url = URL.createObjectURL(file);
        currentAttachmentUrls[file.name] = url;
      });
    }

    // Update sent attachment URLs
    setSentAttachmentUrls({ ...sentAttachmentUrls, ...currentAttachmentUrls });

    // Clear input immediately for better UX
    const currentAttachments = attachments ? [...attachments] : null;
    setMessage("");
    setAttachments(null);

    try {
      // Determine sending method based on message content and connection status
      const hasAttachments =
        currentAttachments && currentAttachments.length > 0;
      const hasText = messageText && messageText.trim();
      const canUseWebSocket = connectionStatus.isConnected && chatId;

      // Case 1: Message with attachments - ALWAYS use API (WebSocket doesn't handle file uploads)
      if (hasAttachments) {
        console.log("Sending message with attachments via API");
        const response = await sendChatMessage(messageText, currentAttachments);

        if (response?.success && response?.data) {
          // Message sent successfully, update UI
          const serverMessageId = response.data.id;

          setPendingMessages((prev) =>
            prev.map((pm) =>
              pm.id === pendingId
                ? {
                    ...pm,
                    status: "sent",
                    serverMessageId: serverMessageId,
                  }
                : pm,
            ),
          );

          // Hide template questions after successful send
          setShowTemplateQuestions(false);

          console.log(
            "Message with attachments sent via API:",
            serverMessageId,
          );

          // If WebSocket is connected, track upload progress via WebSocket
          if (
            connectionStatus.isConnected &&
            wsRef.current &&
            currentAttachments.length > 0
          ) {
            console.log("Setting up WebSocket tracking for file uploads");

            // Associate files with the message for WebSocket tracking
            currentAttachments.forEach((file) => {
              if (fileUploadServiceRef.current) {
                handleFileUpload(file, serverMessageId);
              }
            });
          }
        } else {
          console.error("Failed to send message with attachments");
          setPendingMessages((prev) =>
            prev.map((pm) =>
              pm.id === pendingId ? { ...pm, status: "error" } : pm,
            ),
          );
          toast.error("Errore durante l'invio del messaggio");
        }
      }
      // Case 2: Text-only message and WebSocket is available - use WebSocket
      else if (hasText && canUseWebSocket) {
        console.log("Sending text-only message via WebSocket");
        const result = sendWebSocketMessage(messageText);

        if (result.success) {
          const msgId = result.messageId;
          // Update pending message status
          setPendingMessages((prev) =>
            prev.map((pm) =>
              pm.id === pendingId
                ? {
                    ...pm,
                    status: "sent",
                    clientMessageId: msgId || undefined,
                  }
                : pm,
            ),
          );

          // Hide template questions after successful send
          setShowTemplateQuestions(false);

          console.log(`Message sent with tracking ID: ${msgId}`);
        } else {
          console.error("Failed to send message via WebSocket");
          // Fall back to API only if WebSocket failed
          handleApiSendFallback(pendingId, messageText, null);
        }
      }
      // Case 3: Text-only message but no WebSocket - use API fallback
      else if (hasText && !canUseWebSocket) {
        console.log(
          "WebSocket not available, using API fallback for text message",
        );
        handleApiSendFallback(pendingId, messageText, null);
      }
      // Case 4: Should not happen (empty message), but handle gracefully
      else {
        console.warn("Attempted to send empty message");
        setPendingMessages((prev) => prev.filter((pm) => pm.id !== pendingId));
      }
    } catch (error) {
      console.error("Error sending message:", error);
      setPendingMessages((prev) =>
        prev.map((pm) =>
          pm.id === pendingId ? { ...pm, status: "error" } : pm,
        ),
      );
      toast.error("Errore durante l'invio del messaggio");
    }
  };

  // Helper function for API fallback
  const handleApiSendFallback = async (
    pendingId: string,
    messageText: string,
    files: File[] | null,
  ) => {
    console.log("Using API fallback for message sending:", {
      hasText: !!messageText,
      hasFiles: !!(files && files.length > 0),
      wsConnected: connectionStatus.isConnected,
      chatId: !!chatId,
    });
    try {
      const response = await sendChatMessage(messageText, files);

      if (response?.success && response?.data) {
        setPendingMessages((prev) =>
          prev.map((pm) =>
            pm.id === pendingId
              ? {
                  ...pm,
                  status: "sent",
                  serverMessageId: response.data.id,
                }
              : pm,
          ),
        );

        // Hide template questions after successful send
        setShowTemplateQuestions(false);
      } else {
        setPendingMessages((prev) =>
          prev.map((pm) =>
            pm.id === pendingId ? { ...pm, status: "error" } : pm,
          ),
        );
        toast.error("Errore durante l'invio del messaggio");
      }
    } catch (error) {
      console.error("API fallback failed:", error);
      setPendingMessages((prev) =>
        prev.map((pm) =>
          pm.id === pendingId ? { ...pm, status: "error" } : pm,
        ),
      );
      toast.error("Errore durante l'invio del messaggio");
    }
  };

  const handleSendMessage = async () => {
    // Use our enhanced sendMessage function
    await sendMessage(message, attachments);
  };

  const handleTemplateQuestionClick = (templateMessage: string) => {
    setMessage(templateMessage);
    // Focus on the input after setting the message
    setTimeout(() => {
      const messageInput = document.querySelector(
        'input[placeholder="Scrivi un messaggio..."]',
      ) as HTMLInputElement;
      if (messageInput) {
        messageInput.focus();
      }
    }, 100);
  };

  const renderPendingMessage = (pendingMsg: PendingMessage) => {
    return (
      <div key={pendingMsg.id} className="w-full flex justify-end mb-3">
        {" "}
        <div
          style={{
            maxWidth: "70%",
            padding: "10px 20px",
            borderRadius: "20px 0 20px 20px",
            boxShadow: "0px 0px 10px 0px #00000010",
            backgroundColor: "var(--accent)",
            color: "white",
            opacity:
              pendingMsg.status === "confirmed"
                ? 1
                : pendingMsg.status === "error"
                  ? 0.6
                  : 0.8,
            border:
              pendingMsg.status === "error"
                ? "2px solid #ef4444"
                : pendingMsg.status === "confirmed"
                  ? "2px solid #10b981"
                  : "none",
          }}
        >
          {/* Message text */}
          {pendingMsg.message && (
            <p
              className="mb-2"
              style={{
                color: "white",
                whiteSpace: "pre-wrap",
              }}
            >
              {pendingMsg.message}
            </p>
          )}
          {/* Status indicator */}
          <div className="flex items-center justify-between">
            <p
              className="text-xs"
              style={{
                color: "rgba(255,255,255,0.7)",
              }}
            >
              {new Date(pendingMsg.timestamp).toLocaleString("it-IT", {
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>

            <div className="flex items-center ml-2">
              {pendingMsg.status === "sending" && (
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                  <div
                    className="w-1 h-1 bg-white rounded-full animate-pulse"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                  <div
                    className="w-1 h-1 bg-white rounded-full animate-pulse"
                    style={{ animationDelay: "0.4s" }}
                  ></div>
                </div>
              )}{" "}
              {pendingMsg.status === "sent" && (
                <svg
                  className="w-4 h-4 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {pendingMsg.status === "confirmed" && (
                <svg
                  className="w-4 h-4 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
              {pendingMsg.status === "error" && (
                <svg
                  className="w-4 h-4 text-red-300"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </div>
          </div>{" "}
          {/* Pending attachments */}
          {pendingMsg.attachments && pendingMsg.attachments.length > 0 && (
            <div className="mt-2">
              <AttachmentList
                attachments={pendingMsg.attachments.map((file) => ({
                  id: `pending-${file.name}`,
                  file_name: file.name,
                  file_size: file.size,
                  file_type: file.type,
                  file_url: URL.createObjectURL(file),
                  created_at: new Date().toISOString(),
                }))}
                sentAttachmentUrls={Object.fromEntries(
                  pendingMsg.attachments.map((file) => [
                    file.name,
                    URL.createObjectURL(file),
                  ]),
                )}
                isUserMessage={true}
                layout={
                  pendingMsg.attachments.length > 2 ? "grid" : "horizontal"
                }
                size="medium"
                onAttachmentClick={(attachment, url) => {
                  if (isImageFile(attachment.file_name)) {
                    setPreviewImage({ attachment, url });
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderMessageBubble = (msg: SupportMessage, index: number) => {
    const isUserMessage = msg.sender === "user";
    const isLastMessage = index === messages.length - 1;

    return (
      <div
        key={msg.id}
        className={`w-full flex ${isUserMessage ? "justify-end" : "justify-start"} mb-3`}
      >
        <div
          style={{
            maxWidth: "70%",
            padding: "10px 20px",
            borderRadius: isUserMessage
              ? "20px 0 20px 20px"
              : "0 20px 20px 20px",
            boxShadow: "0px 0px 10px 0px #00000010",
            backgroundColor: isUserMessage ? "var(--accent)" : "white",
            color: isUserMessage ? "white" : "black",
          }}
        >
          {/* Sender name for support messages */}
          {!isUserMessage && (
            <p className="text-xs font-semibold mb-1 text-gray-600">
              Assistenza Affitti
            </p>
          )}
          {/* Message text */}
          {msg.message && (
            <p
              className="mb-2"
              style={{
                color: isUserMessage ? "white" : "black",
                whiteSpace: "pre-wrap",
              }}
            >
              {msg.message}
            </p>
          )}
          {/* Message timestamp and read status */}
          <div className="flex items-center justify-between mb-2">
            <p
              className="text-xs"
              style={{
                color: isUserMessage ? "rgba(255,255,255,0.7)" : "#999",
              }}
            >
              {new Date(msg.created_at).toLocaleString("it-IT", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>

            {/* Read status indicator for user messages */}
            {isUserMessage && (
              <div className="flex items-center ml-2">
                {msg.is_read ? (
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-4 h-4 text-white opacity-50"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </div>
            )}
          </div>{" "}
          {/* Message attachments */}
          {msg.attachments && msg.attachments.length > 0 && (
            <AttachmentList
              attachments={msg.attachments}
              sentAttachmentUrls={sentAttachmentUrls}
              isUserMessage={isUserMessage}
              layout={msg.attachments.length > 2 ? "grid" : "horizontal"}
              size="medium"
              onAttachmentClick={(attachment, url) => {
                if (isImageFile(attachment.file_name)) {
                  setPreviewImage({ attachment, url });
                } else {
                  downloadFile(url, attachment.file_name);
                }
              }}
            />
          )}
        </div>
        {isLastMessage && <div ref={messagesEndRef} />}
      </div>
    );
  };
  // Show loading state while authentication is in progress
  if (isLoading) {
    return (
      <MobilePageStart noPadding>
        <HeaderPage
          actionLeftIcon={() => {
            router.back();
          }}
          title="Assistenza Affitti"
          actionRightIcon={() => {
            router.push("/dashboard");
          }}
        />
        <div className="flex flex-col items-center justify-center h-full">
          <Loader1 />
        </div>
      </MobilePageStart>
    );
  }

  // If not authenticated, show error message
  if (!isAuthenticated) {
    return (
      <MobilePageStart noPadding>
        <HeaderPage
          actionLeftIcon={() => {
            router.back();
          }}
          title="Assistenza Affitti"
          actionRightIcon={() => {
            router.push("/dashboard");
          }}
        />
      </MobilePageStart>
    );
  }

  return (
    <SupportChatErrorBoundary>
      <MobilePageStart noPadding>
        <HeaderPage
          actionLeftIcon={() => {
            router.back();
          }}
          title="Assistenza Affitti"
          actionRightIcon={() => {
            router.push("/dashboard");
          }}
        />

        <div
          style={{
            height: "100%",
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            position: "relative",
          }}
        >
          {/* Chat */}
          <div className="p-2">
            <CardAlert
              title="Attenzione"
              message="I Messaggi inviati in questa chat saranno inoltrati via email al team di assistenza, che risponderà entro 48 ore"
              color="orange"
              style={{
                marginBottom: "20px",
              }}
            />
          </div>
          <div
            className="flex flex-col w-full h-full overflow-y-auto"
            style={{
              paddingBottom: "70px",
              paddingLeft: "10px",
              paddingRight: "10px",
            }}
          >
            {/* Load more button */}
            {hasMore && (
              <div className="flex justify-center mb-4">
                <button
                  onClick={loadMore}
                  disabled={isLoadingMore}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50"
                >
                  {isLoadingMore ? "Caricamento..." : "Carica altri messaggi"}
                </button>
              </div>
            )}{" "}
            {/* Messages */}
            {messages.map((msg, index) => renderMessageBubble(msg, index))}
            {/* Pending Messages */}
            {pendingMessages.map((pendingMsg) =>
              renderPendingMessage(pendingMsg),
            )}
            {/* Typing Indicator */}
            <TypingIndicator typingStatus={typingStatus} className="mb-2" />
            {messages.length === 0 && !isInitializing && (
              <div className="flex flex-col items-center justify-center h-full text-gray-500 px-4">
                <motion.svg
                  className="w-16 h-16 mb-4 text-gray-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </motion.svg>
                <motion.p
                  className="text-center mb-6 text-gray-600"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  Inizia una conversazione con il nostro team di supporto!
                </motion.p>

                {/* Template Questions */}
                <AnimatePresence>
                  {showTemplateQuestions && (
                    <motion.div
                      className="w-full max-w-md space-y-3"
                      initial={{ y: 30, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -30, opacity: 0, scale: 0.95 }}
                      transition={{ duration: 0.4, delay: 0.3 }}
                    >
                      <motion.p
                        className="text-sm font-medium text-gray-700 text-center mb-4"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.5 }}
                      >
                        Domande frequenti:
                      </motion.p>

                      {/* Template Question 1 - CIN Request */}
                      <motion.button
                        onClick={() =>
                          handleTemplateQuestionClick(
                            "Vorrei assistenza per la richiesta del CIN (Codice Identificativo Nazionale) per la mia proprietà.",
                          )
                        }
                        className="w-full p-3 bg-white border border-gray-200 rounded-lg text-left hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                        style={{
                          borderColor: "#e5e7eb",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.6 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: "#113158" }}
                          >
                            <svg
                              className="w-4 h-4 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              Assistenza per richiesta CIN
                            </p>
                            <p className="text-xs text-gray-500">
                              Aiuto con il Codice Identificativo Nazionale
                            </p>
                          </div>
                        </div>
                      </motion.button>

                      {/* Template Question 2 - Property Sync */}
                      <motion.button
                        onClick={() =>
                          handleTemplateQuestionClick(
                            "Vorrei capire come sincronizzare la mia proprietà con le piattaforme di prenotazione.",
                          )
                        }
                        className="w-full p-3 bg-white border border-gray-200 rounded-lg text-left hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                        style={{
                          borderColor: "#e5e7eb",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.7 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: "#fcbd4c" }}
                          >
                            <svg
                              className="w-4 h-4 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              Sincronizzazione proprietà
                            </p>
                            <p className="text-xs text-gray-500">
                              Connessione con piattaforme di prenotazione
                            </p>
                          </div>
                        </div>
                      </motion.button>

                      {/* Template Question 3 - Onboarding Help */}
                      <motion.button
                        onClick={() =>
                          handleTemplateQuestionClick(
                            "Ho bisogno di assistenza per completare la configurazione della mia proprietà su Heibooky.",
                          )
                        }
                        className="w-full p-3 bg-white border border-gray-200 rounded-lg text-left hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                        style={{
                          borderColor: "#e5e7eb",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.8 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: "#113158" }}
                          >
                            <svg
                              className="w-4 h-4 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              Configurazione proprietà
                            </p>
                            <p className="text-xs text-gray-500">
                              Aiuto per completare l'onboarding
                            </p>
                          </div>
                        </div>
                      </motion.button>

                      {/* Template Question 4 - General Assistance */}
                      <motion.button
                        onClick={() =>
                          handleTemplateQuestionClick(
                            "Vorrei assistenza generale per l'utilizzo della piattaforma Heibooky.",
                          )
                        }
                        className="w-full p-3 bg-white border border-gray-200 rounded-lg text-left hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
                        style={{
                          borderColor: "#e5e7eb",
                          boxShadow: "0 1px 3px rgba(0, 0, 0, 0.1)",
                        }}
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.9 }}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: "#fcbd4c" }}
                          >
                            <svg
                              className="w-4 h-4 text-white"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                              />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              Assistenza generale
                            </p>
                            <p className="text-xs text-gray-500">
                              Supporto per l'utilizzo della piattaforma
                            </p>
                          </div>
                        </div>
                      </motion.button>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            )}
            {isInitializing && (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <Loader1 />
                <p>Caricamento messaggi...</p>
              </div>
            )}
          </div>{" "}
          {/* Enhanced Message Input */}
          <div className="absolute bottom-0 left-0 right-0">
            <EnhancedMessageInput
              message={message}
              setMessage={setMessage}
              attachments={attachments}
              setAttachments={setAttachments}
              onSendMessage={handleSendMessage}
              onUserTyping={handleUserTyping}
              isConnected={connectionStatus.isConnected}
              disabled={isInitializing}
            />
          </div>
        </div>
      </MobilePageStart>

      {/* Image Preview Modal */}
      {previewImage && (
        <ImagePreviewModal
          attachment={previewImage.attachment}
          fileUrl={previewImage.url}
          isOpen={true}
          onClose={() => setPreviewImage(null)}
        />
      )}
    </SupportChatErrorBoundary>
  );
}

export default Page;
