"use client";

import React from "react";
import { FileText, Download, Clock, CheckCircle } from "lucide-react";
import useContract from "@/hooks/useContract";

interface ContractDashboardWidgetProps {
  className?: string;
}

const ContractDashboardWidget: React.FC<ContractDashboardWidgetProps> = ({
  className = "",
}) => {
  const { downloadContract, statusSummary } = useContract();

  const getStatusIcon = () => {
    switch (statusSummary.status) {
      case "loading":
        return { icon: Clock, color: "text-gray-500", bgColor: "bg-gray-100" };
      case "available":
        return {
          icon: CheckCircle,
          color: "text-green-500",
          bgColor: "bg-green-100",
        };
      case "pending":
        return {
          icon: Clock,
          color: "text-yellow-500",
          bgColor: "bg-yellow-100",
        };
      default:
        return {
          icon: FileText,
          color: "text-gray-500",
          bgColor: "bg-gray-100",
        };
    }
  };

  const getStatusTitle = () => {
    switch (statusSummary.status) {
      case "loading":
        return "Verifica Contratto";
      case "available":
        return "Contratto Disponibile";
      case "pending":
        return "Contratto in Preparazione";
      default:
        return "Contratto Non Disponibile";
    }
  };

  const iconConfig = getStatusIcon();
  const StatusIcon = iconConfig.icon;

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        <div
          className={`w-12 h-12 rounded-full ${iconConfig.bgColor} flex items-center justify-center flex-shrink-0`}
        >
          <StatusIcon size={24} className={iconConfig.color} />
        </div>

        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 mb-1">
            {getStatusTitle()}
          </h3>
          <p className="text-xs text-gray-600 mb-3">{statusSummary.message}</p>

          {statusSummary.canDownload && (
            <button
              onClick={downloadContract}
              className="inline-flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download size={14} />
              Scarica Contratto
            </button>
          )}

          {statusSummary.status === "incomplete" && (
            <p className="text-xs text-red-600 mt-2">
              Completa prima le informazioni di fatturazione
            </p>
          )}

          {statusSummary.status === "pending" && (
            <p className="text-xs text-yellow-600 mt-2">
              Crea un account collegato per attivare il contratto
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContractDashboardWidget;
