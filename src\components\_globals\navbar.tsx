import React, { useEffect, useState } from "react";
import styles from "./globals.module.css";
import { BookIcon, Calendar, HomeIcon, MoreHorizontal } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

function Navbar() {
  const pathname = usePathname();

  const [currentRoute, setCurrentRoute] = useState("");

  useEffect(() => {
    setCurrentRoute(pathname);
  }, [pathname]);
  return (
    <section className={styles.navbar}>
      {/*  */}
      <Tab
        icon={
          <HomeIcon
            width={18}
            fill={`${currentRoute === "/dashboard" ? "black" : "transparent"}`}
          />
        }
        title={"Home"}
        href="/dashboard"
      />

      <Tab
        icon={
          <BookIcon
            width={18}
            fill={`${currentRoute === "/dashboard/bookings" ? "black" : "transparent"}`}
          />
        }
        title={"Prenotazioni"}
        href="/dashboard/bookings"
      />

      <Tab
        icon={
          <Calendar
            width={18}
            fill={`${currentRoute === "/dashboard/calendar" ? "black" : "transparent"}`}
          />
        }
        title={"Calendario"}
        href="/dashboard/calendar"
      />

      <Tab
        icon={
          <MoreHorizontal
            width={18}
            fill={`${currentRoute === "/dashboard/settings" ? "black" : "transparent"}`}
          />
        }
        title={"Altro"}
        href="/dashboard/settings"
      />
    </section>
  );
}

export default Navbar;

interface TabProps {
  icon: React.ReactNode;
  title: string;
  href?: string;
}
function Tab({ icon, title, href }: TabProps) {
  return (
    <Link
      href={`${href ?? "/"}`}
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: "10px",
        borderRadius: "10px",
        cursor: "pointer",
      }}
    >
      {icon}
      <p style={{ fontSize: "11.5px" }}>{title}</p>
    </Link>
  );
}
