"use client";

import { useState } from "react";
import ChekinWidget from "./ChekinWidget";
import { ChekinStatusSummary, ChekinPropertyEvent, ChekinBookingEvent } from "@/services/chekinApi";
import { RefreshCw } from "lucide-react";

interface Props {
  propertyId: string | number;
  reservationId?: string | number;
  isReservationDetailsMode?: boolean;
  className?: string;
  showStatusPanel?: boolean;
}

export default function ChekinWidgetWithStatus({
  propertyId,
  reservationId,
  isReservationDetailsMode = false,
  className,
  showStatusPanel = false,
}: Props) {
  const [apiData, setApiData] = useState<{
    statusSummary: ChekinStatusSummary | null;
    propertyEvents: ChekinPropertyEvent[];
    bookingEvents: ChekinBookingEvent[];
    apiDataLoaded: boolean;
    refreshData: () => Promise<void>;
  } | null>(null);

  const [refreshing, setRefreshing] = useState(false);

  const handleApiDataChange = (data: any) => {
    setApiData(data);
  };

  const handleRefresh = async () => {
    if (apiData?.refreshData) {
      setRefreshing(true);
      try {
        await apiData.refreshData();
      } finally {
        setRefreshing(false);
      }
    }
  };

  return (
    <div className={`space-y-4 ${className ?? ""}`}>
      {/* Status Panel */}
      {showStatusPanel && apiData?.apiDataLoaded && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">Chekin Status</h3>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-3 h-3 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>

          {apiData.statusSummary && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
              <div className="bg-white p-2 rounded border">
                <div className="font-medium text-gray-600">Total Events</div>
                <div className="text-lg font-bold text-gray-900">{apiData.statusSummary.total_events}</div>
              </div>
              <div className="bg-white p-2 rounded border">
                <div className="font-medium text-yellow-600">Pending</div>
                <div className="text-lg font-bold text-yellow-700">{apiData.statusSummary.pending_events}</div>
              </div>
              <div className="bg-white p-2 rounded border">
                <div className="font-medium text-green-600">Processed</div>
                <div className="text-lg font-bold text-green-700">{apiData.statusSummary.processed_events}</div>
              </div>
              <div className="bg-white p-2 rounded border">
                <div className="font-medium text-red-600">Failed</div>
                <div className="text-lg font-bold text-red-700">{apiData.statusSummary.failed_events}</div>
              </div>
            </div>
          )}

          {/* Property Events Summary */}
          {apiData.propertyEvents.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <div className="text-xs font-medium text-gray-600 mb-2">Property Events</div>
              <div className="space-y-1">
                {apiData.propertyEvents.slice(0, 3).map((event, index) => (
                  <div key={index} className="text-xs bg-white p-2 rounded border">
                    <div className="font-medium">{event.property_name}</div>
                    <div className="text-gray-600">
                      {event.total_events} events | 
                      Police: {event.police_connected ? '✓' : '✗'} | 
                      Stat: {event.stat_connected ? '✓' : '✗'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Booking Events Summary */}
          {apiData.bookingEvents.length > 0 && (
            <div className="mt-3 pt-3 border-t">
              <div className="text-xs font-medium text-gray-600 mb-2">Booking Events</div>
              <div className="space-y-1">
                {apiData.bookingEvents.slice(0, 3).map((event, index) => (
                  <div key={index} className="text-xs bg-white p-2 rounded border">
                    <div className="font-medium">{event.guest_name}</div>
                    <div className="text-gray-600">
                      {event.total_events} events | Status: {event.registration_status} |
                      Guest: {event.guest_created ? '✓' : '✗'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Chekin Widget */}
      <ChekinWidget
        propertyId={propertyId}
        reservationId={reservationId}
        isReservationDetailsMode={isReservationDetailsMode}
        onApiDataChange={handleApiDataChange}
        className="min-h-[600px]"
      />
    </div>
  );
}