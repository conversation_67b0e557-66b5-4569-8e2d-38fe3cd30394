import React from "react";

interface PaymentStatusBadgeProps {
  status: "payment_in_progress" | "future_payment" | "completed" | "cancelled";
  displayText?: string;
  showTooltip?: boolean;
  size?: "sm" | "md" | "lg";
}

const PaymentStatusBadge: React.FC<PaymentStatusBadgeProps> = ({
  status,
  displayText,
  showTooltip = true,
  size = "md",
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case "payment_in_progress":
        return {
          color: "bg-yellow-100 text-yellow-800 border-yellow-200",
          text: displayText || "Pagamento in corso",
          icon: "⏳",
          tooltip: "Il pagamento è in elaborazione nel ciclo attuale",
        };
      case "future_payment":
        return {
          color: "bg-blue-100 text-blue-800 border-blue-200",
          text: displayText || "Pagamento futuro",
          icon: "📅",
          tooltip: "Pagamento pianificato per il prossimo ciclo",
        };
      case "completed":
        return {
          color: "bg-green-100 text-green-800 border-green-200",
          text: displayText || "Completato",
          icon: "✅",
          tooltip: "Pagamento completato",
        };
      case "cancelled":
        return {
          color: "bg-red-100 text-red-800 border-red-200",
          text: displayText || "Annullato",
          icon: "❌",
          tooltip: "Pagamento annullato",
        };
      default:
        return {
          color: "bg-gray-100 text-gray-800 border-gray-200",
          text: displayText || "Sconosciuto",
          icon: "❓",
          tooltip: "Stato del pagamento sconosciuto",
        };
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "px-2 py-1 text-xs";
      case "lg":
        return "px-4 py-2 text-base";
      default:
        return "px-3 py-1 text-sm";
    }
  };

  const config = getStatusConfig();
  const sizeClasses = getSizeClasses();

  return (
    <div className="relative inline-block">
      <span
        className={`
          inline-flex items-center gap-1 rounded-full border font-medium
          ${config.color} ${sizeClasses}
        `}
        title={showTooltip ? config.tooltip : undefined}
      >
        <span className="text-xs">{config.icon}</span>
        {config.text}
      </span>
    </div>
  );
};

export default PaymentStatusBadge;
