.main {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* Ensure property cards container has proper spacing and scrolling */
.propertyCardsContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 10px;
  gap: 10px;
  flex: 1;
  overflow-y: auto;
  /* Add some margin bottom to ensure last card is visible above navbar */
  margin-bottom: 20px;
}

/* Ensure smooth scrolling on mobile devices */
@media (max-width: 768px) {
  .propertyCardsContainer {
    -webkit-overflow-scrolling: touch;
    /* Add extra padding for smaller screens */
    padding-bottom: 20px;
  }
}
