"use client";
import React, { useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import MultiSelect from "@/components/inputs/multiSelect";
import Button from "@/components/buttons/Button";
import { useRouter } from "next/navigation";

function Page() {
  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState(["1"]);

  const handleSaveAndContinue = () => {
    //Save in localstorage
    localStorage.setItem("propertyMode-crp", selectedOption[0]);

    //Redirect to next page
    router.push("/dashboard/setup/name");
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Start"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push("/dashboard");
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col justify-between">
        <MultiSelect
          title="Quante proprietà stai listando?"
          isMultiSelect={false}
          options={[
            {
              id: "1",
              title: "Proprietà singola",
              description:
                "Appartamento singolo, villa, chalet, monolocale, etc.",
            },
            //{ id: '2', title: 'Multi-unità', description: 'Varie unità allo stesso indirizzo' },
          ]}
          value={selectedOption}
          onChange={(selected) => setSelectedOption(selected)}
        />

        <Button
          color="white"
          backgroundColor="var(--blue)"
          text="Continua"
          fontSize="14px"
          onClick={() => {
            handleSaveAndContinue();
          }}
        />
      </div>
    </MobilePageStart>
  );
}

export default Page;
