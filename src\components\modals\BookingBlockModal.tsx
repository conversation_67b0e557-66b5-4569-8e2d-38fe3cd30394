import React, { useState } from "react";
import Modal from "@/components/_globals/modal";
import Title from "@/components/titles/Title";
import Button from "@/components/buttons/Button";
import {
  CalendarX,
  AlertTriangle,
  Check,
  X,
  Calendar,
  Lock,
} from "lucide-react";
import { useCalendar } from "@/components/_context/CalendarContext";

interface BookingBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  blockData: {
    id: number;
    start_date: string;
    end_date: string;
    is_active?: boolean;
    reason?: string;
  };
  onDeactivate: () => void;
}

const BookingBlockModal = ({
  isOpen,
  onClose,
  blockData,
  onDeactivate,
}: BookingBlockModalProps) => {
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { deactivateBlock } = useCalendar();

  const handleDeactivateBlock = async () => {
    try {
      setIsDeactivating(true);
      const success = await deactivateBlock(blockData.id.toString());

      if (success) {
        onDeactivate();
        onClose();
      } else {
        throw new Error("Deactivation failed");
      }
    } catch (error) {
      console.error("Error deactivating booking block:", error);
      setIsDeactivating(false);
    }
  };

  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("it-IT", {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  // Calculate the number of days in the block
  const getDaysCount = () => {
    const startDate = new Date(blockData.start_date);
    const endDate = new Date(blockData.end_date);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
    return diffDays;
  };

  if (showConfirmation) {
    return (
      <Modal isOpen={isOpen} onClose={onClose}>
        <div className="w-full flex flex-col items-center">
          {/* Warning Icon */}
          <div
            className="w-16 h-16 rounded-full flex items-center justify-center mb-6"
            style={{ backgroundColor: "#fcbd4c20" }}
          >
            <AlertTriangle size={32} style={{ color: "#fcbd4c" }} />
          </div>

          {/* Centered Title */}
          <div className="w-full mb-6">
            <Title
              title="Conferma Disattivazione"
              subtitle="Sei sicuro di voler disattivare questo blocco di prenotazione?"
              style={{ textAlign: "center" }}
            />
          </div>

          {/* Centered Date Information Container */}
          <div className="w-full max-w-md mx-auto p-6 my-4 bg-white rounded-xl border border-gray-100 shadow-sm text-center">
            <p className="text-gray-700 mb-3 font-medium">
              Questo renderà disponibili per la prenotazione le seguenti date:
            </p>
            <div
              className="py-3 px-4 rounded-lg"
              style={{ backgroundColor: "#113158", color: "white" }}
            >
              <p className="font-semibold text-sm">
                {formatDate(blockData.start_date)} -{" "}
                {formatDate(blockData.end_date)}
              </p>
              <p className="text-xs mt-1 opacity-90">
                ({getDaysCount()} giorni)
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex w-full gap-4 px-6 mt-4">
            <div style={{ width: "50%" }}>
              <Button
                text="Annulla"
                color="#113158"
                backgroundColor="#f8f9fa"
                onClick={() => setShowConfirmation(false)}
              />
            </div>
            <div style={{ width: "50%" }}>
              <Button
                text={
                  isDeactivating ? "Disattivazione in corso..." : "Conferma"
                }
                color="white"
                backgroundColor="#ef4444"
                onClick={handleDeactivateBlock}
                disabled={isDeactivating}
              />
            </div>
          </div>
        </div>
      </Modal>
    );
  }

  // Main modal content
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-full flex flex-col items-center">
        {/* Lock Icon */}
        <div
          className="w-16 h-16 rounded-full flex items-center justify-center mb-6"
          style={{ backgroundColor: "#fcbd4c20" }}
        >
          <Lock size={32} style={{ color: "#fcbd4c" }} />
        </div>

        {/* Centered Title */}
        <div className="w-full mb-6">
          <Title
            title="Attenzione"
            subtitle="Un blocco prenotazione è attivo in questo giorno"
            style={{ textAlign: "center" }}
          />
        </div>

        {/* Centered Information Container */}
        <div className="w-full max-w-lg mx-auto p-6 my-4 bg-white rounded-xl border border-gray-100 shadow-sm">
          {/* Status indicator */}
          <div className="flex items-center mb-5 pb-4 border-b border-gray-100">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
              style={{
                backgroundColor:
                  blockData.is_active !== false ? "#fcbd4c20" : "#f3f4f6",
              }}
            >
              {blockData.is_active !== false ? (
                <Check size={22} style={{ color: "#fcbd4c" }} />
              ) : (
                <X size={22} className="text-gray-500" />
              )}
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium" style={{ color: "#113158" }}>
                Stato
              </p>
              <p className="font-semibold text-gray-800 text-base">
                {blockData.is_active !== false ? "Attivo" : "Inattivo"}
              </p>
            </div>
          </div>

          {/* Date range */}
          <div className="flex items-center mb-5 pb-4 border-b border-gray-100">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
              style={{ backgroundColor: "#113158" }}
            >
              <Calendar size={22} color="white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium" style={{ color: "#113158" }}>
                Periodo Bloccato
              </p>
              <p className="font-semibold text-gray-800 text-base">
                {formatDate(blockData.start_date)} -{" "}
                {formatDate(blockData.end_date)}
              </p>
              <p className="text-sm text-gray-600 mt-1 font-medium">
                ({getDaysCount()} giorni)
              </p>
            </div>
          </div>

          {/* Reason if available */}
          {blockData.reason && (
            <div className="flex items-center mb-5 pb-4 border-b border-gray-100">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
                style={{ backgroundColor: "#fcbd4c20" }}
              >
                <AlertTriangle size={22} style={{ color: "#fcbd4c" }} />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium" style={{ color: "#113158" }}>
                  Motivo
                </p>
                <p className="font-semibold text-gray-800 text-base">
                  {blockData.reason}
                </p>
              </div>
            </div>
          )}

          {/* Availability message */}
          <div className="flex items-center">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center mr-4"
              style={{
                backgroundColor:
                  blockData.is_active !== false ? "#ef444420" : "#10b98120",
              }}
            >
              <CalendarX
                size={22}
                style={{
                  color: blockData.is_active !== false ? "#ef4444" : "#10b981",
                }}
              />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium" style={{ color: "#113158" }}>
                Disponibilità
              </p>
              <p className="font-semibold text-gray-800 text-base leading-relaxed">
                {blockData.is_active !== false
                  ? "Queste date non sono attualmente disponibili per la prenotazione"
                  : "Questo blocco è inattivo e non influisce sulla disponibilità"}
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex w-full gap-4 px-6 mt-6">
          <div style={{ width: "50%" }}>
            <Button
              text="Chiudi"
              color="#113158"
              backgroundColor="#f8f9fa"
              onClick={onClose}
            />
          </div>
          <div style={{ width: "50%" }}>
            <Button
              text="Disattiva Blocco"
              color="white"
              backgroundColor="#ef4444"
              onClick={() => setShowConfirmation(true)}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default BookingBlockModal;
