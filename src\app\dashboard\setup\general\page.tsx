"use client";
import React, { useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import MultiSelect from "@/components/inputs/multiSelect";
import Button from "@/components/buttons/Button";
import InputCounter from "@/components/inputs/inputCounter";
import { useRouter } from "next/navigation";

function Page() {
  const router = useRouter();

  const [selectedOption, setSelectedOption] = useState(["1"]);
  const [guests, setGuests] = useState(1);

  const handleSaveAndContinue = () => {
    //Save in localstorage
    localStorage.setItem("propertyType-crp", selectedOption[0]);
    localStorage.setItem("guests-crp", guests.toString());

    //Redirect to next page
    router.push("/dashboard/setup/location");
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Generali"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push("/dashboard");
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col justify-between">
        <div className="flex flex-col gap-4">
          <MultiSelect
            title="Che tipo di proprietà è?"
            isMultiSelect={false}
            options={[
              { id: "1", title: "Hotel" },
              { id: "2", title: "Motel" },
              { id: "3", title: "Casa Vacanze" },
            ]}
            value={selectedOption}
            onChange={(selected) => setSelectedOption(selected)}
          />

          <InputCounter
            label="Quanti ospiti può ospitare la tua struttura?"
            value={guests}
            onChange={setGuests}
          />
        </div>

        <Button
          color="white"
          backgroundColor="var(--blue)"
          text="Continua"
          fontSize="14px"
          onClick={() => {
            handleSaveAndContinue();
          }}
        />
      </div>
    </MobilePageStart>
  );
}

export default Page;
