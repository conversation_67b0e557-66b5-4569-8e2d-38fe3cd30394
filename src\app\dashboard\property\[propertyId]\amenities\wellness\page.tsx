"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = React.useState({
    poolgated: false,
    poolkids: false,
    outdoorshower: false,
    beachtowels: false,
    gymaccess: false,
    gymtools: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: "Poolgated",
        category: "Wellness",
        is_available: data.poolgated ?? false,
      },
      {
        name: "Poolkids",
        category: "Wellness",
        is_available: data.poolkids ?? false,
      },
      {
        name: "Outdoorshower",
        category: "Wellness",
        is_available: data.outdoorshower ?? false,
      },
      {
        name: "beachtowels",
        category: "Wellness",
        is_available: data.beachtowels ?? false,
      },
      {
        name: "Gymaccess",
        category: "Wellness",
        is_available: data.gymaccess ?? false,
      },
      {
        name: "Gymtools",
        category: "Wellness",
        is_available: data.gymtools ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };
  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;

    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Piscina & Wellness"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Piscina con barriera "
            value={data.poolgated}
            onChange={(value) => {
              setData({
                ...data,
                poolgated: value,
              });
              handleUpdateAmenities({
                ...data,
                poolgated: value,
              });
            }}
          />

          <InputOnOff
            title="Piscina per bambini"
            value={data.poolkids}
            onChange={(value) => {
              setData({
                ...data,
                poolkids: value,
              });
              handleUpdateAmenities({
                ...data,
                poolkids: value,
              });
            }}
          />

          <InputOnOff
            title="Doccia esterna"
            value={data.outdoorshower}
            onChange={(value) => {
              setData({
                ...data,
                outdoorshower: value,
              });
              handleUpdateAmenities({
                ...data,
                outdoorshower: value,
              });
            }}
          />

          <InputOnOff
            title="Teli mare"
            value={data.beachtowels}
            onChange={(value) => {
              setData({
                ...data,
                beachtowels: value,
              });
              handleUpdateAmenities({
                ...data,
                beachtowels: value,
              });
            }}
          />

          <InputOnOff
            title="Accesso Palestra"
            value={data.gymaccess}
            onChange={(value) => {
              setData({
                ...data,
                gymaccess: value,
              });
              handleUpdateAmenities({
                ...data,
                gymaccess: value,
              });
            }}
          />

          <InputOnOff
            title="Atrezzi"
            value={data.gymtools}
            onChange={(value) => {
              setData({
                ...data,
                gymtools: value,
              });
              handleUpdateAmenities({
                ...data,
                gymtools: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
