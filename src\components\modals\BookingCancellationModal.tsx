import React, { useState } from "react";
import { XCircle } from "lucide-react";
import { requestBookingCancellation } from "@/services/api";
import { useRouter } from "next/navigation";
import { showNavbarSafeToast } from "@/components/toast/NavbarSafeToast";

interface BookingCancellationModalProps {
  isOpen: boolean;
  onClose: () => void;
  bookingId: string;
  isManual: boolean;
  onSuccess?: () => void; // Optional callback for successful cancellation
}

const BookingCancellationModal: React.FC<BookingCancellationModalProps> = ({
  isOpen,
  onClose,
  bookingId,
  isManual,
  onSuccess,
}) => {
  const [reason, setReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  // Validation states
  const [isTouched, setIsTouched] = useState(false);
  const isValid = reason.trim().length >= 20;
  const showError = isTouched && !isValid;

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate input only for non-manual bookings
    if (!isManual) {
      setIsTouched(true);
      if (!isValid) {
        setError("La motivazione deve contenere almeno 20 caratteri.");
        return;
      }
    }

    setIsSubmitting(true);
    setError("");

    try {
      const response = await requestBookingCancellation(bookingId, reason);

      // Check if the request was successful based on HTTP status
      if (response.success && response.status >= 200 && response.status < 300) {
        // Success case
        showNavbarSafeToast({
          message: isManual
            ? "Prenotazione annullata con successo"
            : "Richiesta di cancellazione inviata con successo",
          type: "success",
          duration: 3000,
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }

        onClose();

        // Small delay to ensure toast is visible before navigation
        setTimeout(() => {
          router.push("/dashboard/bookings");
        }, 500);
      } else {
        // Error case - extract error message from response
        const errorMessage =
          response.data?.error ||
          response.data?.detail ||
          response.data?.message ||
          "Errore durante la richiesta di cancellazione";

        setError(errorMessage);
        showNavbarSafeToast({
          message: errorMessage,
          type: "error",
          duration: 5000,
        });
      }
    } catch (error: any) {
      // Handle network errors or other unexpected errors
      console.error("Error cancelling booking:", error);

      const errorMessage =
        error.response?.data?.message ||
        error.response?.data?.error ||
        error.message ||
        "Si è verificato un errore durante la cancellazione";

      setError(errorMessage);
      showNavbarSafeToast({
        message: errorMessage,
        type: "error",
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white w-11/12 max-w-md rounded-lg shadow-xl overflow-hidden">
        <div className="bg-red-500 p-4 text-white">
          <h3 className="text-lg font-bold flex items-center gap-2">
            <XCircle className="w-5 h-5" />
            {isManual ? "Annulla prenotazione" : "Richiedi cancellazione"}
          </h3>
        </div>

        <form onSubmit={handleSubmit} className="p-4">
          {isManual ? (
            <div className="mb-6">
              <p className="text-gray-700 mb-4">
                Sei sicuro di voler annullare questa prenotazione manuale?
              </p>
              <div className="bg-amber-50 border-l-4 border-amber-400 p-4 rounded">
                <p className="text-amber-800 text-sm">
                  <strong>Attenzione:</strong>
                  La prenotazione verrà immediatamente cancellata dal sistema.
                </p>
              </div>
            </div>
          ) : (
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Motivo della cancellazione*
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                onBlur={() => setIsTouched(true)}
                className={`w-full p-3 border rounded-md ${
                  showError ? "border-red-500 bg-red-50" : "border-gray-300"
                }`}
                rows={4}
                placeholder="Inserisci il motivo della cancellazione (minimo 20 caratteri)"
              ></textarea>

              <div className="flex justify-between mt-1">
                {showError ? (
                  <p className="text-sm text-red-600">
                    La motivazione deve contenere almeno 20 caratteri.
                  </p>
                ) : (
                  <p className="text-sm text-gray-500">
                    {reason.length} / 20+ caratteri
                  </p>
                )}
              </div>

              <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mt-3 rounded">
                <p className="text-blue-800 text-sm">
                  <strong>Nota:</strong> La richiesta di cancellazione verrà
                  inviata al channel manager. Lo stato della prenotazione
                  potrebbe non cambiare immediatamente.
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="p-3 mb-4 bg-red-50 border-l-4 border-red-500 text-red-700 text-sm">
              {error}
            </div>
          )}

          <div className="flex justify-center gap-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Annulla
            </button>
            <button
              type="submit"
              disabled={isSubmitting || (!isManual && !isValid)}
              className={`px-4 py-2 rounded-md text-white transition-colors ${
                isSubmitting || (!isManual && !isValid)
                  ? "bg-red-300 cursor-not-allowed"
                  : "bg-red-500 hover:bg-red-600"
              }`}
            >
              {isSubmitting
                ? "Invio in corso..."
                : isManual
                  ? "Conferma Cancellazione"
                  : "Invia Richiesta"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BookingCancellationModal;
