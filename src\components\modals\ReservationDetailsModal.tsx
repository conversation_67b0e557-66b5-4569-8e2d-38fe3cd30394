import React from "react";
import Modal from "@/components/_globals/modal";
import Button from "@/components/buttons/Button";
import { Users, CreditCard, Clock, Bed, CalendarRange } from "lucide-react";

interface ReservationDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  reservationData: {
    reservation_data: {
      guest_name: string;
      number_of_guests: number;
      total_price: string;
      checkin_date: string;
      checkout_date: string;
      payment_type?: string;
      booked_at?: string;
    };
  };
}

const ReservationDetailsModal = ({
  isOpen,
  onClose,
  reservationData,
}: ReservationDetailsModalProps) => {
  const { reservation_data } = reservationData;

  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("it-IT", {
      weekday: "short",
      month: "long",
      day: "numeric",
      year: "numeric",
    });
  };

  // Format currency
  const formatCurrency = (amount: string) => {
    const numAmount = parseFloat(amount);
    return new Intl.NumberFormat("it-IT", {
      style: "currency",
      currency: "EUR",
    }).format(numAmount);
  };

  // Calculate the number of nights
  const getNightsCount = () => {
    const startDate = new Date(reservation_data.checkin_date);
    const endDate = new Date(reservation_data.checkout_date);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  // Format date in short format
  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("it-IT", {
      day: "numeric",
      month: "short",
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-full flex flex-col items-center">
        <div className="w-12 h-12 rounded-full bg-[#133157]/10 flex items-center justify-center mb-2">
          <Bed size={24} className="text-[#133157]" />
        </div>

        <h3 className="text-lg font-semibold mb-3 text-[#133157]">
          Dettagli Prenotazione
        </h3>

        <div className="w-full p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* Compact info grid */}
          <div className="grid grid-cols-2 gap-3">
            {/* Guest Name */}
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-[#133157]/10 flex items-center justify-center mr-2">
                <Users size={16} className="text-[#133157]" />
              </div>
              <div className="overflow-hidden">
                <p className="font-medium text-gray-800 text-sm truncate">
                  {reservation_data.guest_name}
                </p>
                <p className="text-xs text-gray-500">
                  {reservation_data.number_of_guests} ospiti
                </p>
              </div>
            </div>

            {/* Price */}
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-[#133157]/10 flex items-center justify-center mr-2">
                <CreditCard size={16} className="text-[#133157]" />
              </div>
              <div>
                <p className="font-medium text-gray-800 text-sm">
                  {formatCurrency(reservation_data.total_price)}
                </p>
              </div>
            </div>

            {/* Stay Dates */}
            <div className="flex items-center col-span-2">
              <div className="w-8 h-8 rounded-full bg-[#133157]/10 flex items-center justify-center mr-2">
                <CalendarRange size={16} className="text-[#133157]" />
              </div>
              <div>
                <p className="font-medium text-gray-800 text-sm">
                  {formatShortDate(reservation_data.checkin_date)} -{" "}
                  {formatShortDate(reservation_data.checkout_date)}
                  <span className="text-xs text-gray-500 ml-1">
                    ({getNightsCount()} notti)
                  </span>
                </p>
              </div>
            </div>

            {/* Booking Date - only show if available */}
            {reservation_data.booked_at && (
              <div className="flex items-center col-span-2">
                <div className="w-8 h-8 rounded-full bg-[#133157]/10 flex items-center justify-center mr-2">
                  <Clock size={16} className="text-[#133157]" />
                </div>
                <div>
                  <p className="text-xs text-gray-500">
                    Prenotato: {formatShortDate(reservation_data.booked_at)}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="w-full mt-4">
          <Button
            text="Chiudi"
            onClick={onClose}
            color="white"
            backgroundColor="#133157"
          />
        </div>
      </div>
    </Modal>
  );
};

export default ReservationDetailsModal;
