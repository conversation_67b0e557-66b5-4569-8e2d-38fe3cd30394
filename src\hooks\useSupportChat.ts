import { useState, useEffect, useRef, useCallback } from "react";
import { SupportChatWebSocket } from "@/services/SupportChatWebSocket";
import {
  SupportMessage,
  ConnectionStatus,
  ChatPaginationResponse,
  TypingStatus,
  PendingMessage,
} from "@/types/support";
import { getChatMessages } from "@/services/api";

interface UseSupportChatProps {
  token: string;
  enabled?: boolean;
  onMessageConfirmed?: (
    clientMessageId: string,
    serverMessageId: string,
  ) => void;
}

interface UseSupportChatReturn {
  messages: SupportMessage[];
  connectionStatus: ConnectionStatus;
  sendMessage: (message: string) => {
    success: boolean;
    messageId: string | null;
  };
  disconnect: () => void;
  clearMessages: () => void;
  isConnecting: boolean;
  loadMore: () => void;
  hasMore: boolean;
  isLoadingMore: boolean;
  chatId: string | null;
  isInitializing: boolean;
  typingStatus: TypingStatus;
  handleUserTyping: () => void;
  markMessagesAsRead: () => void;
  wsRef: React.RefObject<SupportChatWebSocket>; // Expose WebSocket reference
}

export const useSupportChat = ({
  token,
  enabled = true,
  onMessageConfirmed,
}: UseSupportChatProps): UseSupportChatReturn => {
  const [messages, setMessages] = useState<SupportMessage[]>([]);
  const [chatId, setChatId] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    reconnectAttempts: 0,
  });
  const [isConnecting, setIsConnecting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [nextPage, setNextPage] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [typingStatus, setTypingStatus] = useState<TypingStatus>({
    isTyping: false,
    userIds: [],
  });

  const wsRef = useRef<SupportChatWebSocket | null>(null);
  const hasInitialized = useRef(false);
  const loadMessages = useCallback(async (url?: string) => {
    try {
      console.log("🔄 Loading messages from:", url || "initial endpoint");
      const response: ChatPaginationResponse = await getChatMessages(url);
      console.log("📨 Messages response:", response);

      if (url) {
        // Loading more messages (append to beginning)
        // Don't reverse - backend already sends earliest messages first
        setMessages((prev) => [...response.results, ...prev]);
      } else {
        // Initial load - keep original order (earliest first)
        setMessages(response.results);

        // Extract chat ID from first message if available using current state
        setChatId((currentChatId) => {
          if (response.results.length > 0 && !currentChatId) {
            const extractedChatId = response.results[0].chat_id;
            console.log("📝 Extracted chat ID from messages:", extractedChatId);
            return extractedChatId;
          }
          return currentChatId;
        });
      }

      setNextPage(response.next);
      setHasMore(!!response.next);
    } catch (error) {
      console.error("❌ Error loading messages:", error);
    }
  }, []); // No dependencies needed - function is stable

  const loadMore = useCallback(async () => {
    if (!nextPage || isLoadingMore) return;

    setIsLoadingMore(true);
    try {
      await loadMessages(nextPage);
    } finally {
      setIsLoadingMore(false);
    }
  }, [nextPage, isLoadingMore, loadMessages]);
  const initializeWebSocket = useCallback(() => {
    if (!chatId || !token || !enabled || hasInitialized.current) {
      return;
    }

    setIsConnecting(true);
    hasInitialized.current = true;

    const ws = new SupportChatWebSocket(chatId, token);

    // Set up event handlers
    ws.onConnectionStatusChange = (status: ConnectionStatus) => {
      setConnectionStatus(status);
      setIsConnecting(false);

      if (status.isConnected) {
        console.log("✅ Support chat connected successfully");
      } else if (status.error) {
        console.error("❌ Support chat connection error:", status.error);
      }
    };
    ws.onMessageReceived = (message: SupportMessage) => {
      setMessages((prev) => {
        // Prevent duplicate messages
        if (prev.some((m) => m.id === message.id)) {
          return prev;
        }
        console.log("Adding new message to chat:", message);
        // Add new message to the end (preserving chronological order)
        return [...prev, message];
      });
    };
    ws.onTypingStatusChange = (status: TypingStatus) => {
      setTypingStatus(status);
    };

    ws.onError = (error: string) => {
      console.error("Support chat error:", error);
      setConnectionStatus((prev) => ({
        ...prev,
        error,
      }));
      setIsConnecting(false);
    };

    // Add handler for confirmed messages
    ws.onMessageConfirmed = (clientMessageId, serverMessageId) => {
      console.log(
        `Message confirmed: ${clientMessageId} -> ${serverMessageId}`,
      );
      // Update UI with confirmation
      if (onMessageConfirmed) {
        onMessageConfirmed(clientMessageId, serverMessageId);
      }
    };

    // Add handler for read status updates
    ws.onMessageReadStatusUpdate = (messageId: string, isRead: boolean) => {
      console.log(`Message read status updated: ${messageId} -> ${isRead}`);
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, is_read: isRead } : msg,
        ),
      );
    };

    ws.onMessagesReadStatusUpdate = (messageIds: string[], isRead: boolean) => {
      console.log(
        `Multiple messages read status updated: ${messageIds.length} messages -> ${isRead}`,
      );
      setMessages((prev) =>
        prev.map((msg) =>
          messageIds.includes(msg.id) ? { ...msg, is_read: isRead } : msg,
        ),
      );
    };

    wsRef.current = ws;
    ws.connect();
  }, [chatId, token, enabled, onMessageConfirmed]);
  interface SendMessageResult {
    success: boolean;
    messageId: string | null;
  }

  const sendMessage = useCallback(
    (message: string): SendMessageResult => {
      if (!message.trim()) {
        console.warn("Cannot send empty message");
        return { success: false, messageId: null };
      }

      // Check if WebSocket is available and connected
      if (wsRef.current && connectionStatus.isConnected) {
        try {
          console.log("Sending message via WebSocket");
          const messageId = wsRef.current.sendChatMessage(message);

          if (messageId) {
            console.log("Message sent via WebSocket with ID:", messageId);
            return { success: true, messageId };
          } else {
            console.error("Failed to send message via WebSocket");
            return { success: false, messageId: null };
          }
        } catch (error) {
          console.error("Error sending message via WebSocket:", error);
          return { success: false, messageId: null };
        }
      } else {
        console.warn(
          "WebSocket not available or not connected, message will need to use API fallback",
        );
        return { success: false, messageId: null };
      }
    },
    [connectionStatus.isConnected],
  );

  const handleUserTyping = useCallback(() => {
    if (wsRef.current && connectionStatus.isConnected) {
      wsRef.current.handleUserTyping();
    }
  }, [connectionStatus.isConnected]);

  // Mark messages as read when user opens chat
  const markMessagesAsRead = useCallback(() => {
    if (wsRef.current && connectionStatus.isConnected && messages.length > 0) {
      // Find unread messages from support
      const unreadSupportMessages = messages
        .filter((msg) => msg.sender === "support" && !msg.is_read)
        .map((msg) => msg.id);

      if (unreadSupportMessages.length > 0) {
        console.log("Marking support messages as read:", unreadSupportMessages);
        wsRef.current.markMessagesAsRead(unreadSupportMessages);
      }
    }
  }, [connectionStatus.isConnected, messages]);

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.disconnect();
      wsRef.current = null;
    }
    hasInitialized.current = false;
    setConnectionStatus({ isConnected: false });
    setIsConnecting(false);
  }, []);
  const clearMessages = useCallback(() => {
    setMessages([]);
  }, []); // Load initial messages on mount
  useEffect(() => {
    console.log("🚀 Initial load effect triggered:", {
      enabled,
      token: !!token,
    });
    if (enabled && token) {
      console.log("✅ Starting initial message load...");
      setIsInitializing(true);
      loadMessages().finally(() => {
        console.log("🏁 Initial message load completed");

        // Ensure messages are in chronological order (oldest to newest)
        // In case the API returns them in a different order
        setMessages((currentMessages) => {
          const sortedMessages = [...currentMessages].sort((a, b) => {
            return (
              new Date(a.created_at).getTime() -
              new Date(b.created_at).getTime()
            );
          });

          console.log("📋 Ensured messages are in chronological order");
          return sortedMessages;
        });

        setIsInitializing(false);
      });
    } else {
      console.log("❌ Initial load skipped - missing requirements:", {
        enabled,
        hasToken: !!token,
      });
    }
  }, [enabled, token, loadMessages]);

  // Initialize WebSocket when we have a valid chat ID
  useEffect(() => {
    if (enabled && chatId && token && chatId !== "pending") {
      if (wsRef.current) {
        // Update existing WebSocket with new chat ID
        wsRef.current.updateChatId(chatId);
      } else {
        // Initialize new WebSocket
        initializeWebSocket();
      }
    }

    return () => {
      if (!chatId || chatId === "pending") {
        disconnect();
      }
    };
  }, [chatId, token, enabled, initializeWebSocket, disconnect]);

  // Auto-mark messages as read when chat is opened or new support messages arrive
  useEffect(() => {
    // Only mark messages as read if we have a connection and messages
    if (connectionStatus.isConnected && messages.length > 0) {
      // Use a small delay to ensure the user has actually "seen" the messages
      const timer = setTimeout(() => {
        markMessagesAsRead();
      }, 1000); // 1 second delay

      return () => clearTimeout(timer);
    }
  }, [connectionStatus.isConnected, messages.length, markMessagesAsRead]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);
  return {
    messages,
    connectionStatus,
    sendMessage,
    disconnect,
    clearMessages,
    isConnecting,
    loadMore,
    hasMore,
    isLoadingMore,
    chatId,
    isInitializing,
    typingStatus,
    handleUserTyping,
    markMessagesAsRead,
    wsRef, // Expose WebSocket reference for file upload service
  };
};
