import React from "react";
import { ChevronRightIcon } from "@heroicons/react/16/solid";
import { useRouter } from "next/navigation";

interface Props {
  title: string;
  desciption?: string;
  href: string;
  completed?: boolean;
  status?: "required" | "completed" | "pending";
  statusColor?: string;
  disabled?: boolean;
  onClick?: () => void;
}

function ButtonSection({
  title,
  desciption,
  href,
  completed = false,
  status = "completed",
  statusColor = "var(--accent)",
  disabled = false,
  onClick,
}: Props) {
  const router = useRouter();

  const handleClick = () => {
    if (disabled) return;

    if (onClick) {
      onClick();
    } else {
      router.push(href);
    }
  };

  return (
    <div
      className={`flex justify-between items-center p-3 rounded-lg ${disabled ? "cursor-not-allowed" : "cursor-pointer"}`}
      style={{
        background: disabled ? "#f5f5f5" : "white",
        boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.05)",
        border: disabled
          ? "1px solid #e0e0e0"
          : status === "required"
            ? "1px solid red"
            : "1px solid #e0e0e0",
        opacity: disabled ? 0.7 : 1,
      }}
      onClick={handleClick}
    >
      <div className="flex flex-col gap-[2px]">
        <p className="text-[14px]" style={{ fontWeight: "500" }}>
          {title}
        </p>
        {desciption && (
          <p className="text-[12px] text-gray-500">{desciption}</p>
        )}
      </div>
      <div className="flex items-center">
        {status === "required" && !disabled && (
          <div
            className="mr-2 px-2 py-1 rounded-full text-xs"
            style={{
              color: "white",
              backgroundColor: statusColor,
            }}
          >
            Richiesto
          </div>
        )}
        {disabled && (
          <div
            className="mr-2 px-2 py-1 rounded-full text-xs"
            style={{
              color: "white",
              backgroundColor: "#9e9e9e",
            }}
          >
            Disabilitato
          </div>
        )}
        <ChevronRightIcon
          width={16}
          color={disabled ? "#9e9e9e" : "currentColor"}
        />
      </div>
    </div>
  );
}

export default ButtonSection;
