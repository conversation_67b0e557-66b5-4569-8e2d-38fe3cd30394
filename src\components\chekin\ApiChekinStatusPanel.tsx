"use client";
import React, { useEffect, useState } from 'react';
import { chekinApi, type ChekinPropertyEvent, type ChekinBookingEvent } from '@/services/chekinApi';
import { StatusBadge } from '@/components/status-badge';
import { 
  UserPlus, 
  Shield, 
  BarChart3, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  ShieldOff,
  TrendingDown
} from 'lucide-react';

interface ApiChekinStatusPanelProps {
  propertyId?: string;
  bookingId?: string;
  mode?: 'property' | 'booking';
  className?: string;
}

export const ApiChekinStatusPanel: React.FC<ApiChekinStatusPanelProps> = ({ 
  propertyId,
  bookingId,
  mode = 'property',
  className = "" 
}) => {
  const [propertyData, setPropertyData] = useState<ChekinPropertyEvent | null>(null);
  const [bookingData, setBookingData] = useState<ChekinBookingEvent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        if (mode === 'property' && propertyId) {
          const properties = await chekinApi.getEventsByProperty(propertyId);
          setPropertyData(properties[0] || null);
        } else if (mode === 'booking' && bookingId) {
          const bookings = await chekinApi.getEventsByBooking(bookingId);
          setBookingData(bookings[0] || null);
        }
      } catch (err) {
        setError('Failed to load Chekin status');
        console.error('Error fetching Chekin data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [propertyId, bookingId, mode]);

  const getGuestIcon = () => UserPlus;
  const getPoliceIcon = () => Shield;
  const getStatIcon = () => BarChart3;

  const getGuestVariant = () => {
    if (mode === 'property' && propertyData) {
      return propertyData.has_active_guest ? 'green' : 'yellow';
    }
    if (mode === 'booking' && bookingData) {
      return bookingData.guest_created ? 'green' : 'yellow';
    }
    return 'default';
  };

  const getPoliceVariant = () => {
    if (mode === 'property' && propertyData) {
      const status = propertyData.police_registration_status;
      const connected = propertyData.police_connected;
      
      if (!connected) return 'black';
      if (status === 'complete') return 'green';
      if (status === 'error') return 'red';
      return 'yellow';
    }
    if (mode === 'booking' && bookingData) {
      // Derive status from booking events
      const policeEvents = bookingData.events.filter(e => e.event_type.includes('Police'));
      if (policeEvents.length === 0) return 'yellow';
      
      const latestEvent = policeEvents[policeEvents.length - 1];
      if (latestEvent.event_type === 'PoliceRegistration.complete') return 'green';
      if (latestEvent.event_type === 'PoliceRegistration.error') return 'red';
      if (latestEvent.event_type === 'PoliceAccount.disconnected') return 'black';
      return 'yellow';
    }
    return 'default';
  };

  const getStatVariant = () => {
    if (mode === 'property' && propertyData) {
      const status = propertyData.stat_registration_status;
      const connected = propertyData.stat_connected;
      
      if (!connected) return 'black';
      if (status === 'complete') return 'green';
      if (status === 'error') return 'red';
      return 'yellow';
    }
    if (mode === 'booking' && bookingData) {
      // Derive status from booking events
      const statEvents = bookingData.events.filter(e => e.event_type.includes('Stat'));
      if (statEvents.length === 0) return 'yellow';
      
      const latestEvent = statEvents[statEvents.length - 1];
      if (latestEvent.event_type === 'StatRegistration.complete') return 'green';
      if (latestEvent.event_type === 'StatRegistration.error') return 'red';
      if (latestEvent.event_type === 'StatAccount.disconnected') return 'black';
      return 'yellow';
    }
    return 'default';
  };

  const getStatusText = (type: 'guest' | 'police' | 'stat') => {
    const variants = {
      guest: getGuestVariant(),
      police: getPoliceVariant(),
      stat: getStatVariant()
    };
    
    const variant = variants[type];
    const statusMap: Record<string, string> = {
      green: 'Completato',
      yellow: 'In corso',
      red: 'Errore',
      black: 'Disconnesso',
      default: 'Sconosciuto'
    };
    
    return statusMap[variant] || 'Sconosciuto';
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg p-4 border border-red-200 ${className}`}>
        <div className="text-red-600 text-sm">
          <p className="font-medium">Errore nel caricamento dei dati Chekin</p>
          <p className="text-xs mt-1">{error}</p>
        </div>
      </div>
    );
  }

  // Don't show status badges if we don't have data
  const hasData = (mode === 'property' && propertyData) || (mode === 'booking' && bookingData);
  
  if (!hasData && !loading) {
    return (
      <div className={`bg-white rounded-lg p-4 border border-gray-200 ${className}`}>
        <div className="text-gray-600 text-sm">
          <p className="font-medium">Nessun dato Chekin disponibile</p>
          <p className="text-xs mt-1">I dati di registrazione non sono ancora disponibili per questa {mode === 'property' ? 'proprietà' : 'prenotazione'}.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg p-4 ${className}`}>
      <h3 className="text-base font-semibold mb-4 text-[#133157]">
        Stato Registrazioni Chekin
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Guest Registration */}
        <div className="flex flex-col items-center space-y-2">
          <StatusBadge 
            icon={getGuestIcon()}
            variant={getGuestVariant()}
            size="sm"
          />
          <div className="text-center">
            <h4 className="text-xs font-medium text-gray-900">
              Ospite
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText('guest')}
            </p>
          </div>
        </div>

        {/* Police Registration */}
        <div className="flex flex-col items-center space-y-2">
          <StatusBadge 
            icon={getPoliceIcon()}
            variant={getPoliceVariant()}
            size="sm"
          />
          <div className="text-center">
            <h4 className="text-xs font-medium text-gray-900">
              Alloggiati
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText('police')}
            </p>
          </div>
        </div>

        {/* ISTAT Registration */}
        <div className="flex flex-col items-center space-y-2">
          <StatusBadge 
            icon={getStatIcon()}
            variant={getStatVariant()}
            size="sm"
          />
          <div className="text-center">
            <h4 className="text-xs font-medium text-gray-900">
              ISTAT
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText('stat')}
            </p>
          </div>
        </div>
      </div>

      {/* Status Summary */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600">Eventi Totali:</span>
          <span className="font-medium text-[#133157]">
            {mode === 'property' ? propertyData?.total_events || 0 : bookingData?.total_events || 0}
          </span>
        </div>
        
        {(propertyData?.latest_guest_event || bookingData?.latest_status_update) && (
          <div className="flex items-center justify-between text-xs text-gray-500 mt-1">
            <span>Ultimo Aggiornamento:</span>
            <span>
              {mode === 'property' 
                ? new Date(propertyData?.latest_guest_event?.created_at || '').toLocaleDateString('it-IT')
                : new Date(bookingData?.latest_status_update || '').toLocaleDateString('it-IT')
              }
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiChekinStatusPanel;