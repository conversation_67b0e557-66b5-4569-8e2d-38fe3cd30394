"use client";
import React, { useEffect, useState, useContext } from "react";
import { propertyDeleteProperty } from "@/services/api";
import { UserContext } from "@/components/_context/UserContext";
import { PropertyContext } from "@/components/_context/PropertyContext";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import { useRouter } from "next/navigation";
import { AlertTriangle, Trash2 } from "lucide-react";
import toast from "react-hot-toast";
import Loader1 from "@/components/loaders/Loader1";
import Modal from "@/components/_globals/modal";
import Title from "@/components/titles/Title";
import Button from "@/components/buttons/Button";

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  propertyName: string;
  onConfirm: () => Promise<void>;
  isDeleting: boolean;
}

const ConfirmationModal = ({
  isOpen,
  onClose,
  propertyName,
  onConfirm,
  isDeleting,
}: ConfirmationModalProps) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-full flex flex-col items-center">
        <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-4">
          <AlertTriangle size={32} className="text-red-500" />
        </div>

        <Title
          title="Conferma Disattivazione"
          subtitle={`Sei sicuro di voler disattivare la proprietà "${propertyName}"?`}
          style={{ textAlign: "center" }}
        />

        <div className="w-full px-6 py-4 text-center">
          <p className="text-gray-600 mb-4">
            Questa azione non può essere annullata. La proprietà verrà rimossa
            dal tuo account e tutti i dati associati potrebbero essere
            eliminati.
          </p>
        </div>

        <div className="flex w-full gap-4 px-6">
          <div style={{ width: "50%" }}>
            <Button
              text="Annulla"
              color="black"
              backgroundColor="#f3f4f6"
              onClick={onClose}
            />
          </div>
          <div style={{ width: "50%" }}>
            <Button
              text={
                isDeleting
                  ? "Disattivazione in corso..."
                  : "Conferma Disattivazione"
              }
              color="white"
              backgroundColor="#ef4444"
              onClick={onConfirm}
              disabled={isDeleting}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

function Page() {
  const router = useRouter();
  const { staffProperties } = useContext(UserContext);
  const { properties, fetchProperties, isLoadingProperties } =
    useContext(PropertyContext);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedProperty, setSelectedProperty] = useState<any>(null);
  const [showConfirmationModal, setShowConfirmationModal] =
    useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await fetchProperties();
      } catch (error) {
        console.error("Error fetching properties:", error);
        toast.error("Errore durante il caricamento delle proprietà");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Check if user has owner permission for a specific property
  const hasOwnerPermission = (propertyId: string) => {
    if (!staffProperties) return false;

    const property = staffProperties.find((p) => p.property_id === propertyId);
    if (!property) return false;

    return property.permissions.includes("owner");
  };

  const handleDeleteClick = (property: any) => {
    if (!hasOwnerPermission(property.id)) {
      toast.error(
        "Non hai i permessi necessari per disattivare questa proprietà",
      );
      return;
    }

    if (property.is_active === false) {
      toast.error(
        "Questa proprietà è già stata disattivata e verrà eliminata dal sistema a breve. Contatta il supporto se desideri annullare la richiesta di disattivazione.",
        {
          duration: 5000, // Show for 5 seconds
          style: {
            maxWidth: "500px",
            padding: "16px",
          },
        },
      );
      return;
    }

    setSelectedProperty(property);
    setShowConfirmationModal(true);
  };

  const handleConfirmDelete = async () => {
    if (!selectedProperty) return;

    setIsDeleting(true);
    try {
      const result = await propertyDeleteProperty(selectedProperty.id);

      if (result.error) {
        throw new Error(result.error);
      }

      toast.success(
        `La proprietà "${selectedProperty.name}" è stata disattivata con successo`,
      );
      setShowConfirmationModal(false);

      // Refresh the properties list
      await fetchProperties();
    } catch (error) {
      console.error("Error deleting property:", error);
      toast.error("Errore durante la disattivazione della proprietà");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Disattiva Proprietà"
        actionLeftIcon={() => {
          router.push("/dashboard/settings/account");
        }}
      />

      {isLoading || isLoadingProperties ? (
        <div className="flex-1 flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
          <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 mb-2">
            <h3 className="text-amber-800 font-medium text-sm mb-1">
              Attenzione
            </h3>
            <p className="text-amber-700 text-xs">
              La disattivazione di una proprietà è un'azione irreversibile.
              Tutti i dati associati alla proprietà potrebbero essere eliminati.
              Solo i proprietari possono disattivare una proprietà.
            </p>
          </div>

          {!Array.isArray(properties) || properties.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">Non hai proprietà da disattivare</p>
            </div>
          ) : (
            <div className="flex flex-col gap-3">
              <h4 className="text-[14px] font-medium">Le tue proprietà</h4>

              {properties.map((property: any) => {
                const canDelete =
                  hasOwnerPermission(property.id) &&
                  property.is_active !== false;
                const isInactive = property.is_active === false;

                return (
                  <div
                    key={property.id}
                    className={`flex justify-between items-center p-4 rounded-lg shadow-sm border ${
                      isInactive
                        ? "bg-gray-50 border-gray-200"
                        : "bg-white border-gray-100"
                    }`}
                    style={{
                      opacity: canDelete ? 1 : 0.7,
                      filter: isInactive ? "grayscale(100%)" : "none",
                      position: "relative",
                      overflow: "hidden",
                    }}
                  >
                    {isInactive && (
                      <div
                        className="absolute top-0 right-0 bg-gray-200 text-gray-700 px-2 py-1 text-xs transform rotate-45 translate-x-2 translate-y-1"
                        style={{
                          width: "120px",
                          textAlign: "center",
                        }}
                      >
                        Disattivata
                      </div>
                    )}
                    <div>
                      <p className="font-medium">{property.name}</p>
                      <p className="text-xs text-gray-500">
                        {property.hotel_id}
                      </p>
                      <div className="mt-1 flex gap-2">
                        {property.is_onboarded ? (
                          <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            Sincronizzata
                          </span>
                        ) : (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                            Non Sincronizzata
                          </span>
                        )}

                        {isInactive ? (
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">
                            Disattivata
                          </span>
                        ) : (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            Attiva
                          </span>
                        )}
                      </div>
                    </div>

                    <button
                      onClick={() => handleDeleteClick(property)}
                      disabled={!canDelete}
                      className={`p-2 rounded-full ${
                        canDelete
                          ? "bg-red-50 hover:bg-red-100"
                          : "bg-gray-100 cursor-not-allowed"
                      }`}
                      title={
                        isInactive
                          ? "Questa proprietà è già stata disattivata"
                          : !hasOwnerPermission(property.id)
                            ? "Non hai i permessi per disattivare questa proprietà"
                            : "Disattiva proprietà"
                      }
                    >
                      <Trash2
                        size={18}
                        color={canDelete ? "#ef4444" : "#9e9e9e"}
                      />
                    </button>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}

      {showConfirmationModal && selectedProperty && (
        <ConfirmationModal
          isOpen={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          propertyName={selectedProperty.name}
          onConfirm={handleConfirmDelete}
          isDeleting={isDeleting}
        />
      )}
    </MobilePageStart>
  );
}

export default Page;
