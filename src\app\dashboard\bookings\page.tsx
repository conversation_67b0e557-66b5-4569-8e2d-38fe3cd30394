"use client";
import React, { useContext, useMemo, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderLogo from "@/components/_globals/headerLogo";
import Navbar from "@/components/_globals/navbar";
import { useRouter } from "next/navigation";
import Loader1 from "@/components/loaders/Loader1";
import { Calendar, User, Home, Laptop, ClipboardList } from "lucide-react";
import { useBookingFilters } from "@/hooks/useBookingFilters";
import BookingFiltersComponent from "@/components/bookings/BookingFiltersComponent";
import { PropertyContext } from "@/components/_context/PropertyContext";
import type { Booking } from "@/types/booking";
import { statusConfig } from "@/types/booking";

function Page() {
  const router = useRouter();
  const [showFilters, setShowFilters] = useState(false);
  const { properties } = useContext(PropertyContext);

  // Use the new booking filters hook
  const {
    bookings,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalCount,
    pageSize,
    filters,
    activeFilters,
    setFilter,
    clearFilter,
    clearAllFilters,
    applyFilters,
    setPage,
    setPageSize,
    refetch,
    hasActiveFilters,
  } = useBookingFilters();

  // Hydrate bookings with property_data (name, is_domorent) using PropertyContext
  const hydratedBookings = useMemo(() => {
    if (!bookings || bookings.length === 0) return [] as Booking[];
    return bookings.map((b: any) => {
      // API may return property as UUID string; find matching property in context
      const propId = typeof b.property === "string" ? b.property : b.property?.id;
      const prop = properties?.find((p: any) => p?.id === propId);
      const property_data = {
        hotel_id: prop?.hotel_id || prop?.id || "",
        name: prop?.name || b?.property_data?.name || "",
        is_domorent: Boolean(prop?.is_domorent ?? b?.property_data?.is_domorent),
      };
      return { ...b, property_data };
    });
  }, [bookings, properties]);

  const handleSelectBooking = (booking: Booking) => {
    // Persist a hydrated booking so the details page has property_data available
    localStorage.setItem("selectedBooking-heibooky", JSON.stringify(booking));
    router.push("/dashboard/bookings/details");
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("it-IT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getStatusConfig = (status: string) => {
    return (
      statusConfig[status as keyof typeof statusConfig] || {
        color: "#999",
        bgColor: "#f0f0f0",
        icon: "circle",
        label: "Sconosciuto",
      }
    );
  };

  const renderStatusIcon = (status: string) => {
    switch (status) {
      case "new":
        return "✨";
      case "modified":
        return "✏️";
      case "request":
        return "⏰";
      case "cancelled":
        return "❌";
      case "completed":
        return "✓";
      default:
        return "⚪";
    }
  };

  // Safely derive a display ID from reservation_data.id (part before underscore) with fallbacks
  const getDisplayReservationId = (booking: Booking): string => {
    const reservationId = booking?.reservation_data?.id;
    if (typeof reservationId === "string" && reservationId.length > 0) {
      const prefix = reservationId.split("_")[0];
      if (prefix && prefix.trim().length >= 4) {
        return prefix.trim();
      }
    }
    // Fallback to booking.id slice if reservation id invalid
    if (booking?.id) return booking.id.slice(0, 8);
    return "N/D"; // Not available
  };

  // Handle error state
  if (error) {
    return (
      <MobilePageStart isNavbar>
        <HeaderLogo />
        <div className="w-full h-full flex flex-col items-center justify-center p-4 text-center">
          <div className="text-red-500 text-lg font-medium mb-2">
            Errore nel caricamento
          </div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-[#133157] text-white rounded-lg hover:bg-[#0F2847]"
          >
            Riprova
          </button>
        </div>
        <Navbar />
      </MobilePageStart>
    );
  }

  return (
    <MobilePageStart isNavbar>
      <HeaderLogo />
      <div className="w-full h-full flex flex-col p-4 gap-4 pb-20 overflow-auto bg-gray-50">
        {/* Header with Enhanced Filters */}
        <div className="flex justify-between items-start mb-2">
          <div>
            <h1 className="text-xl font-bold text-[#133157]">
              Le tue prenotazioni
            </h1>
            {totalCount > 0 && (
              <div className="text-sm text-gray-600 mt-1 space-y-1">
                <p>
                  {hasActiveFilters
                    ? `${bookings.length} di ${totalCount} prenotazioni`
                    : `${totalCount} prenotazioni totali`}
                </p>
                {(activeFilters.start_date || activeFilters.end_date) && (
                  <div className="flex items-center gap-1 text-xs text-[#133157] bg-blue-50 px-2 py-1 rounded-full w-fit">
                    <Calendar className="w-3 h-3" />
                    <span>
                      {activeFilters.start_date && activeFilters.end_date
                        ? `Dal ${new Date(activeFilters.start_date).toLocaleDateString("it-IT")} al ${new Date(activeFilters.end_date).toLocaleDateString("it-IT")}`
                        : activeFilters.start_date
                          ? `Da ${new Date(activeFilters.start_date).toLocaleDateString("it-IT")}`
                          : activeFilters.end_date
                            ? `Fino al ${new Date(activeFilters.end_date).toLocaleDateString("it-IT")}`
                            : ""}
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>

          <BookingFiltersComponent
            filters={filters}
            activeFilters={activeFilters}
            onFilterChange={setFilter}
            onClearFilter={clearFilter}
            onApplyFilters={applyFilters}
            onClearAllFilters={clearAllFilters}
            isOpen={showFilters}
            onToggle={() => setShowFilters(!showFilters)}
            hasActiveFilters={hasActiveFilters}
            isLoading={isLoading}
            properties={properties}
          />
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="h-full w-full flex items-center justify-center">
            <Loader1 />
          </div>
        )}

        {/* Results */}
        {!isLoading && hydratedBookings.length > 0 ? (
          <>
            {/* Bookings List */}
            {hydratedBookings.map((booking: Booking) => (
              <div
                key={booking.id}
                onClick={() => handleSelectBooking(booking)}
                className="bg-white rounded-lg shadow-sm border-l-4 p-4 cursor-pointer hover:shadow-md transition-all duration-200 transform hover:scale-[1.01]"
                style={{
                  borderLeftColor: getStatusConfig(booking.status).color,
                }}
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-500">
                        Prenotazione ID
                      </span>
                      {booking.is_manual ? (
                        <div className="flex items-center gap-1 px-2 py-0.5 bg-gray-100 rounded-full text-xs text-gray-600">
                          <ClipboardList className="w-3 h-3" />
                          <span>Manuale</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-1 px-2 py-0.5 bg-blue-50 rounded-full text-xs text-blue-600">
                          <Laptop className="w-3 h-3" />
                          <span>Canale</span>
                        </div>
                      )}
                    </div>
                    <span
                      className="font-bold text-[#133157]"
                      aria-label="Reservation Reference"
                    >
                      #{getDisplayReservationId(booking)}
                    </span>
                  </div>
                  <div
                    className="flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium shadow-sm"
                    style={{
                      backgroundColor: getStatusConfig(booking.status).bgColor,
                      color: getStatusConfig(booking.status).color,
                    }}
                  >
                    <span>{renderStatusIcon(booking.status)}</span>
                    <span>{getStatusConfig(booking.status).label}</span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Home className="w-4 h-4 text-[#133157]" />
                    <span className="text-sm truncate">
                      {booking.property_data?.name || "Property Name"}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-[#133157]" />
                    <span className="text-sm truncate">
                      {booking.customer.first_name} {booking.customer.last_name}
                    </span>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-[#133157]" />
                    <div className="text-sm">
                      <span className="text-gray-500">
                        {formatDate(booking.reservation_data.checkin_date)}
                      </span>
                      <span className="mx-2 text-gray-400">→</span>
                      <span className="text-gray-500">
                        {formatDate(booking.reservation_data.checkout_date)}
                      </span>
                    </div>
                  </div>
                  <span className="text-sm font-bold text-[#133157]">
                    €{booking.reservation_data.total_price}
                  </span>
                </div>
              </div>
            ))}

            {/* Enhanced Pagination Controls */}
            {bookings.length > 0 && totalPages > 1 && (
              <div className="flex flex-col justify-center items-center mt-6 mb-2 gap-4">
                <div className="flex items-center gap-3 bg-white rounded-lg shadow-sm p-3">
                  <button
                    onClick={() => setPage(Math.max(currentPage - 1, 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded-md transition-all duration-200 ${
                      currentPage === 1
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-[#133157] hover:bg-gray-100 hover:scale-110"
                    }`}
                    aria-label="Pagina precedente"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                  </button>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-700">
                      Pagina{" "}
                      <span className="font-bold text-[#133157]">
                        {currentPage}
                      </span>{" "}
                      di <span className="font-bold">{totalPages}</span>
                    </span>
                  </div>

                  <button
                    onClick={() =>
                      setPage(Math.min(currentPage + 1, totalPages))
                    }
                    disabled={currentPage === totalPages}
                    className={`p-2 rounded-md transition-all duration-200 ${
                      currentPage === totalPages
                        ? "text-gray-400 cursor-not-allowed"
                        : "text-[#133157] hover:bg-gray-100 hover:scale-110"
                    }`}
                    aria-label="Pagina successiva"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </div>

                {/* Enhanced Page Size Selector */}
                <div className="flex items-center gap-2 text-sm text-gray-700 bg-white rounded-lg shadow-sm p-3">
                  <span>Mostra:</span>
                  <select
                    value={pageSize}
                    onChange={(e) => setPageSize(Number(e.target.value))}
                    className="p-2 border border-gray-300 rounded-md bg-white text-[#133157] font-medium focus:ring-2 focus:ring-[#133157] focus:border-transparent"
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                  <span>prenotazioni per pagina</span>
                </div>
              </div>
            )}
          </>
        ) : (
          !isLoading && (
            <div className="flex flex-col items-center justify-center p-8 text-center text-gray-500 bg-white rounded-lg shadow-sm">
              <Calendar className="w-16 h-16 mb-4 text-gray-300" />
              <h3 className="text-lg font-semibold mb-2">
                {hasActiveFilters
                  ? "Nessuna prenotazione trovata"
                  : "Nessuna prenotazione presente"}
              </h3>
              <p className="text-sm text-gray-400 mb-4">
                {hasActiveFilters
                  ? "Prova a modificare i filtri di ricerca"
                  : "Le tue prenotazioni appariranno qui"}
              </p>
              {hasActiveFilters && (
                <button
                  onClick={clearAllFilters}
                  className="px-6 py-3 bg-[#133157] text-white rounded-lg font-medium hover:bg-[#0F2847] transition-all duration-200 shadow-md hover:shadow-lg"
                >
                  Rimuovi tutti i filtri
                </button>
              )}
            </div>
          )
        )}
      </div>
      <Navbar />
    </MobilePageStart>
  );
}

export default Page;
