"use client";
import React, { useEffect, useState } from "react";

import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import MultiSelect from "@/components/inputs/multiSelect";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import { useRouter } from "next/navigation";
import CardAlert from "@/components/cards/CardAlert";

import { collaboratorPermissionsOptions } from "@/models/modelCollaboratorPermissions";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import { inviteCollaborator } from "@/services/api";
import toast from "react-hot-toast";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [email, setEmail] = useState("");
  const [selectedOptions, setSelectedOptions] = useState<any>([]);

  const handleCreateCollaborator = async () => {
    if (email == "") {
      return toast.error("La mail è obbligatoria");
    }

    if (selectedOptions.length === 0) {
      return toast.error("Selezionare almeno una opzione");
    }

    const call = await inviteCollaborator(
      params.propertyId,
      email,
      selectedOptions,
    );

    if (call.status === 200 || call.status === 201) {
      toast.success("Collaboratore aggiunto con successo");

      setTimeout(() => {
        router.back();
      }, 500);
    }

    return true;
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Permessi"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
        <div className="flex flex-col gap-4">
          <CardAlert
            title="Informazioni"
            message="Aggiungi l'indirizzo email della persona a cui vuoi concedere l'accesso a questa proprietà. Se non hanno ancora un account HeiBooky, riceveranno un'e-mail con un collegamento per crearne uno."
            color="orange"
          />

          <InputLabelDescription
            label="Aggiungi Collaboratore"
            placeholder="E-mail del collaboratore"
            value={email}
            onChange={setEmail}
          />

          <MultiSelect
            options={collaboratorPermissionsOptions}
            onChange={(valueId) => {
              setSelectedOptions(valueId);
            }}
            value={selectedOptions}
            title="Quali permessi deve avere il collaboratore?"
            isMultiSelect
            optionsStyle={{
              fontSizeDescription: "10px",
              fontSizeTitle: "12px",
            }}
            unselectingAnswer={1}
          />

          <ButtonLoading
            text={"Aggiungi Collaboratore"}
            onClick={() => {
              return handleCreateCollaborator();
            }}
            color="white"
            backgroundColor="var(--blue)"
          />
        </div>
      </div>
    </MobilePageStart>
  );
}

export default Page;
