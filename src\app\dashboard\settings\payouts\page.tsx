"use client";
import React, { useEffect, useState, useMemo } from "react";
import { motion } from "framer-motion";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";

import Button from "@/components/buttons/Button";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import { useRouter } from "next/navigation";
import { getPayouts } from "@/services/api";
import InputCalendar from "@/components/inputs/InputCalendar";
import toast from "react-hot-toast";
import Card from "@/components/cards/Card";
import PaymentStatusBadge from "@/components/financial/PaymentStatusBadge";
import { financialUtils } from "@/services/financialApi";

interface Payout {
  id: string;
  amount: string;
  currency: string;
  status: string;
  created_at: string;
  processed_at?: string;
  description?: string;
  payment_method?: string;
  reference_id?: string;
}

function Page() {
  const router = useRouter();

  // Intervallo date predefinito: ultimi 12 mesi
  const today = useMemo(() => new Date(), []);
  const twelveMonthsAgo = useMemo(() => {
    const d = new Date();
    d.setMonth(d.getMonth() - 12);
    return d;
  }, []);
  const [startDate, setStartDate] = useState<string | any>(
    twelveMonthsAgo.toISOString().substring(0, 10),
  );
  const [endDate, setEndDate] = useState<string | any>(
    today.toISOString().substring(0, 10),
  );

  const [payouts, setPayouts] = useState<Payout[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  const handleGetPayouts = async () => {
    if (!startDate || !endDate) {
      return toast.error("Seleziona un intervallo di date valido");
    }

    setIsLoading(true);

    try {
      const call = await getPayouts(startDate, endDate);

      if (call.status === 200) {
        setPayouts(call.data || []);

        // Calculate total amount
        const total = (call.data || []).reduce(
          (sum: number, payout: Payout) => {
            return sum + parseFloat(payout.amount || "0");
          },
          0,
        );
        setTotalAmount(total);
      } else {
        toast.error("Impossibile caricare i pagamenti");
        setPayouts([]);
        setTotalAmount(0);
      }
    } catch (error) {
      console.error("Error fetching payouts:", error);
      toast.error("Impossibile caricare i pagamenti");
      setPayouts([]);
      setTotalAmount(0);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleGetPayouts();
  }, [startDate, endDate]);

  const getStatusBadge = (status: string) => {
    const normalizedStatus = status.toLowerCase();

    switch (normalizedStatus) {
      case "completed":
      case "paid":
      case "processed":
        return (
          <PaymentStatusBadge status="completed" displayText="Completato" />
        );
      case "pending":
      case "processing":
        return (
          <PaymentStatusBadge
            status="payment_in_progress"
            displayText="In corso"
          />
        );
      case "cancelled":
      case "failed":
        return (
          <PaymentStatusBadge status="cancelled" displayText="Annullato" />
        );
      default:
        return (
          <PaymentStatusBadge
            status="future_payment"
            displayText="Programmato"
          />
        );
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("it-IT", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Pagamenti"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      <div className="mt-3 px-3 pb-24 w-full flex flex-col gap-5 max-w-md mx-auto overflow-x-hidden">
        {/* Filtro Date */}
        <motion.div
          initial={{ opacity: 0, y: 28 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ type: "spring", stiffness: 160, damping: 22 }}
        >
          <Card>
            <div className="p-4">
              <h3 className="text-base font-semibold mb-3 tracking-tight">
                Filtra per Intervallo
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <InputCalendar
                  value={startDate}
                  label="Inizio"
                  onChange={setStartDate}
                />
                <InputCalendar
                  value={endDate}
                  label="Fine"
                  onChange={setEndDate}
                />
              </div>
              <div className="mt-3 flex items-center justify-between text-[11px] text-gray-500">
                <span>Ultimi 12 mesi predefiniti</span>
                <Button
                  onClick={() => {
                    void handleGetPayouts();
                  }}
                  color="white"
                  backgroundColor="var(--accent)"
                  text="↻ Aggiorna"
                />
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Riepilogo */}
        {!isLoading && payouts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 26 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{
              type: "spring",
              stiffness: 150,
              damping: 22,
              delay: 0.05,
            }}
          >
            <Card>
              <div className="p-4">
                <h3 className="text-base font-semibold mb-3 tracking-tight">
                  Riepilogo
                </h3>
                <motion.div
                  className="grid grid-cols-3 gap-3"
                  initial="hidden"
                  animate="show"
                  variants={{
                    hidden: { opacity: 0 },
                    show: { opacity: 1, transition: { staggerChildren: 0.07 } },
                  }}
                >
                  {[
                    {
                      label: "Totali",
                      value: payouts.length,
                      color: "text-[var(--accent)]",
                    },
                    {
                      label: "Importo",
                      value: financialUtils.formatCurrency(totalAmount),
                      color: "text-green-600 font-mono",
                    },
                    {
                      label: "Completati",
                      value: payouts.filter((p) =>
                        ["completed", "paid", "processed"].includes(
                          p.status.toLowerCase(),
                        ),
                      ).length,
                      color: "text-blue-600",
                    },
                  ].map((item) => (
                    <motion.div
                      key={item.label}
                      variants={{
                        hidden: { opacity: 0, y: 14 },
                        show: {
                          opacity: 1,
                          y: 0,
                          transition: {
                            type: "spring",
                            stiffness: 190,
                            damping: 18,
                          },
                        },
                      }}
                      className="text-center rounded-md bg-gray-50 py-3"
                    >
                      <div
                        className={`text-lg font-bold leading-none mb-1 ${item.color}`}
                      >
                        {item.value}
                      </div>
                      <div className="text-[11px] tracking-wide text-gray-500 uppercase font-medium">
                        {item.label}
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4 }}
          >
            <Card>
              <div className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                  {[...Array(3)].map((_, i) => (
                    <div
                      key={i}
                      className="flex justify-between items-center py-3 border-b"
                    >
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-32"></div>
                        <div className="h-3 bg-gray-200 rounded w-24"></div>
                      </div>
                      <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-20"></div>
                        <div className="h-6 bg-gray-200 rounded w-16"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Elenco Pagamenti */}
        {!isLoading && payouts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 26 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-40px" }}
            transition={{ type: "spring", stiffness: 150, damping: 22 }}
          >
            <Card>
              <div className="p-4">
                <h3 className="text-base font-semibold mb-3 tracking-tight">
                  Storico Pagamenti
                </h3>

                {/* Tabella Desktop (ridotta) */}
                <div className="hidden md:block overflow-x-auto -mx-2">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 text-xs uppercase tracking-wide text-gray-500">
                        <th className="text-left py-2 px-2 font-medium">
                          Importo
                        </th>
                        <th className="text-left py-2 px-2 font-medium">
                          Stato
                        </th>
                        <th className="text-left py-2 px-2 font-medium">
                          Creato
                        </th>
                        <th className="text-left py-2 px-2 font-medium">
                          Processato
                        </th>
                        <th className="text-left py-2 px-2 font-medium">
                          Rif.
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {payouts.map((payout) => (
                        <motion.tr
                          key={payout.id}
                          className="border-b border-gray-100 hover:bg-gray-50"
                          initial={{ opacity: 0 }}
                          whileInView={{ opacity: 1 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.25 }}
                        >
                          <td className="py-2 px-2">
                            <div className="font-mono font-semibold">
                              {financialUtils.formatCurrency(payout.amount)}{" "}
                              {payout.currency}
                            </div>
                          </td>
                          <td className="py-2 px-2">
                            {getStatusBadge(payout.status)}
                          </td>
                          <td className="py-2 px-2 text-gray-600">
                            {formatDate(payout.created_at)}
                          </td>
                          <td className="py-2 px-2 text-gray-600">
                            {payout.processed_at
                              ? formatDate(payout.processed_at)
                              : "-"}
                          </td>
                          <td className="py-2 px-2 text-gray-500 font-mono">
                            {(payout.reference_id || payout.id).substring(0, 8)}
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Schede Mobile */}
                <motion.div
                  className="md:hidden space-y-3"
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true, margin: "-40px" }}
                  variants={{
                    hidden: { opacity: 0 },
                    show: { opacity: 1, transition: { staggerChildren: 0.08 } },
                  }}
                >
                  {payouts.map((payout) => (
                    <motion.div
                      key={payout.id}
                      variants={{
                        hidden: { opacity: 0, y: 16 },
                        show: {
                          opacity: 1,
                          y: 0,
                          transition: {
                            type: "spring",
                            stiffness: 200,
                            damping: 22,
                          },
                        },
                      }}
                      className="rounded-lg p-3 bg-gradient-to-br from-white to-gray-50 border border-gray-100 shadow-sm"
                    >
                      <div className="flex justify-between gap-3 mb-2">
                        <div>
                          <div className="text-[15px] font-bold font-mono leading-tight">
                            {financialUtils.formatCurrency(payout.amount)}{" "}
                            {payout.currency}
                          </div>
                          {payout.description && (
                            <div className="text-[11px] text-gray-500 mt-1 line-clamp-2">
                              {payout.description}
                            </div>
                          )}
                        </div>
                        <div className="shrink-0">
                          {getStatusBadge(payout.status)}
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-3 text-[11px]">
                        <div className="space-y-0.5">
                          <span className="text-gray-500 block">Creato</span>
                          <span className="font-medium text-gray-800 text-[12px] leading-tight">
                            {formatDate(payout.created_at)}
                          </span>
                        </div>
                        <div className="space-y-0.5">
                          <span className="text-gray-500 block">
                            Processato
                          </span>
                          <span className="font-medium text-gray-800 text-[12px] leading-tight">
                            {payout.processed_at
                              ? formatDate(payout.processed_at)
                              : "In attesa"}
                          </span>
                        </div>
                        <div className="col-span-2 space-y-0.5">
                          <span className="text-gray-500 block">
                            Riferimento
                          </span>
                          <span className="font-mono text-[11px] break-all text-gray-700">
                            {payout.reference_id || payout.id}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Stato Vuoto */}
        {!isLoading && payouts.length === 0 && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ type: "spring", stiffness: 160, damping: 24 }}
          >
            <Card>
              <div className="flex flex-col items-center py-14 px-4 text-center">
                <div className="text-5xl mb-4">💳</div>
                <p className="text-[20px] font-semibold text-gray-800 mb-1">
                  Nessun pagamento ancora
                </p>
                <p className="text-[12px] text-gray-500 mb-5">
                  Quando verranno elaborati pagamenti appariranno qui
                </p>
                <Button
                  onClick={() => router.push("/dashboard/financial")}
                  color="white"
                  backgroundColor="var(--accent)"
                  text="📊 Vai alla Dashboard"
                />
              </div>
            </Card>
          </motion.div>
        )}

        {/* Azioni Rapide */}
        {!isLoading && payouts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 26 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 150, damping: 22 }}
          >
            <Card>
              <div className="p-4">
                <h3 className="text-base font-semibold mb-3 tracking-tight">
                  Azioni Rapide
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <Button
                    onClick={() => router.push("/dashboard/financial")}
                    color="white"
                    backgroundColor="var(--accent)"
                    text="📊 Dashboard"
                  />
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </div>
    </MobilePageStart>
  );
}

export default Page;
