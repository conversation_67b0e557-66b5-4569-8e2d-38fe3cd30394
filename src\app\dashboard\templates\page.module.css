/* Templates Page Styles */
.templatesContainer {
  width: 100%;
  max-width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  overflow: hidden;
  padding: 1rem 0.75rem;
}

@media (min-width: 640px) {
  .templatesContainer {
    padding: 1rem;
  }
}

.templatesScrollArea {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  max-width: 100%;
}

.templatesGrid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding-bottom: 1rem;
}

@media (min-width: 640px) {
  .templatesGrid {
    gap: 1rem;
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 8rem;
}

.emptyState {
  text-align: center;
  color: #6b7280;
  margin-top: 2rem;
  padding: 2rem 1rem;
}

.emptyState p {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Ensure proper mobile scrolling */
@media (max-width: 768px) {
  .templatesScrollArea {
    -webkit-overflow-scrolling: touch;
  }
}

/* Handle very small screens */
@media (max-width: 320px) {
  .templatesContainer {
    padding: 0.75rem 0.5rem;
  }

  .templatesGrid {
    gap: 0.5rem;
  }
}
