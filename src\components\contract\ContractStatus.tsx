"use client";

import React, { useEffect } from "react";
import {
  FileText,
  Download,
  Clock,
  CheckCircle,
  AlertCircle,
  ExternalLink,
} from "lucide-react";
import { useRouter } from "next/navigation";
import useContract from "@/hooks/useContract";

interface ContractStatusProps {
  showFullDetails?: boolean;
  className?: string;
}

const ContractStatus: React.FC<ContractStatusProps> = ({
  showFullDetails = true,
  className = "",
}) => {
  const {
    hasContract,
    hasBillingProfile,
    downloadContract,
    refreshContract,
    statusSummary,
  } = useContract();

  const router = useRouter();

  useEffect(() => {
    // Refresh contract status when component mounts
    if (hasBillingProfile) {
      refreshContract();
    }
  }, [hasBillingProfile, refreshContract]);

  const handleNavigateToBilling = () => {
    router.push("/dashboard/settings/account/billing");
  };

  const getStatusIcon = () => {
    switch (statusSummary.status) {
      case "available":
        return {
          icon: CheckCircle,
          color: "text-green-500",
          bgColor: "bg-green-100",
        };
      case "pending":
        return {
          icon: Clock,
          color: "text-yellow-500",
          bgColor: "bg-yellow-100",
        };
      default:
        return {
          icon: AlertCircle,
          color: "text-red-500",
          bgColor: "bg-red-100",
        };
    }
  };

  const getStatusTitle = () => {
    switch (statusSummary.status) {
      case "available":
        return "Contratto Disponibile";
      case "pending":
        return "In Attesa di Generazione Contratto";
      default:
        return "Profilo Incompleto";
    }
  };

  const iconConfig = getStatusIcon();
  const StatusIcon = iconConfig.icon;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <div
            className={`w-10 h-10 rounded-full ${iconConfig.bgColor} flex items-center justify-center`}
          >
            <StatusIcon size={20} className={iconConfig.color} />
          </div>
          <div>
            <h3 className="text-base font-semibold text-gray-900">
              {getStatusTitle()}
            </h3>
            <p className="text-sm text-gray-600">{statusSummary.message}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {showFullDetails && (
          <>
            {/* Contract Details */}
            {hasContract && (
              <div className="mb-4 p-3 bg-green-50 rounded-md">
                <div className="flex items-start justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-green-800 mb-1">
                      Dettagli Contratto
                    </h4>
                    <div className="text-xs text-green-700 space-y-1">
                      <p>Stato: Contratto disponibile</p>
                      <p>
                        Il contratto è stato generato ed è pronto per il
                        download
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Status-specific information */}
            {statusSummary.status === "pending" && (
              <div className="mb-4 p-3 bg-yellow-50 rounded-md">
                <h4 className="text-sm font-medium text-yellow-800 mb-2">
                  Prossimi Passi
                </h4>
                <ul className="text-xs text-yellow-700 space-y-1">
                  <li>
                    • Completa la configurazione dell'account collegato Stripe
                  </li>
                  <li>
                    • Genera il contratto utilizzando il pulsante apposito
                  </li>
                  <li>• Potrai scaricare il PDF del contratto</li>
                </ul>
              </div>
            )}

            {statusSummary.status === "incomplete" && (
              <div className="mb-4 p-3 bg-red-50 rounded-md">
                <h4 className="text-sm font-medium text-red-800 mb-2">
                  Informazioni Mancanti
                </h4>
                <ul className="text-xs text-red-700 space-y-1">
                  <li>• Dati personali e fiscali</li>
                  <li>• Indirizzo di fatturazione</li>
                  <li>• Codice IBAN valido</li>
                </ul>
              </div>
            )}
          </>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col gap-2">
          {statusSummary.canDownload && (
            <button
              onClick={downloadContract}
              className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download size={16} />
              Scarica Contratto PDF
            </button>
          )}

          {statusSummary.status === "incomplete" && (
            <button
              onClick={handleNavigateToBilling}
              className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-gray-900 text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors"
            >
              <ExternalLink size={16} />
              Completa Profilo Fatturazione
            </button>
          )}

          {/* Refresh button */}
          <button
            onClick={() => refreshContract()}
            className="inline-flex items-center justify-center gap-2 px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors"
          >
            <Clock size={16} />
            Aggiorna Stato
          </button>
        </div>
      </div>
    </div>
  );
};

export default ContractStatus;
