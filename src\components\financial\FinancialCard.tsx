import React from "react";
import Card from "../cards/Card";
import { financialUtils } from "@/services/financialApi";

interface FinancialCardProps {
  title: string;
  // value may be undefined/null when data is not available
  value?: string | number | null;
  currency?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  loading?: boolean;
  error?: boolean;
  errorMessage?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  className?: string;
  onClick?: () => void;
  noCurrency?: boolean;
}

const FinancialCard: React.FC<FinancialCardProps> = ({
  title,
  value,
  currency = "EUR",
  subtitle,
  icon,
  loading = false,
  error = false,
  errorMessage,
  trend,
  className = "",
  onClick,
  noCurrency = false,
}) => {
  const formatValue = () => {
    if (loading) return "---";
    if (error) return "Errore";
    // If value is null or undefined, show an empty placeholder
    if (value === null || value === undefined) return "";

    // If noCurrency is true, just return the value as string/number without currency formatting
    if (noCurrency) {
      return String(value);
    }

    // If value is number 0 or string '0' -> still format as 0 currency
    if (typeof value === "number") {
      return financialUtils.formatCurrency(value, currency);
    }

    if (typeof value === "string") {
      // If string contains decimal point or digits, attempt to format
      if (value.includes(".") || /^\d+$/.test(value)) {
        return financialUtils.formatCurrency(value, currency);
      }
      return value;
    }

    return String(value);
  };

  const getTrendIcon = () => {
    if (!trend) return null;

    if (trend.isPositive) {
      return (
        <span className="text-green-600 text-sm font-medium">
          ↗ +{Math.abs(trend.value)}%
        </span>
      );
    } else {
      return (
        <span className="text-red-600 text-sm font-medium">
          ↘ -{Math.abs(trend.value)}%
        </span>
      );
    }
  };

  if (loading) {
    return (
      <Card className={`animate-pulse ${className}`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            {icon && <div className="h-6 w-6 bg-gray-200 rounded"></div>}
          </div>
          <div className="h-8 bg-gray-200 rounded w-32 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-20"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-red-200 ${className}`}>
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-600">{title}</h3>
            {icon && <div className="text-red-400">{icon}</div>}
          </div>
          <div className="text-red-600 font-semibold mb-2">Errore</div>
          <p className="text-xs text-red-500">
            {errorMessage || "Impossibile caricare i dati"}
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={`hover:shadow-lg transition-shadow cursor-pointer ${className}`}
      onClick={onClick}
    >
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-gray-600">{title}</h3>
          {icon && <div className="text-[var(--accent)]">{icon}</div>}
        </div>

        <div className="mb-2">
          <div className="text-2xl font-bold text-gray-900 font-mono">
            {formatValue()}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div>
            {subtitle && <p className="text-sm text-gray-500">{subtitle}</p>}
          </div>
          <div>{getTrendIcon()}</div>
        </div>

        {trend?.period && (
          <p className="text-xs text-gray-400 mt-1">
            rispetto a {trend.period}
          </p>
        )}
      </div>
    </Card>
  );
};

export default FinancialCard;
