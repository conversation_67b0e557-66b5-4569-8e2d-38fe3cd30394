"use client";

import styles from "./page.module.css";

import { Suspense, useState, useEffect, useContext } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Loader1 from "@/components/loaders/Loader1";
import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import Input from "@/components/inputs/input";
import InputHidden from "@/components/inputs/inputHidden";
import Link from "next/link";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import GoogleAuthButton from "@/components/buttons/GoogleAuthButton";
import toast from "react-hot-toast";
import dynamic from "next/dynamic";
import AuthLogo from "@/components/auth/AuthLogo";
import { AuthContext } from "@/components/_context/AuthContext";

const Home = dynamic(() => Promise.resolve(HomeComponent), {
  ssr: false,
});

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Home />
    </Suspense>
  );
}

function HomeComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    login: authLogin,
    isAuthenticated,
    isLoading,
    checkAuth,
  } = useContext(AuthContext);

  // States
  const emailLogin = searchParams.get("email");
  const redirectPath = searchParams.get("redirect");
  const [email, setEmail] = useState(
    emailLogin && emailLogin !== "" ? emailLogin : "",
  );
  const [password, setPassword] = useState("");
  const [isCheckingToken, setIsCheckingToken] = useState(true);

  useEffect(() => {
    const checkExistingToken = async () => {
      const isValid = await checkAuth();

      if (isValid) {
        // Token is valid, redirect to dashboard or original path
        const targetPath = redirectPath
          ? decodeURIComponent(redirectPath)
          : "/dashboard";
        router.push(targetPath);
      }

      setIsCheckingToken(false);
    };

    checkExistingToken();
  }, [redirectPath, router, checkAuth]);

  // Show loading state while checking token
  if (isCheckingToken || isLoading) {
    return (
      <MobilePage>
        <div className="w-full h-screen flex items-center justify-center">
          <Loader1 />
        </div>
      </MobilePage>
    );
  }

  // Handle regular login
  const handleLogin = async () => {
    if (!email || !password) {
      toast.error("Inserisci email e password");
      return false;
    }

    const success = await authLogin(email, password);

    if (success) {
      // Redirect to the original path if available, otherwise go to dashboard
      const targetPath = redirectPath
        ? decodeURIComponent(redirectPath)
        : "/dashboard";
      router.push(targetPath);
      return true;
    } else {
      toast.error("La mail o la password non sono corrette");
    }

    return false;
  };

  // Handle form submission (for Enter key support)
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleLogin();
  };

  // Handle successful Google auth
  const handleGoogleAuthSuccess = () => {
    const targetPath = redirectPath
      ? decodeURIComponent(redirectPath)
      : "/dashboard";
    router.push(targetPath);
  };

  // Handle Google auth error
  const handleGoogleAuthError = (errorMessage: string) => {
    toast.error(errorMessage || "Autenticazione Google fallita");
  };

  return (
    <MobilePage>
      <AuthLogo />
      <div className="mt-6"></div>
      <h1 className={styles.title}>Login</h1>

      <form onSubmit={handleSubmit} className={styles.form}>
        <div className="w-full flex flex-col gap-4">
          <Input
            type="email"
            placeholder="E-mail"
            value={email}
            onChange={setEmail}
          />
          <InputHidden
            placeholder="Password"
            value={password}
            onChange={setPassword}
          />
          <Link className={styles.link} href="/auth/recover">
            Hai dimenticato la password?
          </Link>

          <div className="flex justify-center mt-3">
            <ButtonLoading
              text="Accedi"
              onClick={() => {
                return handleLogin();
              }}
              color="white"
              backgroundColor="var(--blue)"
            />
          </div>

          <div className={styles.divider}>
            <span>oppure</span>
          </div>

          <div className="flex justify-center">
            <GoogleAuthButton
              text="Accedi con Google"
              onSuccess={handleGoogleAuthSuccess}
              onError={handleGoogleAuthError}
            />
          </div>
        </div>
      </form>

      <div className={styles.link2}>
        Non hai ancora un account?{" "}
        <Link className={styles.link} href="/auth/register">
          Iscriviti
        </Link>
      </div>
    </MobilePage>
  );
}
