// Booking related types and interfaces

export interface BookingFilters {
  status?: "new" | "modified" | "request" | "cancelled" | "completed";
  type?: "manual" | "ota";
  property?: string; // UUID
  start_date?: string; // YYYY-MM-DD format - filter bookings with check-out date >= start_date
  end_date?: string; // YYYY-MM-DD format - filter bookings with check-in date <= end_date
  page?: number;
  page_size?: number;
}

export interface Property {
  id?: string;
  name?: string;
  is_domorent?: boolean;
}

export interface PropertyData {
  hotel_id: string;
  name: string;
  is_domorent: boolean;
}

export interface Customer {
  first_name: string;
  last_name: string;
  email: string;
  telephone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zip_code: string;
}

export interface ReservationData {
  id: string;
  guest_name: string;
  total_price: string;
  net_price: string;
  deposit: string;
  checkin_date: string;
  checkout_date: string;
  number_of_guests: number;
  number_of_adults: number;
  number_of_children: number;
  number_of_infants: number;
  payment_type: string;
  calculation_type?: string; // Added for Domorent detection

  // New API fields - Common
  total_tax?: string;
  booked_at?: string;
  modified_at?: string;

  // Heibooky fields (null for Domorent)
  client_price?: string;
  base_price?: string;
  ota_commission: string | null;
  heibooky_commission: string | null;
  payment_charge: string | null;
  payment_fee?: string;
  activation_fee?: string;
  activation_fee_applied?: string;
  vat_22?: string;
  iva_amount: string | null;
  substitute_tax_21?: string;
  owner_tax: string | null;
  net_total_for_owner: string | null;
  total_owner_payout?: string;

  // Domorent fields (null for non-Domorent)
  cleaning_cost: string | null;
  taxable_domorent: string | null;
  tot_platform_commission: string | null;
  owner_net_transfer: string | null;
  stamp_duty?: string | null;
  payment_fee_1_5?: number | string | null;
  subtotal_after_deductions?: number | string | null; // TOTAL before Flat Tax
  flat_tax_21?: number | string | null;
  net_total?: number | string | null; // Final NET TOTAL

  // Common fields
  commission_amount: string; // Legacy field
  extra_fees: object;
  taxes: object;
  payment_due: string;
  remarks: string | null;
  addons: object;
}

export interface Booking {
  id: string;
  property: Property;
  property_data: PropertyData;
  customer: Customer;
  reservation_data: ReservationData;
  booking_date: string;
  status: "new" | "modified" | "request" | "cancelled" | "completed";
  is_manual: boolean;
  channel_code: number;
  payment_processed: boolean;
  whatsapp_link?: string;
}

export interface BookingApiResponse {
  results: Booking[];
  count: number;
  next: string | null;
  previous: string | null;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  appliedFilters?: BookingFilters;
}

export interface StatusConfig {
  color: string;
  bgColor: string;
  icon: string;
  label: string;
  darkColor?: string;
}

export interface TypeConfig {
  color: string;
  bgColor: string;
  label: string;
  darkColor: string;
}

// Status configuration
export const statusConfig: Record<string, StatusConfig> = {
  new: {
    color: "#2196F3",
    bgColor: "#E3F2FD",
    icon: "sparkles",
    label: "Nuova",
    darkColor: "#1976D2",
  },
  modified: {
    color: "#FF9800",
    bgColor: "#FFF3E0",
    icon: "pencil",
    label: "Modificata",
    darkColor: "#F57C00",
  },
  request: {
    color: "#9C27B0",
    bgColor: "#F3E5F5",
    icon: "clock",
    label: "Richiesta",
    darkColor: "#7B1FA2",
  },
  cancelled: {
    color: "#F44336",
    bgColor: "#FFEBEE",
    icon: "x",
    label: "Cancellata",
    darkColor: "#D32F2F",
  },
  completed: {
    color: "#4CAF50",
    bgColor: "#E8F5E9",
    icon: "check",
    label: "Completata",
    darkColor: "#388E3C",
  },
};

// Type configuration
export const typeConfig: Record<string, TypeConfig> = {
  manual: {
    color: "#6B7280",
    bgColor: "#F3F4F6",
    label: "Manuale",
    darkColor: "#374151",
  },
  ota: {
    color: "#3B82F6",
    bgColor: "#DBEAFE",
    label: "OTA",
    darkColor: "#1D4ED8",
  },
};
