import React, { useState } from "react";
import { <PERSON>R<PERSON>, <PERSON><PERSON><PERSON>, Link, XIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import loadingAnimation from "../../../public/animations/loading.json";

interface ButtonSectionProps {
  ticker?: boolean;
  completed: boolean;
  title: string;
  desciption?: string | null;
  href?: string;
  onClick?: () => void | Promise<void> | Promise<boolean>;

  noIcon?: boolean;
  disabled?: boolean;
  backgroundColor?: string;
  showLoading?: boolean;
}

const ButtonSectionFunction = ({
  ticker,
  completed,
  title,
  desciption,
  href,
  onClick,
  noIcon,
  backgroundColor,
  disabled,
  showLoading = true,
}: ButtonSectionProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (disabled || isLoading) {
      return;
    }

    try {
      if (showLoading && onClick) {
        setIsLoading(true);
      }

      if (href) {
        router.push(href);
      }

      if (onClick) {
        const result = onClick();
        if (result instanceof Promise) {
          await result;
        }
      }
    } catch (error) {
      console.error("ButtonSectionFunction onClick error:", error);
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  const isDisabled = disabled || isLoading;

  return (
    <div
      onClick={handleClick}
      className="flex flex-row bg-[white] p-3 rounded-[15px] items-center justify-between w-full"
      style={{
        opacity: isDisabled ? 0.5 : 1,
        boxShadow: "0 0 5px 15pxrgba(0, 0, 0, 0.19)",
        backgroundColor: `${backgroundColor ? backgroundColor : "white"}`,
        cursor: isDisabled ? "not-allowed" : "pointer",
        pointerEvents: isDisabled ? "none" : "auto",
      }}
    >
      <div className="flex flex-row items-center gap-3">
        {/* Ticker green/red */}
        {ticker && (
          <div
            style={{
              minHeight: "22px",
              minWidth: "22px",
              borderRadius: "50%",
              background: `${completed ? "yellowgreen" : "tomato"}`,
            }}
            className="flex flex-row items-center justify-center"
          >
            {completed ? (
              <CheckIcon width={17} color="white" />
            ) : (
              <XIcon width={17} color="white" />
            )}
          </div>
        )}
        <div>
          <p className="text-[14px]">{title}</p>
          {desciption && (
            <p className="text-[12px] text-[#00000090] mt-1">{desciption}</p>
          )}
        </div>
      </div>

      {isLoading ? (
        <Lottie
          animationData={loadingAnimation}
          loop={true}
          style={{
            width: "20px",
            height: "20px",
            filter:
              "brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)",
          }}
        />
      ) : (
        !noIcon && <ArrowRight width={20} color="black" />
      )}
    </div>
  );
};

export default ButtonSectionFunction;
