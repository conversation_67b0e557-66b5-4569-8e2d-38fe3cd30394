"use client";

import { useEffect, useRef, useState } from "react";
import { apiClient } from "@/services/apiClient";
import { chekinApi, type ChekinStatusSummary, type ChekinPropertyEvent, type ChekinBookingEvent } from "@/services/chekinApi";
import Loader1 from "@/components/loaders/Loader1";

// Esteso per includere l'identificatore della configurazione, necessario per gli aggiornamenti di stato
export type ChekinLaunchPayload = {
  sdkUrl: string;
  apiKey: string;
  housingId?: string | null;
  externalHousingId?: string | null;
  reservationId?: string | null;
  hiddenSections?: string[];
  hiddenFormFields?: any[];
  styles?: string;
  // Nuovi campi opzionali (gestiamo sia camelCase che snake_case per retro‑compatibilità)
  configId?: string | number;
  config_id?: string | number;
};

declare global {
  interface Window {
    ChekinHousingsSDK?: new () => {
      initialize: (cfg: Record<string, any>) => void;
      renderApp: (opts: { targetNode: string }) => void;
    };
  }
}

interface Props {
  propertyId: string | number;
  reservationId?: string | number;
  isReservationDetailsMode?: boolean;
  className?: string;
  onApiDataChange?: (data: {
    statusSummary: ChekinStatusSummary | null;
    propertyEvents: ChekinPropertyEvent[];
    bookingEvents: ChekinBookingEvent[];
    apiDataLoaded: boolean;
    refreshData: () => Promise<void>;
  }) => void;
}

export default function ChekinWidget({
  propertyId,
  reservationId,
  isReservationDetailsMode = false,
  className,
  onApiDataChange,
}: Props) {
  const rootId = useRef(`chekin-root-${Math.random().toString(36).slice(2)}`);
  const [payload, setPayload] = useState<ChekinLaunchPayload | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [configId, setConfigId] = useState<string | number | null>(null);
  
  // Chekin API status data
  const [statusSummary, setStatusSummary] = useState<ChekinStatusSummary | null>(null);
  const [propertyEvents, setPropertyEvents] = useState<ChekinPropertyEvent[]>([]);
  const [bookingEvents, setBookingEvents] = useState<ChekinBookingEvent[]>([]);
  const [apiDataLoaded, setApiDataLoaded] = useState<boolean>(false);
  
  // Manteniamo sempre l'ultimo configId per i callback dell'SDK
  const configIdRef = useRef<string | number | null>(null);
  const initializedRef = useRef(false);
  const lastRefreshRef = useRef<number>(0); // Track last refresh time to prevent rapid calls

  // Fetch launch payload from backend
  useEffect(() => {
    let cancelled = false;
    (async () => {
      setLoading(true);
      try {
        const res = await apiClient.post(
          // Endpoint aggiornato secondo la documentazione: launch action
          "/integrations/chekin/config/launch/",
          {
            property_id: String(propertyId),
            reservation_id: reservationId ? String(reservationId) : null,
            reservation_details_mode: isReservationDetailsMode,
          },
        );
        if (!res.ok) throw new Error(`Avvio non riuscito (${res.status})`);
        const data = (await res.json()) as ChekinLaunchPayload;
        if (!cancelled) {
          setPayload(data);
          const cid = (data as any).configId ?? (data as any).config_id;
          if (cid) setConfigId(cid);
          setError(null);
        }
      } catch (e: any) {
        if (!cancelled) setError(e?.message || "Impossibile caricare Chekin");
      } finally {
        if (!cancelled) setLoading(false);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [propertyId, reservationId]);

  // Fetch Chekin API status data
  useEffect(() => {
    let cancelled = false;
    let timeoutId: NodeJS.Timeout;
    
    const fetchChekinData = async () => {
      try {
        // Fetch status summary
        const summary = await chekinApi.getStatusSummary();
        if (!cancelled) {
          setStatusSummary(summary);
        }

        // Fetch property events if we have a propertyId
        if (propertyId) {
          try {
            const propEvents = await chekinApi.getEventsByProperty(String(propertyId));
            if (!cancelled) {
              setPropertyEvents(propEvents);
            }
          } catch (propError) {
            console.warn('Property events not available:', propError);
          }
        }

        // Fetch booking events if we have a reservationId
        if (reservationId) {
          try {
            const bookEvents = await chekinApi.getEventsByBooking(String(reservationId));
            if (!cancelled) {
              setBookingEvents(bookEvents);
            }
          } catch (bookError) {
            console.warn('Booking events not available:', bookError);
          }
        }

        if (!cancelled) {
          setApiDataLoaded(true);
        }
      } catch (error) {
        console.error('Error fetching Chekin API data:', error);
        // Don't set error state here as this is supplementary data
        if (!cancelled) {
          setApiDataLoaded(false);
        }
      }
    };

    // Only fetch if we have authentication (payload indicates successful backend connection)
    // Add debouncing to prevent rapid successive calls
    if (payload) {
      timeoutId = setTimeout(() => {
        if (!cancelled) {
          fetchChekinData();
        }
      }, 300); // 300ms debounce
    }

    return () => {
      cancelled = true;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [payload, propertyId, reservationId]);

  // Se il payload non include ancora l'id di configurazione, tentiamo di recuperarlo (una sola volta)
  useEffect(() => {
    if (!payload) return;
    if (configId != null) return; // già noto
    let cancelled = false;
    (async () => {
      try {
        const res = await apiClient.get(
          `/integrations/chekin/config/?property=${encodeURIComponent(String(propertyId))}`,
        );
        if (!res.ok) return;
        const data = await res.json();
        if (
          !cancelled &&
          Array.isArray(data) &&
          data.length === 1 &&
          data[0]?.id != null
        ) {
          setConfigId(data[0].id);
        }
      } catch {
        // silenzioso: non blocca il widget
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [payload, configId, propertyId]);

  // Load SDK script and mount app
  useEffect(() => {
    if (!payload) return;

    // payload è cambiato: permettiamo una nuova inizializzazione
    initializedRef.current = false;

    const onLoad = () => {
      if (initializedRef.current) return; // evita doppia init
      if (!window.ChekinHousingsSDK) {
        setError("SDK Chekin non disponibile");
        return;
      }
      try {
        // Svuota il nodo di mount nel caso di re-initialization
        const mountNode = document.getElementById(rootId.current);
        if (mountNode) mountNode.innerHTML = "";

        const api = new window.ChekinHousingsSDK();
        const initConfig: any = {
          apiKey: payload.apiKey,
          defaultLanguage: "it",
          hiddenSections: payload.hiddenSections ?? [],
          hiddenFormFields: payload.hiddenFormFields ?? [],
          styles: payload.styles ?? "",
        };

        // In reservation details mode, ONLY include reservationId
        if (isReservationDetailsMode) {
          if (payload.reservationId) {
            initConfig.reservationId = payload.reservationId;
          }
        } else {
          // Normal mode: include housing/property IDs and optionally reservationId
          initConfig.housingId = payload.housingId ?? undefined;
          initConfig.externalHousingId = payload.externalHousingId ?? undefined;
          if (payload.reservationId) {
            initConfig.reservationId = payload.reservationId;
          }
        }

        // Add callback functions
        initConfig.onPoliceAccountConnection = async (connected: boolean) => {
          const cid = configIdRef.current;
          if (cid == null) return;
          
          // Update backend status
          await apiClient.post(
            `/integrations/chekin/config/${encodeURIComponent(String(cid))}/status/`,
            { type: connected ? "police_connected" : "police_disconnected" },
          );
          
          // Throttle API refresh calls to prevent rapid successive requests
          const now = Date.now();
          if (now - lastRefreshRef.current < 2000) { // 2 second throttle
            console.log('Throttling Chekin API refresh - too soon since last call');
            return;
          }
          lastRefreshRef.current = now;
          
          // Force refresh to get latest data after connection change
          try {
            const refreshedData = await chekinApi.forceRefresh(
              propertyId ? String(propertyId) : undefined,
              reservationId ? String(reservationId) : undefined
            );
            
            setStatusSummary(refreshedData.statusSummary);
            if (refreshedData.propertyEvents) {
              setPropertyEvents(refreshedData.propertyEvents);
            }
            if (refreshedData.bookingEvents) {
              setBookingEvents(refreshedData.bookingEvents);
            }
          } catch (error) {
            console.warn('Failed to refresh data after police connection change:', error);
          }
        };

        initConfig.onStatAccountConnection = async (connected: boolean) => {
          const cid = configIdRef.current;
          if (cid == null) return;
          
          // Update backend status
          await apiClient.post(
            `/integrations/chekin/config/${encodeURIComponent(String(cid))}/status/`,
            { type: connected ? "istat_connected" : "istat_disconnected" },
          );
          
          // Throttle API refresh calls to prevent rapid successive requests
          const now = Date.now();
          if (now - lastRefreshRef.current < 2000) { // 2 second throttle
            console.log('Throttling Chekin API refresh - too soon since last call');
            return;
          }
          lastRefreshRef.current = now;
          
          // Force refresh to get latest data after connection change
          try {
            const refreshedData = await chekinApi.forceRefresh(
              propertyId ? String(propertyId) : undefined,
              reservationId ? String(reservationId) : undefined
            );
            
            setStatusSummary(refreshedData.statusSummary);
            if (refreshedData.propertyEvents) {
              setPropertyEvents(refreshedData.propertyEvents);
            }
            if (refreshedData.bookingEvents) {
              setBookingEvents(refreshedData.bookingEvents);
            }
          } catch (error) {
            console.warn('Failed to refresh data after stat connection change:', error);
          }
        };

        api.initialize(initConfig);
        api.renderApp({ targetNode: rootId.current });
        initializedRef.current = true;
      } catch (err: any) {
        setError(err?.message || "Impossibile inizializzare Chekin");
      }
    };

    const onError = () => setError("Caricamento dell'SDK Chekin non riuscito");

    // Se l'SDK è già disponibile, esegui direttamente l'onLoad
    if (window.ChekinHousingsSDK) {
      onLoad();
      return;
    }

    // Se esiste già uno script con la stessa src, riusa i listener
    const existingScript = document.querySelector(
      `script[src="${payload.sdkUrl}"]`,
    ) as HTMLScriptElement | null;
    if (existingScript) {
      existingScript.addEventListener("load", onLoad);
      existingScript.addEventListener("error", onError);
      return () => {
        existingScript.removeEventListener("load", onLoad);
        existingScript.removeEventListener("error", onError);
      };
    }

    const script = document.createElement("script");
    script.src = payload.sdkUrl;
    script.async = true;
    script.referrerPolicy = "strict-origin-when-cross-origin";
    script.addEventListener("load", onLoad);
    script.addEventListener("error", onError);
    document.body.appendChild(script);

    return () => {
      script.removeEventListener("load", onLoad);
      script.removeEventListener("error", onError);
      // Non rimuoviamo lo script per permettere riutilizzo in altre viste
    };
  }, [payload]);

  // Mantieni aggiornato il ref con l'ultimo configId
  useEffect(() => {
    configIdRef.current = configId;
  }, [configId]);

  // Method to manually refresh Chekin API data
  const refreshChekinData = async () => {
    // Throttle manual refresh calls as well
    const now = Date.now();
    if (now - lastRefreshRef.current < 1000) { // 1 second throttle for manual calls
      console.log('Throttling manual Chekin API refresh - too soon since last call');
      return;
    }
    lastRefreshRef.current = now;

    try {
      const refreshedData = await chekinApi.forceRefresh(
        propertyId ? String(propertyId) : undefined,
        reservationId ? String(reservationId) : undefined
      );
      
      setStatusSummary(refreshedData.statusSummary);
      if (refreshedData.propertyEvents) {
        setPropertyEvents(refreshedData.propertyEvents);
      }
      if (refreshedData.bookingEvents) {
        setBookingEvents(refreshedData.bookingEvents);
      }
      setApiDataLoaded(true);
    } catch (error) {
      console.error('Error refreshing Chekin data:', error);
      setApiDataLoaded(false);
    }
  };

  // Expose API data and methods for parent components
  const chekinApiData = {
    statusSummary,
    propertyEvents,
    bookingEvents,
    apiDataLoaded,
    refreshData: refreshChekinData,
  };

  // Notify parent component when API data changes
  useEffect(() => {
    if (onApiDataChange) {
      onApiDataChange(chekinApiData);
    }
  }, [statusSummary, propertyEvents, bookingEvents, apiDataLoaded, onApiDataChange]);

  if (error)
    return <div className="text-red-600 text-sm leading-relaxed">{error}</div>;

  return (
    <div className={className ?? ""}>
      {loading || !payload ? (
        <Loader1 className="w-full h-full" />
      ) : (
        <div className="relative">
          {/* API Status Indicator */}
          {apiDataLoaded && statusSummary && (
            <div className="absolute top-2 right-2 z-10 bg-green-100 border border-green-300 rounded-full px-2 py-1">
              <span className="text-xs text-green-700 font-medium">
                API: {statusSummary.total_events} events
              </span>
            </div>
          )}
          
          <div id={rootId.current} />
        </div>
      )}
    </div>
  );
}
