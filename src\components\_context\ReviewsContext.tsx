"use client";

import React, { createContext, useState } from "react";
import {
  getReviews,
  markReviewAsRead,
  markAllReviewsAsRead,
} from "@/services/api";

interface Review {
  id: string;
  external_review_id: string;
  property: string;
  booking: string | null;
  public_review: string;
  private_feedback: string;
  ratings: Record<string, any>;
  reviewer_id: string;
  reviewer_role: string;
  reviewee_id: string;
  reviewee_role: string;
  channel_id: string;
  thread_id: string | null;
  is_hidden: boolean;
  submitted_at: string;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
  responses: any[];
  is_read?: boolean;
}

interface ReviewsContextType {
  reviews: Review[];
  isLoadingReviews: boolean;
  fetchReviews: () => Promise<void>;
  markAsRead: (reviewId: string) => Promise<boolean>;
  markAllAsRead: () => Promise<boolean>;
}

export const ReviewsContext = createContext<ReviewsContextType>({
  reviews: [],
  isLoadingReviews: true,
  fetchReviews: async () => {},
  markAsRead: async () => false,
  markAllAsRead: async () => false,
});

export const ReviewsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoadingReviews, setIsLoadingReviews] = useState<boolean>(false);

  const fetchReviews = async () => {
    setIsLoadingReviews(true);
    try {
      const data = await getReviews();
      setReviews(data);
    } catch (error) {
      console.error("Error fetching reviews:", error);
    }
    setIsLoadingReviews(false);
  };

  const markAsRead = async (reviewId: string) => {
    try {
      const response = await markReviewAsRead(reviewId);
      if (response.status === 200 && response.data.is_read) {
        // Update the local state to reflect the change
        setReviews((prev) =>
          prev.map((review) =>
            review.id === reviewId ? { ...review, is_read: true } : review,
          ),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error marking review as read:", error);
      return false;
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await markAllReviewsAsRead();
      if (response.status === 200) {
        // Update all reviews to be marked as read
        setReviews((prev) =>
          prev.map((review) => ({ ...review, is_read: true })),
        );
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error marking all reviews as read:", error);
      return false;
    }
  };

  return (
    <ReviewsContext.Provider
      value={{
        reviews,
        isLoadingReviews,
        fetchReviews,
        markAsRead,
        markAllAsRead,
      }}
    >
      {children}
    </ReviewsContext.Provider>
  );
};
