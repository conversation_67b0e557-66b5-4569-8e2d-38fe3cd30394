import { useState, useCallback, useEffect } from "react";
import {
  getPendingBalance,
  getTotalEarnings,
  getFinancialSummary,
  getPaymentStatus,
  getPaymentCycleInfo,
  getCommissionBreakdown,
  getHistoricalEarnings,
  FinancialSummary,
  PaymentStatus,
  PaymentCycle,
  Reservation,
  HistoricalEarnings,
} from "@/services/financialApi";

interface UseFinancialDataOptions {
  propertyIds?: string[];
  autoRefresh?: boolean;
  refreshInterval?: number; // in milliseconds
}

interface FinancialState {
  summary: FinancialSummary | null;
  paymentStatus: PaymentStatus | null;
  paymentCycle: PaymentCycle | null;
  commissionBreakdown: any;
  recentReservations: Reservation[];
  historicalEarnings: HistoricalEarnings | null;
  loading: {
    summary: boolean;
    paymentStatus: boolean;
    paymentCycle: boolean;
    commissionBreakdown: boolean;
    historicalEarnings: boolean;
  };
  errors: {
    summary: string | null;
    paymentStatus: string | null;
    paymentCycle: string | null;
    commissionBreakdown: string | null;
    historicalEarnings: string | null;
  };
  lastRefresh: Date | null;
}

export const useFinancialData = (options: UseFinancialDataOptions = {}) => {
  const {
    propertyIds = [],
    autoRefresh = true,
    refreshInterval = 15 * 60 * 1000, // 15 minutes
  } = options;

  const [state, setState] = useState<FinancialState>({
    summary: null,
    paymentStatus: null,
    paymentCycle: null,
    commissionBreakdown: null,
    recentReservations: [],
    historicalEarnings: null,
    loading: {
      summary: false,
      paymentStatus: false,
      paymentCycle: false,
      commissionBreakdown: false,
      historicalEarnings: false,
    },
    errors: {
      summary: null,
      paymentStatus: null,
      paymentCycle: null,
      commissionBreakdown: null,
      historicalEarnings: null,
    },
    lastRefresh: null,
  });

  const [refreshing, setRefreshing] = useState(false);

  // Fetch financial summary
  const fetchSummary = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, summary: true },
      errors: { ...prev.errors, summary: null },
    }));

    try {
      const summary = await getFinancialSummary(
        propertyIds.length > 0 ? propertyIds : undefined,
      );
      setState((prev) => ({
        ...prev,
        summary,
        loading: { ...prev.loading, summary: false },
      }));
      return summary;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch financial summary";
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, summary: false },
        errors: { ...prev.errors, summary: errorMessage },
      }));
      throw error;
    }
  }, [propertyIds]);

  // Fetch payment status
  const fetchPaymentStatus = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, paymentStatus: true },
      errors: { ...prev.errors, paymentStatus: null },
    }));

    try {
      const paymentStatus = await getPaymentStatus(
        undefined,
        propertyIds.length > 0 ? propertyIds : undefined,
        50,
      );

      // Extract recent reservations
      const allReservations = [
        ...paymentStatus.payment_in_progress.reservations,
        ...paymentStatus.future_payment.reservations,
      ];

      const sortedReservations = allReservations.sort(
        (a, b) =>
          new Date(b.checkout_date).getTime() -
          new Date(a.checkout_date).getTime(),
      );

      setState((prev) => ({
        ...prev,
        paymentStatus,
        recentReservations: sortedReservations.slice(0, 20),
        loading: { ...prev.loading, paymentStatus: false },
      }));

      return paymentStatus;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment status";
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, paymentStatus: false },
        errors: { ...prev.errors, paymentStatus: errorMessage },
      }));
      throw error;
    }
  }, [propertyIds]);

  // Fetch payment cycle info
  const fetchPaymentCycle = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      loading: { ...prev.loading, paymentCycle: true },
      errors: { ...prev.errors, paymentCycle: null },
    }));

    try {
      const paymentCycle = await getPaymentCycleInfo();
      setState((prev) => ({
        ...prev,
        paymentCycle,
        loading: { ...prev.loading, paymentCycle: false },
      }));
      return paymentCycle;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch payment cycle info";
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, paymentCycle: false },
        errors: { ...prev.errors, paymentCycle: errorMessage },
      }));
      throw error;
    }
  }, []);

  // Fetch commission breakdown
  const fetchCommissionBreakdown = useCallback(
    async (period?: "last_30_days" | "last_90_days" | "ytd" | "all_time") => {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, commissionBreakdown: true },
        errors: { ...prev.errors, commissionBreakdown: null },
      }));

      try {
        const commissionBreakdown = await getCommissionBreakdown(
          propertyIds.length > 0 ? propertyIds : undefined,
          period,
        );
        setState((prev) => ({
          ...prev,
          commissionBreakdown,
          loading: { ...prev.loading, commissionBreakdown: false },
        }));
        return commissionBreakdown;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to fetch commission breakdown";
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, commissionBreakdown: false },
          errors: { ...prev.errors, commissionBreakdown: errorMessage },
        }));
        throw error;
      }
    },
    [propertyIds],
  );

  // Fetch historical earnings for trend calculation
  const fetchHistoricalEarnings = useCallback(
    async (period: "last_30_days" | "last_90_days" = "last_30_days") => {
      setState((prev) => ({
        ...prev,
        loading: { ...prev.loading, historicalEarnings: true },
        errors: { ...prev.errors, historicalEarnings: null },
      }));

      try {
        const historicalEarnings = await getHistoricalEarnings(
          propertyIds.length > 0 ? propertyIds : undefined,
          period,
        );
        setState((prev) => ({
          ...prev,
          historicalEarnings,
          loading: { ...prev.loading, historicalEarnings: false },
        }));
        return historicalEarnings;
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to fetch historical earnings";
        setState((prev) => ({
          ...prev,
          loading: { ...prev.loading, historicalEarnings: false },
          errors: { ...prev.errors, historicalEarnings: errorMessage },
        }));
        throw error;
      }
    },
    [propertyIds],
  );

  // Refresh all data
  const refreshAll = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.allSettled([
        fetchSummary(),
        fetchPaymentStatus(),
        fetchPaymentCycle(),
        fetchCommissionBreakdown(),
        fetchHistoricalEarnings(),
      ]);
      setState((prev) => ({ ...prev, lastRefresh: new Date() }));
    } finally {
      setRefreshing(false);
    }
  }, [
    fetchSummary,
    fetchPaymentStatus,
    fetchPaymentCycle,
    fetchCommissionBreakdown,
    fetchHistoricalEarnings,
  ]);

  // Initial load
  useEffect(() => {
    refreshAll();
  }, [refreshAll]);

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      if (!refreshing) {
        fetchSummary();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshing, fetchSummary]);

  // Computed values
  const isLoading = Object.values(state.loading).some((loading) => loading);
  const hasErrors = Object.values(state.errors).some((error) => error !== null);

  const nextPaymentCountdown = state.paymentCycle
    ? (() => {
        const daysUntil = state.paymentCycle.days_until_next_payment;
        if (daysUntil === 0) return "Today";
        if (daysUntil === 1) return "Tomorrow";
        return `${daysUntil} days`;
      })()
    : null;

  return {
    // Data
    ...state,

    // Computed values
    isLoading,
    hasErrors,
    refreshing,
    nextPaymentCountdown,

    // Actions
    refreshAll,
    fetchSummary,
    fetchPaymentStatus,
    fetchPaymentCycle,
    fetchCommissionBreakdown,
    fetchHistoricalEarnings,
  };
};
