"use client";
import React, { useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import Button from "@/components/buttons/Button";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import { useRouter } from "next/navigation";
import { getBilling, updateBilling } from "@/services/api";
import toast from "react-hot-toast";

// IBAN validation regex
const IBAN_REGEX = /^[A-Z]{2}\d{2}[A-Z0-9]{10,30}$/;

// Format IBAN with spaces for better readability
const formatIban = (value: string) => {
  // Remove all spaces first
  const cleanValue = value.replace(/\s/g, "");
  // Add a space every 4 characters
  return cleanValue.replace(/(.{4})/g, "$1 ").trim();
};

function Page() {
  const router = useRouter();
  const [iban, setIban] = useState("");
  const [originalIban, setOriginalIban] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [billingProfileId, setBillingProfileId] = useState("");

  // Track if IBAN has been changed
  const hasChanged =
    iban.replace(/\s/g, "") !== originalIban.replace(/\s/g, "");

  // Validate IBAN format
  const validateIban = (value: string): boolean => {
    const cleanIban = value.replace(/\s/g, "");
    return IBAN_REGEX.test(cleanIban);
  };

  const isValidIban = validateIban(iban);

  // Button should be enabled only when IBAN is valid and has changed
  const isButtonEnabled = hasChanged && isValidIban && !isLoading;

  const handleFetchIban = async () => {
    try {
      setIsLoading(true);
      const response = await getBilling();

      if (
        response.status === 200 &&
        response.data &&
        response.data.length > 0
      ) {
        const billingData = response.data[0];
        const formattedIban = formatIban(billingData.iban || "");

        setIban(formattedIban);
        setOriginalIban(billingData.iban || "");
        setBillingProfileId(billingData.id);
      } else {
        setError("Impossibile recuperare le informazioni bancarie");
        toast.error("Impossibile recuperare le informazioni bancarie");
      }
    } catch (err) {
      console.error("Error fetching billing information:", err);
      setError(
        "Si è verificato un errore durante il recupero delle informazioni bancarie",
      );
      toast.error(
        "Si è verificato un errore durante il recupero delle informazioni bancarie",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const updateIban = async () => {
    if (!isButtonEnabled) return;

    try {
      setIsLoading(true);
      setError("");

      const response = await updateBilling({
        iban: iban,
        billingProfileId: billingProfileId,
      });

      if (response.success) {
        toast.success("IBAN aggiornato con successo");
        // Update original IBAN to match the new one
        setOriginalIban(iban.replace(/\s/g, ""));
      } else {
        const errorMessage =
          response.data?.error || "Errore durante l'aggiornamento dell'IBAN";
        setError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (err) {
      console.error("Error updating IBAN:", err);
      setError("Si è verificato un errore durante l'aggiornamento dell'IBAN");
      toast.error(
        "Si è verificato un errore durante l'aggiornamento dell'IBAN",
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleFetchIban();
  }, []);

  return (
    <MobilePageStart>
      <HeaderPage
        title="Informazioni Bancarie"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
        <div className="flex flex-col gap-4">
          <InputLabelDescription
            label="IBAN"
            isTextArea
            placeholder="Es. ***************************"
            value={iban}
            onChange={(value, e) => {
              // Preserve caret while formatting with spaces every 4 chars
              const target = e?.target as
                | HTMLTextAreaElement
                | HTMLInputElement
                | undefined;
              const prevSelectionStart = target?.selectionStart ?? value.length;
              const rawBefore = value.slice(0, prevSelectionStart);
              const cleanedBefore = rawBefore.replace(/\s/g, "");

              const formatted = formatIban(value.toUpperCase());
              setIban(formatted);
              setError("");

              // After state update, defer caret reset
              if (target) {
                requestAnimationFrame(() => {
                  const newIndex = (() => {
                    // Reconstruct mapping: we know how many groups of 4 will precede the cleaned length
                    const groups = Math.floor(cleanedBefore.length / 4);
                    const remainder = cleanedBefore.length % 4;
                    return groups * 5 + remainder; // each full group adds 5 chars (4 + space)
                  })();
                  try {
                    target.selectionStart = target.selectionEnd = Math.min(
                      newIndex,
                      formatted.length,
                    );
                  } catch {
                    /* ignore */
                  }
                });
              }
            }}
            error={error}
          />

          {/* Help text */}
          <div className="text-sm text-gray-600 -mt-2">
            <p>Inserisci il tuo IBAN completo, incluso il codice del paese.</p>
          </div>

          {/* Validation status */}
          {iban && (
            <div
              className={`text-sm ${isValidIban ? "text-green-600" : "text-red-600"} -mt-2`}
            >
              {isValidIban
                ? "Formato IBAN valido"
                : "Formato IBAN non valido. Verifica che inizi con il codice del paese (es. IT) seguito da numeri e lettere."}
            </div>
          )}
        </div>

        <Button
          color="white"
          backgroundColor={isButtonEnabled ? "var(--blue)" : "#9e9e9e"}
          text={isLoading ? "Aggiornamento..." : "Aggiorna IBAN"}
          fontSize="14px"
          onClick={updateIban}
          disabled={!isButtonEnabled}
        />

        {!hasChanged && (
          <p className="text-sm text-center text-gray-500">
            Modifica l'IBAN per abilitare il pulsante di aggiornamento
          </p>
        )}
      </div>
    </MobilePageStart>
  );
}

export default Page;
