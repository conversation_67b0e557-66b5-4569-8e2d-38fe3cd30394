"use client";

import React from "react";
import { useRouter } from "next/navigation";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderLogo from "@/components/_globals/headerLogo";
import Button from "@/components/buttons/Button";
import styles from "./not-found.module.css";
import { AlertCircle, HomeIcon } from "lucide-react";

export default function NotFound() {
  const router = useRouter();

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderLogo />
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.errorIcon}>
            <AlertCircle className="h-16 w-16 text-[#113158]" />
          </div>

          <div className={styles.errorCode}>404</div>

          <div className={styles.illustration}>
            <svg
              width="180"
              height="180"
              viewBox="0 0 200 200"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 180C144.183 180 180 144.183 180 100C180 55.8172 144.183 20 100 20C55.8172 20 20 55.8172 20 100C20 144.183 55.8172 180 100 180Z"
                fill="#113158"
                fillOpacity="0.1"
              />
              <path
                d="M100 160C133.137 160 160 133.137 160 100C160 66.8629 133.137 40 100 40C66.8629 40 40 66.8629 40 100C40 133.137 66.8629 160 100 160Z"
                stroke="#113158"
                strokeWidth="4"
              />
              <path
                d="M65 85C70.5228 85 75 80.5228 75 75C75 69.4772 70.5228 65 65 65C59.4772 65 55 69.4772 55 75C55 80.5228 59.4772 85 65 85Z"
                fill="#113158"
              />
              <path
                d="M135 85C140.523 85 145 80.5228 145 75C145 69.4772 140.523 65 135 65C129.477 65 125 69.4772 125 75C125 80.5228 129.477 85 135 85Z"
                fill="#113158"
              />
              <path
                d="M65 130C82.6731 110 117.327 110 135 130"
                stroke="#113158"
                strokeWidth="4"
                strokeLinecap="round"
              />
              <path
                d="M160 40L40 160"
                stroke="#FCBD4C"
                strokeWidth="6"
                strokeLinecap="round"
              />
              <path
                d="M40 40L160 160"
                stroke="#FCBD4C"
                strokeWidth="6"
                strokeLinecap="round"
              />
            </svg>
          </div>

          <h1 className={styles.title}>Pagina non trovata</h1>
          <p className={styles.description}>
            La pagina che stai cercando non esiste o è stata spostata. Verifica
            l'URL o torna alla dashboard.
          </p>

          <div className={styles.buttonContainer}>
            <Button
              text="Torna alla Dashboard"
              color="white"
              backgroundColor="var(--blue)"
              icon={<HomeIcon width={17} />}
              fontSize="14px"
              onClick={() => router.push("/dashboard")}
            />
          </div>
        </div>
      </div>
    </MobilePageStart>
  );
}
