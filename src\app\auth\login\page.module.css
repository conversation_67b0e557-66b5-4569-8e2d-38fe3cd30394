.main {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.title {
  color: var(--blue);
  font-size: 36px;
  font-weight: 600;
  line-height: 43px;
  margin-bottom: 30px;
}

.form {
  width: 100%;
  padding: 10px;

  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
}

.link {
  text-decoration: underline;
  font-size: 14px;
  font-weight: 500;
  color: var(--accent);
}

.link2 {
  margin-top: 30px;
  font-size: 14px;
  font-weight: 300;
  color: var(--blue);
}

.link3 {
  margin-top: 20px;
  font-size: 13px;
  font-weight: 300;
  color: var(--blue);
}

.link4 {
  font-size: 13px;
  font-weight: 300;
  color: var(--blue);
}

.divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 1rem 0;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid #e5e7eb;
}

.divider span {
  padding: 0 1rem;
  color: #6b7280;
  font-size: 0.875rem;
}
