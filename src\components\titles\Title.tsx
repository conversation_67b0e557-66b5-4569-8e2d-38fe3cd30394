import { Plus } from "lucide-react";
import styles from "./title.module.css";
import React, { act, CSSProperties } from "react";
import Skeleton from "../loaders/Skeleton";

function Title(props: {
  style?: CSSProperties;
  title?: string;
  subtitle?: string;
  actionRight?: React.ReactNode;
  onClick?: () => void;
  isLoading?: boolean;
  textColor?: string;
}) {
  if (props.isLoading) {
    return (
      <div className={styles.containerTitle} style={{ color: "black" }}>
        <div className="flex flex-col gap-[7px]">
          <div className="flex items-center justify-center w-[170px] h-[24px]">
            <Skeleton />
          </div>
          {props.subtitle && (
            <div className="flex items-center justify-center w-[130px] h-[20px]">
              <Skeleton />
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={styles.containerTitle} style={props.style}>
      <div
        style={{
          width: props.style?.textAlign === "center" ? "100%" : "auto",
          display: "flex",
          flexDirection: "column",
          alignItems:
            props.style?.textAlign === "center" ? "center" : "flex-start",
        }}
      >
        <p
          className={styles.title}
          style={{
            color: props.textColor ? props.textColor : "black",
            justifyContent:
              props.style?.textAlign === "center" ? "center" : "flex-start",
            width: "100%",
            textAlign: props.style?.textAlign as any,
          }}
        >
          <span
            style={{
              opacity: props.isLoading ? 0 : 1,
            }}
          >
            {props.title && props.title}
          </span>
        </p>
        {props.subtitle && (
          <p
            className={styles.subtitle}
            style={{
              justifyContent:
                props.style?.textAlign === "center" ? "center" : "flex-start",
              width: "100%",
              textAlign: props.style?.textAlign as any,
            }}
          >
            {props.subtitle}
          </p>
        )}
      </div>
      {props.actionRight && props.actionRight}
    </div>
  );
}

export default Title;
