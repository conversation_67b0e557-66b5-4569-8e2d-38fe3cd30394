import React from "react";
import {
  ArrowR<PERSON>,
  BanknoteIcon,
  CheckIcon,
  Link,
  UserIcon,
  XIcon,
} from "lucide-react";
import { useRouter } from "next/navigation";

interface ButtonSectionProps {
  ticker?: boolean;
  picture?: boolean;
  picture2?: boolean;
  completed?: boolean;
  title: string;
  desciption?: string;
  href?: string;
  onClick?: () => void;

  noIcon?: boolean;
  backgroundColor?: string;

  deactivate?: boolean;
}

const ButtonSection = ({
  ticker,
  picture,
  picture2,
  completed,
  title,
  desciption,
  href,
  onClick,

  noIcon,
  backgroundColor,

  deactivate,
}: ButtonSectionProps) => {
  const router = useRouter();

  return (
    <div
      onClick={() => {
        href && router.push(href);
        onClick && onClick();
      }}
      className="flex flex-row bg-[white] p-3 rounded-[15px] items-center justify-between w-full"
      style={{
        boxShadow: "0 0 5px 15pxrgba(0, 0, 0, 0.19)",
        backgroundColor: `${backgroundColor ? backgroundColor : "white"}`,
        pointerEvents: deactivate ? "none" : "all",
        opacity: deactivate ? "0.5" : "1",
      }}
    >
      <div className="flex flex-row items-center gap-3">
        {/* Ticker green/red */}
        {ticker && (
          <div
            style={{
              minHeight: "22px",
              minWidth: "22px",
              borderRadius: "50%",
              background: `${completed ? "yellowgreen" : "tomato"}`,
            }}
            className="flex flex-row items-center justify-center"
          >
            {completed ? (
              <CheckIcon width={17} color="white" />
            ) : (
              <XIcon width={17} color="white" />
            )}
          </div>
        )}
        {picture && (
          <div
            style={{
              minHeight: "40px",
              minWidth: "40px",
              borderRadius: "50%",
              background: `${completed ? "yellowgreen" : "tomato"}`,
            }}
            className="flex flex-row items-center justify-center"
          >
            {!picture2 ? (
              <UserIcon color="white" />
            ) : (
              <BanknoteIcon color="white" />
            )}
          </div>
        )}
        <div>
          <p className="text-[14px]">{title}</p>
          {desciption && (
            <p className="text-[12px] text-[#********] mt-1">{desciption}</p>
          )}
        </div>
      </div>

      {!noIcon && <ArrowRight width={20} color="black" />}
    </div>
  );
};

export default ButtonSection;
