"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = React.useState({
    transportcard: false,
    transports: false,
    ebikerecharge: false,
    evrecharge: false,
    bike: false,
    ebike: false,
    efficiencyglass: false,
    greenroof: false,
    roofisolation: false,
    wallisolation: false,
    floorisolation: false,
    naturalisolationmaterials: false,
    heater: false,
    infrared: false,
    geothermal: false,
    thermalsolar: false,
    solarheater: false,
    recicleglass: false,
    reciclepaper: false,
    recicleplastic: false,
    recicleorganic: false,
    ledlights: false,
    renewableenergy: false,
    fotovoltaic: false,
    windturbines: false,
    methanedigestor: false,
    smartcontrols: false,
    ecoproducts: false,
    savewatershower: false,
    savewaterwc: false,
    savewatertap: false,
    collectwater: false,
    greywater: false,
    honey: false,
    fruit: false,
    verdure: false,
    herbs: false,
    oil: false,
    jam: false,
    eggs: false,
    cheese: false,
    bread: false,
    nuts: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: "Transportcard",
        category: "Sustainability",
        is_available: data.transportcard ?? false,
      },
      {
        name: "Transports",
        category: "Sustainability",
        is_available: data.transports ?? false,
      },
      {
        name: "Ebikerecharge",
        category: "Sustainability",
        is_available: data.ebikerecharge ?? false,
      },
      {
        name: "Evrecharge",
        category: "Sustainability",
        is_available: data.evrecharge ?? false,
      },
      {
        name: "Bike",
        category: "Sustainability",
        is_available: data.bike ?? false,
      },
      {
        name: "Ebike",
        category: "Sustainability",
        is_available: data.ebike ?? false,
      },
      {
        name: "Efficiencyglass",
        category: "Sustainability",
        is_available: data.efficiencyglass ?? false,
      },
      {
        name: "Greenroof",
        category: "Sustainability",
        is_available: data.greenroof ?? false,
      },
      {
        name: "Roofisolation",
        category: "Sustainability",
        is_available: data.roofisolation ?? false,
      },
      {
        name: "Wallisolation",
        category: "Sustainability",
        is_available: data.wallisolation ?? false,
      },
      {
        name: "Floorisolation",
        category: "Sustainability",
        is_available: data.floorisolation ?? false,
      },
      {
        name: "Naturalisolationmaterials",
        category: "Sustainability",
        is_available: data.naturalisolationmaterials ?? false,
      },
      {
        name: "Heater",
        category: "Sustainability",
        is_available: data.heater ?? false,
      },
      {
        name: "Infrared",
        category: "Sustainability",
        is_available: data.infrared ?? false,
      },
      {
        name: "Geothermal",
        category: "Sustainability",
        is_available: data.geothermal ?? false,
      },
      {
        name: "Thermalsolar",
        category: "Sustainability",
        is_available: data.thermalsolar ?? false,
      },
      {
        name: "Solarheater",
        category: "Sustainability",
        is_available: data.solarheater ?? false,
      },
      {
        name: "Recicleglass",
        category: "Sustainability",
        is_available: data.recicleglass ?? false,
      },
      {
        name: "Reciclepaper",
        category: "Sustainability",
        is_available: data.reciclepaper ?? false,
      },
      {
        name: "Recicleplastic",
        category: "Sustainability",
        is_available: data.recicleplastic ?? false,
      },
      {
        name: "Recicleorganic",
        category: "Sustainability",
        is_available: data.recicleorganic ?? false,
      },
      {
        name: "Ledlights",
        category: "Sustainability",
        is_available: data.ledlights ?? false,
      },
      {
        name: "Renewableenergy",
        category: "Sustainability",
        is_available: data.renewableenergy ?? false,
      },
      {
        name: "Fotovoltaic",
        category: "Sustainability",
        is_available: data.fotovoltaic ?? false,
      },
      {
        name: "Windturbines",
        category: "Sustainability",
        is_available: data.windturbines ?? false,
      },
      {
        name: "Methanedigestor",
        category: "Sustainability",
        is_available: data.methanedigestor ?? false,
      },
      {
        name: "Smartcontrols",
        category: "Sustainability",
        is_available: data.smartcontrols ?? false,
      },
      {
        name: "Ecoproducts",
        category: "Sustainability",
        is_available: data.ecoproducts ?? false,
      },
      {
        name: "Savewatershower",
        category: "Sustainability",
        is_available: data.savewatershower ?? false,
      },
      {
        name: "Savewaterwc",
        category: "Sustainability",
        is_available: data.savewaterwc ?? false,
      },
      {
        name: "Savewatertap",
        category: "Sustainability",
        is_available: data.savewatertap ?? false,
      },
      {
        name: "Collectwater",
        category: "Sustainability",
        is_available: data.collectwater ?? false,
      },
      {
        name: "Greywater",
        category: "Sustainability",
        is_available: data.greywater ?? false,
      },
      {
        name: "Honey",
        category: "Sustainability",
        is_available: data.honey ?? false,
      },
      {
        name: "Fruit",
        category: "Sustainability",
        is_available: data.fruit ?? false,
      },
      {
        name: "Verdure",
        category: "Sustainability",
        is_available: data.verdure ?? false,
      },
      {
        name: "Herbs",
        category: "Sustainability",
        is_available: data.herbs ?? false,
      },
      {
        name: "Oil",
        category: "Sustainability",
        is_available: data.oil ?? false,
      },
      {
        name: "Jam",
        category: "Sustainability",
        is_available: data.jam ?? false,
      },
      {
        name: "Eggs",
        category: "Sustainability",
        is_available: data.eggs ?? false,
      },
      {
        name: "Cheese",
        category: "Sustainability",
        is_available: data.cheese ?? false,
      },
      {
        name: "Bread",
        category: "Sustainability",
        is_available: data.bread ?? false,
      },
      {
        name: "Nuts",
        category: "Sustainability",
        is_available: data.nuts ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };

  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;

    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Sostenibilità"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Tessera Trasporti"
            value={data.transportcard}
            onChange={(value) => {
              setData({
                ...data,
                transportcard: value,
              });
              handleUpdateAmenities({
                ...data,
                transportcard: value,
              });
            }}
          />

          <InputOnOff
            title="Trasporto Pubblico"
            description="Trasporto pubblico nelle vicinanze, raggiungibile a piedi"
            value={data.transports}
            onChange={(value) => {
              setData({
                ...data,
                transports: value,
              });
              handleUpdateAmenities({
                ...data,
                transports: value,
              });
            }}
          />

          <InputOnOff
            title="Stazione Ricarica eBike"
            value={data.ebikerecharge}
            onChange={(value) => {
              setData({
                ...data,
                ebikerecharge: value,
              });
              handleUpdateAmenities({
                ...data,
                ebikerecharge: value,
              });
            }}
          />

          <InputOnOff
            title="Stazione Ricarica EV"
            value={data.evrecharge}
            onChange={(value) => {
              setData({
                ...data,
                evrecharge: value,
              });
              handleUpdateAmenities({
                ...data,
                evrecharge: value,
              });
            }}
          />

          <InputOnOff
            title="Bici"
            value={data.bike}
            onChange={(value) => {
              setData({
                ...data,
                bike: value,
              });
              handleUpdateAmenities({
                ...data,
                bike: value,
              });
            }}
          />

          <InputOnOff
            title="eBike"
            value={data.ebike}
            onChange={(value) => {
              setData({
                ...data,
                ebike: value,
              });
              handleUpdateAmenities({
                ...data,
                ebike: value,
              });
            }}
          />

          <InputOnOff
            title="Vetro ad alta efficienza"
            value={data.efficiencyglass}
            onChange={(value) => {
              setData({
                ...data,
                efficiencyglass: value,
              });
              handleUpdateAmenities({
                ...data,
                efficiencyglass: value,
              });
            }}
          />

          <InputOnOff
            title="Tetto verde/freddo"
            value={data.greenroof}
            onChange={(value) => {
              setData({
                ...data,
                greenroof: value,
              });
              handleUpdateAmenities({
                ...data,
                greenroof: value,
              });
            }}
          />

          <InputOnOff
            title="Isolazione Tetto"
            value={data.roofisolation}
            onChange={(value) => {
              setData({
                ...data,
                roofisolation: value,
              });
              handleUpdateAmenities({
                ...data,
                roofisolation: value,
              });
            }}
          />

          <InputOnOff
            title="Isolazione Muri"
            value={data.wallisolation}
            onChange={(value) => {
              setData({
                ...data,
                wallisolation: value,
              });
              handleUpdateAmenities({
                ...data,
                wallisolation: value,
              });
            }}
          />

          <InputOnOff
            title="Isolazione Pavimento"
            value={data.floorisolation}
            onChange={(value) => {
              setData({
                ...data,
                floorisolation: value,
              });
              handleUpdateAmenities({
                ...data,
                floorisolation: value,
              });
            }}
          />

          <InputOnOff
            title="Materiali da Isolamento naturali"
            value={data.naturalisolationmaterials}
            onChange={(value) => {
              setData({
                ...data,
                naturalisolationmaterials: value,
              });
              handleUpdateAmenities({
                ...data,
                naturalisolationmaterials: value,
              });
            }}
          />

          <InputOnOff
            title="Caldaia"
            value={data.heater}
            onChange={(value) => {
              setData({
                ...data,
                heater: value,
              });
              handleUpdateAmenities({
                ...data,
                heater: value,
              });
            }}
          />

          <InputOnOff
            title="Pannelli Raggi Infrarossi"
            value={data.infrared}
            onChange={(value) => {
              setData({
                ...data,
                infrared: value,
              });
              handleUpdateAmenities({
                ...data,
                infrared: value,
              });
            }}
          />

          <InputOnOff
            title="Energia Geotermica"
            value={data.geothermal}
            onChange={(value) => {
              setData({
                ...data,
                geothermal: value,
              });
              handleUpdateAmenities({
                ...data,
                geothermal: value,
              });
            }}
          />

          <InputOnOff
            title="Panelli Solari Termici"
            value={data.thermalsolar}
            onChange={(value) => {
              setData({
                ...data,
                thermalsolar: value,
              });
              handleUpdateAmenities({
                ...data,
                thermosolar: value,
              });
            }}
          />

          <InputOnOff
            title="Scaldabagno Solare"
            value={data.solarheater}
            onChange={(value) => {
              setData({
                ...data,
                solarheater: value,
              });
              handleUpdateAmenities({
                ...data,
                solarheater: value,
              });
            }}
          />

          <InputOnOff
            title="Riciclo Vetro"
            value={data.recicleglass}
            onChange={(value) => {
              setData({
                ...data,
                recicleglass: value,
              });
              handleUpdateAmenities({
                ...data,
                recicleglass: value,
              });
            }}
          />

          <InputOnOff
            title="Riciclo Carta"
            value={data.reciclepaper}
            onChange={(value) => {
              setData({
                ...data,
                reciclepaper: value,
              });
              handleUpdateAmenities({
                ...data,
                reciclepaper: value,
              });
            }}
          />

          <InputOnOff
            title="Riciclo Plastica"
            value={data.recicleplastic}
            onChange={(value) => {
              setData({
                ...data,

                recicleplastic: value,
              });
              handleUpdateAmenities({
                ...data,
                recicleplastic: value,
              });
            }}
          />

          <InputOnOff
            title="Riciclo Organico"
            value={data.recicleorganic}
            onChange={(value) => {
              setData({
                ...data,
                recicleorganic: value,
              });
              handleUpdateAmenities({
                ...data,
                recicleorganic: value,
              });
            }}
          />

          <InputOnOff
            title="Luci LED"
            value={data.ledlights}
            onChange={(value) => {
              setData({
                ...data,
                ledlights: value,
              });
              handleUpdateAmenities({
                ...data,
                ledlights: value,
              });
            }}
          />

          <InputOnOff
            title="Energia rinnovabile al 100%"
            value={data.renewableenergy}
            onChange={(value) => {
              setData({
                ...data,
                renewableenergy: value,
              });
              handleUpdateAmenities({
                ...data,
                renewableenergy: value,
              });
            }}
          />

          <InputOnOff
            title="Fotovoltaico"
            value={data.fotovoltaic}
            onChange={(value) => {
              setData({
                ...data,
                fotovoltaic: value,
              });
              handleUpdateAmenities({
                ...data,
                fotovoltaic: value,
              });
            }}
          />

          <InputOnOff
            title="Turbine Eoliche"
            value={data.windturbines}
            onChange={(value) => {
              setData({
                ...data,
                windturbines: value,
              });
              handleUpdateAmenities({
                ...data,
                windturbines: value,
              });
            }}
          />

          <InputOnOff
            title="Metano Digestore"
            value={data.methanedigestor}
            onChange={(value) => {
              setData({
                ...data,
                methanedigestor: value,
              });
              handleUpdateAmenities({
                ...data,
                methanedigestor: value,
              });
            }}
          />

          <InputOnOff
            title="Controlli Smart"
            description="Controlli Smart per l’energia elettrica e il riscaldamento"
            value={data.smartcontrols}
            onChange={(value) => {
              setData({
                ...data,
                smartcontrols: value,
              });
              handleUpdateAmenities({
                ...data,
                smartcontrols: value,
              });
            }}
          />

          <InputOnOff
            title="Prodotti Lavaggio Eco"
            value={data.ecoproducts}
            onChange={(value) => {
              setData({
                ...data,
                ecoproducts: value,
              });
              handleUpdateAmenities({
                ...data,
                ecoproducts: value,
              });
            }}
          />

          <InputOnOff
            title="Doccia a Risparmio Acqua"
            value={data.savewatershower}
            onChange={(value) => {
              setData({
                ...data,
                savewatershower: value,
              });
              handleUpdateAmenities({
                ...data,
                savewatershower: value,
              });
            }}
          />

          <InputOnOff
            title="WC a Risparmio Acqua"
            value={data.savewaterwc}
            onChange={(value) => {
              setData({
                ...data,
                savewaterwc: value,
              });
              handleUpdateAmenities({
                ...data,
                savewaterwc: value,
              });
            }}
          />

          <InputOnOff
            title="Rubinetti a Risparmio Acqua"
            value={data.savewatertap}
            onChange={(value) => {
              setData({
                ...data,
                savewatertap: value,
              });
              handleUpdateAmenities({
                ...data,
                savewatertap: value,
              });
            }}
          />

          <InputOnOff
            title="Raccolta Acqua Piovana"
            value={data.collectwater}
            onChange={(value) => {
              setData({
                ...data,
                collectwater: value,
              });
              handleUpdateAmenities({
                ...data,
                collectwater: value,
              });
            }}
          />

          <InputOnOff
            title="Sistema Acque Grigie"
            value={data.greywater}
            onChange={(value) => {
              setData({
                ...data,
                greywater: value,
              });
              handleUpdateAmenities({
                ...data,
                greywater: value,
              });
            }}
          />

          <InputOnOff
            title="Marmellata Homemade"
            value={data.jam}
            onChange={(value) => {
              setData({
                ...data,
                jam: value,
              });
              handleUpdateAmenities({
                ...data,
                jam: value,
              });
            }}
          />
          <InputOnOff
            title="Frutti Homemade"
            value={data.fruit}
            onChange={(value) => {
              setData({
                ...data,
                fruit: value,
              });
              handleUpdateAmenities({
                ...data,
                fruit: value,
              });
            }}
          />
          <InputOnOff
            title="Verdure Homemade"
            value={data.verdure}
            onChange={(value) => {
              setData({
                ...data,
                verdure: value,
              });
              handleUpdateAmenities({
                ...data,
                verdure: value,
              });
            }}
          />
          <InputOnOff
            title="Erbe Homemade"
            value={data.herbs}
            onChange={(value) => {
              setData({
                ...data,
                herbs: value,
              });
              handleUpdateAmenities({
                ...data,
                herbs: value,
              });
            }}
          />
          <InputOnOff
            title="Olio Homemade"
            value={data.oil}
            onChange={(value) => {
              setData({
                ...data,
                oil: value,
              });
              handleUpdateAmenities({
                ...data,
                oil: value,
              });
            }}
          />
          <InputOnOff
            title="Miele Homemade"
            value={data.honey}
            onChange={(value) => {
              setData({
                ...data,
                honey: value,
              });
              handleUpdateAmenities({
                ...data,
                honey: value,
              });
            }}
          />

          <InputOnOff
            title="Uova Homemade"
            value={data.eggs}
            onChange={(value) => {
              setData({
                ...data,
                eggs: value,
              });
              handleUpdateAmenities({
                ...data,
                eggs: value,
              });
            }}
          />

          <InputOnOff
            title="Formaggio Homemade"
            value={data.cheese}
            onChange={(value) => {
              setData({
                ...data,
                cheese: value,
              });
              handleUpdateAmenities({
                ...data,
                cheese: value,
              });
            }}
          />

          <InputOnOff
            title="Pane Homemade"
            value={data.bread}
            onChange={(value) => {
              setData({
                ...data,
                bread: value,
              });
              handleUpdateAmenities({
                ...data,
                bread: value,
              });
            }}
          />

          <InputOnOff
            title="Noci Homemade"
            value={data.nuts}
            onChange={(value) => {
              setData({
                ...data,
                nuts: value,
              });
              handleUpdateAmenities({
                ...data,
                nuts: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
