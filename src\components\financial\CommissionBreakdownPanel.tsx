import React, { useState } from "react";
import Card from "../cards/Card";
import { CommissionBreakdown, PaymentDetails } from "@/services/financialApi";
import { financialUtils } from "@/services/financialApi";

interface CommissionBreakdownProps {
  breakdown?: CommissionBreakdown;
  paymentDetails?: PaymentDetails;
  loading?: boolean;
  title?: string;
  className?: string;
}

const CommissionBreakdownPanel: React.FC<CommissionBreakdownProps> = ({
  breakdown,
  paymentDetails,
  loading = false,
  title = "Panoramica Commissioni",
  className = "",
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getBreakdownItems = () => {
    if (paymentDetails) {
      // Use payment details from a specific reservation
      return [
        {
          label: "Prezzo Cliente",
          amount: paymentDetails.client_price,
          description: "Importo totale pagato dall’ospite",
          type: "income" as const,
        },
        {
          label: "Commissione OTA (15%)",
          amount: paymentDetails.ota_commission,
          description:
            "Commissione addebitata dalla piattaforma di prenotazione",
          type: "deduction" as const,
        },
        {
          label: "Commissione Heibooky (8%)",
          amount: paymentDetails.heibooky_commission,
          description: "Costo di gestione della piattaforma",
          type: "deduction" as const,
        },
        {
          label: "Commissione Pagamento (3,5%)",
          amount: paymentDetails.payment_fee_3_5,
          description: "Costo di elaborazione del pagamento",
          type: "deduction" as const,
        },
        {
          label: "IVA (22%)",
          amount: paymentDetails.vat_22,
          description: "Imposta sul valore aggiunto sulle commissioni",
          type: "deduction" as const,
        },
        {
          label: "Ritenuta d’acconto (21%)",
          amount: paymentDetails.substitute_tax_21,
          description: "Ritenuta fiscale sui compensi",
          type: "deduction" as const,
        },
        {
          label: "Quota di Attivazione",
          amount: paymentDetails.activation_fee,
          description: "Costo una tantum di attivazione (recupero graduale)",
          type: "deduction" as const,
        },
        {
          label: "Totale al Proprietario",
          amount: paymentDetails.total_owner,
          description: "Importo finale liquidato al proprietario",
          type: "final" as const,
        },
      ];
    } else if (breakdown) {
      // Use summary breakdown data
      return [
        {
          label: "Commissioni OTA",
          amount: parseFloat(breakdown.total_ota_commission),
          description: "Commissioni totali dalle piattaforme di prenotazione",
          type: "deduction" as const,
        },
        {
          label: "Commissioni Heibooky",
          amount: parseFloat(breakdown.total_heibooky_commission),
          description: "Costi di gestione della piattaforma",
          type: "deduction" as const,
        },
        {
          label: "Costi di Pagamento",
          amount: parseFloat(breakdown.total_payment_fees),
          description: "Oneri per l’elaborazione dei pagamenti",
          type: "deduction" as const,
        },
        {
          label: "IVA",
          amount: parseFloat(breakdown.total_vat),
          description: "Imposta sul valore aggiunto",
          type: "deduction" as const,
        },
        {
          label: "Imposte",
          amount: parseFloat(breakdown.total_taxes),
          description: "Imposte sui redditi e altre imposte",
          type: "deduction" as const,
        },
        {
          label: "Quote di Attivazione",
          amount: parseFloat(breakdown.total_activation_fees),
          description: "Costi di configurazione e attivazione",
          type: "deduction" as const,
        },
        {
          label: "Costi di Pulizia",
          amount: parseFloat(breakdown.total_cleaning_costs),
          description: "Spese di pulizia dell’immobile",
          type: "deduction" as const,
        },
      ];
    }
    return [];
  };

  const items = getBreakdownItems();
  const totalDeductions = items
    .filter((item) => item.type === "deduction")
    .reduce((sum, item) => sum + item.amount, 0);

  if (loading) {
    return (
      <Card className={className}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="h-6 bg-gray-200 rounded w-40 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
          </div>
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div
                key={i}
                className="flex justify-between items-center animate-pulse"
              >
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (items.length === 0) {
    return (
      <Card className={className}>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">{title}</h3>
          <div className="text-center py-8">
            <div className="text-gray-400 text-4xl mb-4">💰</div>
            <p className="text-gray-500">
              Nessun dato sulle commissioni disponibile
            </p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-sm text-[var(--accent)] hover:underline"
          >
            {isExpanded ? "Mostra meno" : "Mostra dettagli"}
          </button>
        </div>

        {/* Summary View */}
        {!isExpanded && (
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b">
              <span className="text-gray-600">Detrazioni Totali</span>
              <span className="font-mono font-semibold text-red-600">
                -{financialUtils.formatCurrency(totalDeductions)}
              </span>
            </div>

            {paymentDetails && (
              <div className="flex justify-between items-center py-2 bg-green-50 px-3 rounded">
                <span className="font-medium text-green-800">
                  Saldo Netto Proprietario
                </span>
                <span className="font-mono font-bold text-green-700">
                  {financialUtils.formatCurrency(paymentDetails.total_owner)}
                </span>
              </div>
            )}

            <div className="pt-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Detrazione maggiore:</span>
                  <div className="font-medium">
                    {items
                      .filter((item) => item.type === "deduction")
                      .sort((a, b) => b.amount - a.amount)[0]?.label || "N/D"}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Voci totali:</span>
                  <div className="font-medium">
                    {items.filter((item) => item.type === "deduction").length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Detailed View */}
        {isExpanded && (
          <div className="space-y-3">
            {items.map((item, index) => (
              <div
                key={index}
                className={`flex justify-between items-center py-3 px-3 rounded ${
                  item.type === "income"
                    ? "bg-blue-50 border border-blue-100"
                    : item.type === "final"
                      ? "bg-green-50 border border-green-200"
                      : "bg-gray-50"
                }`}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span
                      className={`font-medium ${
                        item.type === "income"
                          ? "text-blue-700"
                          : item.type === "final"
                            ? "text-green-700"
                            : "text-gray-700"
                      }`}
                    >
                      {item.label}
                    </span>

                    {paymentDetails && item.type === "deduction" && (
                      <span className="text-xs text-gray-500">
                        (
                        {financialUtils.formatPercentage(
                          financialUtils.calculatePercentage(
                            item.amount,
                            paymentDetails.client_price,
                          ),
                        )}
                        )
                      </span>
                    )}
                  </div>

                  <div className="text-xs text-gray-500 mt-1 truncate">
                    {item.description}
                  </div>
                </div>

                <div className="text-right shrink-0">
                  <div
                    className={`font-mono font-semibold ${
                      item.type === "income"
                        ? "text-blue-600"
                        : item.type === "final"
                          ? "text-green-600"
                          : "text-red-600"
                    }`}
                  >
                    {item.type === "deduction" ? "-" : ""}
                    {financialUtils.formatCurrency(item.amount)}
                  </div>
                </div>
              </div>
            ))}

            {/* Activation Fee Recovery Info */}
            {paymentDetails?.activation_fee_tracking && (
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                <h4 className="font-medium text-yellow-800 mb-2">
                  Recupero Quota di Attivazione
                </h4>
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-yellow-700">
                      Applicata su questa prenotazione:
                    </span>
                    <span className="font-mono">
                      {financialUtils.formatCurrency(
                        paymentDetails.activation_fee_tracking
                          .activation_fee_applied,
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-yellow-700">
                      Residuo da recuperare:
                    </span>
                    <span className="font-mono">
                      {financialUtils.formatCurrency(
                        paymentDetails.activation_fee_tracking
                          .activation_fee_remaining_after,
                      )}
                    </span>
                  </div>
                  <div className="w-full bg-yellow-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-yellow-600 h-2 rounded-full"
                      style={{
                        width: `${Math.max(0, 100 - (paymentDetails.activation_fee_tracking.activation_fee_remaining_after / paymentDetails.activation_fee_tracking.activation_fee_remaining_before) * 100)}%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export default CommissionBreakdownPanel;
