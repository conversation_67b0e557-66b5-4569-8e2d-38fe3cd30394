"use client";
import React, { useState } from "react";
import { motion } from "framer-motion";
import Card from "../cards/Card";
import PaymentStatusBadge from "./PaymentStatusBadge";
import { Reservation } from "@/services/financialApi";
import { financialUtils } from "@/services/financialApi";

interface ReservationTableProps {
  reservations: Reservation[];
  loading?: boolean;
  title?: string;
  showPagination?: boolean;
  itemsPerPage?: number;
  onReservationClick?: (reservation: Reservation) => void;
  className?: string;
}

const ReservationTable: React.FC<ReservationTableProps> = ({
  reservations,
  loading = false,
  title = "Prenotazioni Recenti",
  showPagination = true,
  itemsPerPage = 10,
  onReservationClick,
  className = "",
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] =
    useState<keyof Reservation>("checkout_date");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Sorting logic
  const sortedReservations = [...reservations].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];

    if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
    if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
    return 0;
  });

  // Pagination logic
  const totalPages = Math.ceil(sortedReservations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedReservations = showPagination
    ? sortedReservations.slice(startIndex, startIndex + itemsPerPage)
    : sortedReservations;

  const handleSort = (field: keyof Reservation) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const getSortIcon = (field: keyof Reservation) => {
    if (field !== sortField) return "↕️";
    return sortDirection === "asc" ? "↑" : "↓";
  };

  if (loading) {
    return (
      <Card className={className}>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">{title}</h3>
          <div className="animate-pulse">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex space-x-4 py-3 border-b">
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (reservations.length === 0) {
    return (
      <Card className={className}>
        <div className="p-6">
          <h3 className="text-lg font-semibold mb-4">{title}</h3>
          <div className="text-center py-8">
            <div className="text-gray-400 text-4xl mb-4">📊</div>
            <h4 className="text-lg font-medium text-gray-600 mb-2">
              Nessuna prenotazione trovata
            </h4>
            <p className="text-gray-500">
              Non ci sono prenotazioni da mostrare al momento.
            </p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          <span className="text-sm text-gray-500">
            {reservations.length} reservation
            {reservations.length !== 1 ? "s" : ""}
          </span>
        </div>

        {/* Tabella Desktop */}
        <div className="hidden md:block overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th
                  className="text-left py-3 px-4 font-medium text-gray-600 cursor-pointer hover:text-gray-900"
                  onClick={() => handleSort("id")}
                >
                  ID Prenotazione {getSortIcon("id")}
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-600 cursor-pointer hover:text-gray-900"
                  onClick={() => handleSort("guest_name")}
                >
                  Ospite {getSortIcon("guest_name")}
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-600 cursor-pointer hover:text-gray-900"
                  onClick={() => handleSort("checkout_date")}
                >
                  Check-out {getSortIcon("checkout_date")}
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-600 cursor-pointer hover:text-gray-900"
                  onClick={() => handleSort("total_price")}
                >
                  Prezzo Totale {getSortIcon("total_price")}
                </th>
                <th
                  className="text-left py-3 px-4 font-medium text-gray-600 cursor-pointer hover:text-gray-900"
                  onClick={() => handleSort("net_amount")}
                >
                  Importo Netto {getSortIcon("net_amount")}
                </th>
                <th className="text-left py-3 px-4 font-medium text-gray-600">
                  Stato
                </th>
              </tr>
            </thead>
            <tbody>
              {paginatedReservations.map((reservation) => (
                <tr
                  key={reservation.id}
                  className="border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                  onClick={() => onReservationClick?.(reservation)}
                >
                  <td className="py-3 px-4 font-mono text-sm">
                    {reservation.id.substring(0, 8)}...
                  </td>
                  <td className="py-3 px-4">
                    <div className="font-medium">{reservation.guest_name}</div>
                    <div className="text-sm text-gray-500">
                      {financialUtils.formatDate(reservation.checkin_date)} -{" "}
                      {financialUtils.formatDate(reservation.checkout_date)}
                    </div>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-600">
                    {financialUtils.formatDate(reservation.checkout_date)}
                  </td>
                  <td className="py-3 px-4 font-mono">
                    {financialUtils.formatCurrency(reservation.total_price)}
                  </td>
                  <td className="py-3 px-4 font-mono font-semibold text-green-600">
                    {financialUtils.formatCurrency(reservation.net_amount)}
                  </td>
                  <td className="py-3 px-4">
                    <PaymentStatusBadge
                      status={reservation.payment_status as any}
                      displayText={reservation.payment_status_display}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <motion.div
          className="md:hidden space-y-4"
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-40px" }}
          variants={{
            hidden: { opacity: 0 },
            show: { opacity: 1, transition: { staggerChildren: 0.07 } },
          }}
        >
          {paginatedReservations.map((reservation) => (
            <motion.div
              key={reservation.id}
              variants={{
                hidden: { opacity: 0, y: 18 },
                show: {
                  opacity: 1,
                  y: 0,
                  transition: { type: "spring", stiffness: 160, damping: 20 },
                },
              }}
              className="bg-gray-50 rounded-lg p-4 cursor-pointer hover:bg-gray-100"
              onClick={() => onReservationClick?.(reservation)}
            >
              <div className="flex justify-between items-start mb-2">
                <div>
                  <div className="font-medium">{reservation.guest_name}</div>
                  <div className="text-sm text-gray-500 font-mono">
                    {reservation.id.substring(0, 12)}...
                  </div>
                </div>
                <PaymentStatusBadge
                  status={reservation.payment_status as any}
                  displayText={reservation.payment_status_display}
                  size="sm"
                />
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-500">Check-out:</span>
                  <div className="font-medium">
                    {financialUtils.formatDate(reservation.checkout_date)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Totale:</span>
                  <div className="font-mono">
                    {financialUtils.formatCurrency(reservation.total_price)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Importo netto:</span>
                  <div className="font-mono font-semibold text-green-600">
                    {financialUtils.formatCurrency(reservation.net_amount)}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Prossimo pagamento:</span>
                  <div className="text-sm">
                    {reservation.next_payment_info?.days_until_payment} giorni
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Paginazione */}
        {showPagination && totalPages > 1 && (
          <div className="flex justify-between items-center mt-6 pt-4 border-t">
            <div className="text-sm text-gray-500">
              Mostrati {startIndex + 1} -{" "}
              {Math.min(startIndex + itemsPerPage, reservations.length)} di{" "}
              {reservations.length} risultati
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Precedente
              </button>

              {[...Array(totalPages)].map((_, i) => {
                const page = i + 1;
                if (
                  page === 1 ||
                  page === totalPages ||
                  (page >= currentPage - 1 && page <= currentPage + 1)
                ) {
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-1 text-sm border rounded ${
                        currentPage === page
                          ? "bg-[var(--accent)] text-white border-[var(--accent)]"
                          : "hover:bg-gray-50"
                      }`}
                    >
                      {page}
                    </button>
                  );
                } else if (
                  page === currentPage - 2 ||
                  page === currentPage + 2
                ) {
                  return (
                    <span key={page} className="px-1">
                      ...
                    </span>
                  );
                }
                return null;
              })}

              <button
                onClick={() =>
                  setCurrentPage((p) => Math.min(totalPages, p + 1))
                }
                disabled={currentPage === totalPages}
                className="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Successivo
              </button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default ReservationTable;
