"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);

  const [data, setData] = React.useState({
    elevator: false,
    noStepsInside: false,
    minWidth: false,
    handlesBathroom: false,
    raisedSeatWc: false,
    bathtubAccess: false,
    sinkAccess: false,
    emergencyRope: false,
    circleBathroom: false,
    rollIn: false,
    showerHandles: false,
    raisedShower: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: "Elevator",
        category: "Accessibility",
        is_available: data.elevator ?? false,
      },
      {
        name: "No Steps Inside",
        category: "Accessibility",
        is_available: data.noStepsInside ?? false,
      },
      {
        name: "Min Width",
        category: "Accessibility",
        is_available: data.minWidth ?? false,
      },
      {
        name: "Handles Bathroom",
        category: "Accessibility",
        is_available: data.handlesBathroom ?? false,
      },
      {
        name: "Raised Seat Wc",
        category: "Accessibility",
        is_available: data.raisedSeatWc ?? false,
      },
      {
        name: "Bathtub Access",
        category: "Accessibility",
        is_available: data.bathtubAccess ?? false,
      },
      {
        name: "Sink Access",
        category: "Accessibility",
        is_available: data.sinkAccess ?? false,
      },
      {
        name: "Emergency Rope",
        category: "Accessibility",
        is_available: data.emergencyRope ?? false,
      },
      {
        name: "Circle Bathroom",
        category: "Accessibility",
        is_available: data.circleBathroom ?? false,
      },
      {
        name: "Roll In",
        category: "Accessibility",
        is_available: data.rollIn ?? false,
      },
      {
        name: "Shower Handles",
        category: "Accessibility",
        is_available: data.showerHandles ?? false,
      },
      {
        name: "Raised Shower",
        category: "Accessibility",
        is_available: data.raisedShower ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };
  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;
    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Accessibilità"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Acensore"
            value={data.elevator}
            onChange={(value) => {
              setData({
                ...data,
                elevator: value,
              });
              handleUpdateAmenities({
                ...data,
                elevator: value,
              });
            }}
          />

          <InputOnOff
            title="Nessun gradino all'interno della proprietà"
            value={data.noStepsInside}
            onChange={(value) => {
              setData({
                ...data,
                noStepsInside: value,
              });
              handleUpdateAmenities({
                ...data,
                noStepsInside: value,
              });
            }}
          />

          <InputOnOff
            title="Larghezza minima porte 80cm"
            value={data.minWidth}
            onChange={(value) => {
              setData({
                ...data,
                minWidth: value,
              });
              handleUpdateAmenities({
                ...data,
                minWidth: value,
              });
            }}
          />

          <InputOnOff
            title="Maniglioni nei bagni"
            value={data.handlesBathroom}
            onChange={(value) => {
              setData({
                ...data,
                handlesBathroom: value,
              });
              handleUpdateAmenities({
                ...data,
                handlesBathroom: value,
              });
            }}
          />

          <InputOnOff
            title="Water sedile rialzato"
            value={data.raisedSeatWc}
            onChange={(value) => {
              setData({
                ...data,
                raisedSeatWc: value,
              });
              handleUpdateAmenities({
                ...data,
                raisedSeatWc: value,
              });
            }}
          />

          <InputOnOff
            title="Vasca con accesso facilitato"
            value={data.bathtubAccess}
            onChange={(value) => {
              setData({
                ...data,
                bathtubAccess: value,
              });
              handleUpdateAmenities({
                ...data,
                bathubAccess: value,
              });
            }}
          />

          <InputOnOff
            title="Lavandino con accesso facilitato"
            value={data.sinkAccess}
            onChange={(value) => {
              setData({
                ...data,
                sinkAccess: value,
              });
              handleUpdateAmenities({
                ...data,
                sinkAccess: value,
              });
            }}
          />

          <InputOnOff
            title="Corda di emergenza in bagno"
            value={data.emergencyRope}
            onChange={(value) => {
              setData({
                ...data,
                emergencyRope: value,
              });
              handleUpdateAmenities({
                ...data,
                emergencyRope: value,
              });
            }}
          />

          <InputOnOff
            title="Bagno con circolo di 150cm di diametro"
            value={data.circleBathroom}
            onChange={(value) => {
              setData({
                ...data,
                circleBathroom: value,
              });
              handleUpdateAmenities({
                ...data,
                circleBathroom: value,
              });
            }}
          />

          <InputOnOff
            title="Doccia roll-in"
            value={data.rollIn}
            onChange={(value) => {
              setData({
                ...data,
                rollIn: value,
              });
              handleUpdateAmenities({
                ...data,
                rollIn: value,
              });
            }}
          />

          <InputOnOff
            title="Doccia con maniglioni"
            value={data.showerHandles}
            onChange={(value) => {
              setData({
                ...data,
                showerHandles: value,
              });
              handleUpdateAmenities({
                ...data,
                showerHandles: value,
              });
            }}
          />

          <InputOnOff
            title="Doccia con sedile rialzato"
            value={data.raisedShower}
            onChange={(value) => {
              setData({
                ...data,
                raisedShower: value,
              });
              handleUpdateAmenities({
                ...data,
                raisedShower: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
