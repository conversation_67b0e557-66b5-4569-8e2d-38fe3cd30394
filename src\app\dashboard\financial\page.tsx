"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import FinancialCard from "@/components/financial/FinancialCard";
import PaymentStatusBadge from "@/components/financial/PaymentStatusBadge";
import ReservationTable from "@/components/financial/ReservationTable";
import CommissionBreakdownPanel from "@/components/financial/CommissionBreakdownPanel";
import Button from "@/components/buttons/Button";
import Card from "@/components/cards/Card";
import toast from "react-hot-toast";

import { useFinancialData } from "@/hooks/useFinancialData";
import { Reservation, financialUtils } from "@/services/financialApi";
import { calculateTrend } from "@/utils/financialTrends";

const FinancialDashboard: React.FC = () => {
  const router = useRouter();
  const [selectedPropertyIds] = useState<string[]>([]);

  const {
    summary,
    paymentStatus,
    paymentCycle,
    recentReservations,
    historicalEarnings,
    loading,
    errors,
    lastRefresh,
    refreshing,
    nextPaymentCountdown,
    refreshAll,
  } = useFinancialData({
    propertyIds: selectedPropertyIds,
    autoRefresh: true,
    refreshInterval: 15 * 60 * 1000, // 15 minutes
  });

  const handleReservationClick = (reservation: Reservation) => {
    // TODO: Navigate to reservation details or show modal
    console.log("Reservation clicked:", reservation);
  };

  const handleRefresh = async () => {
    try {
      await refreshAll();
      toast.success("Dati finanziari aggiornati con successo");
    } catch (error) {
      toast.error("Impossibile aggiornare alcuni dati finanziari");
    }
  };

  const getTrendData = () => {
    // Use historical earnings data if available
    if (historicalEarnings?.trend) {
      return {
        value: Math.abs(historicalEarnings.trend.percentage_change),
        isPositive: historicalEarnings.trend.is_positive,
        period: historicalEarnings.trend.comparison_period,
      };
    }

    // Fallback: Calculate trend based on pending balance vs total earnings ratio
    if (!summary?.pending_balance || !summary?.total_earnings) {
      return null; // No trend data if no financial data available
    }

    const pendingBalance = parseFloat(summary.pending_balance || "0");
    const totalEarnings = parseFloat(summary.total_earnings || "0");

    // If total earnings is 0, can't calculate meaningful trend
    if (totalEarnings === 0) {
      return null;
    }

    // Calculate pending balance as percentage of total earnings
    const pendingRatio = (pendingBalance / totalEarnings) * 100;

    // Define what constitutes good/bad trends
    // Higher pending balance ratio (more recent earnings) = positive trend
    // Lower pending balance ratio (less recent activity) = negative trend
    const baselineRatio = 5; // 5% is our baseline for comparison

    const trendValue = Math.abs(pendingRatio - baselineRatio);
    const isPositive = pendingRatio >= baselineRatio;

    // If trend value is very small, don't show trend
    if (trendValue < 0.1) {
      return null;
    }

    return {
      value: Math.round(trendValue * 10) / 10, // Round to 1 decimal
      isPositive,
      period: "ultimi 15 giorni",
    };
  };

  const getTotalEarningsTrend = () => {
    // Use historical earnings data if available
    if (historicalEarnings?.current_period && historicalEarnings?.previous_period) {
      return calculateTrend(
        historicalEarnings.current_period.earnings,
        historicalEarnings.previous_period.earnings,
        "periodo precedente",
      );
    }
    return null;
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Finanze"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push("/dashboard")}
      />

      <div className="mt-3 px-3 pb-24 space-y-6 max-w-md mx-auto w-full overflow-x-hidden">
        {/* Testata */}
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <h1 className="text-xl font-bold tracking-tight text-gray-900">
              Panoramica Finanziaria
            </h1>
            {lastRefresh && (
              <p className="text-[11px] mt-1 text-gray-500">
                Aggiornato alle {lastRefresh.toLocaleTimeString()}
              </p>
            )}
          </div>
          <div className="shrink-0">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              color="white"
              backgroundColor="var(--accent)"
              text={refreshing ? "🔄" : "↻"}
            />
          </div>
        </div>

        {/* Metriche Principali */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 gap-3"
          initial="hidden"
          animate="show"
          variants={{
            hidden: { opacity: 0 },
            show: { opacity: 1, transition: { staggerChildren: 0.06 } },
          }}
        >
          {[
            <FinancialCard
              key="pending"
              title="Saldo in Attesa"
              // show value only if present (null/undefined => empty); 0 or "0" will display as 0
              value={summary?.pending_balance ?? null}
              currency="EUR"
              subtitle="Ultimi 15 giorni"
              icon={<span className="text-2xl">💰</span>}
              loading={loading.summary}
              error={!!errors.summary}
              errorMessage={errors.summary || undefined}
              trend={getTrendData() || undefined}
            />,
            <FinancialCard
              key="total"
              title="Guadagni Totali"
              value={summary?.total_earnings ?? null}
              currency="EUR"
              subtitle="Storico"
              icon={<span className="text-2xl">📈</span>}
              loading={loading.summary}
              error={!!errors.summary}
              errorMessage={errors.summary || undefined}
              trend={getTotalEarningsTrend() || undefined}
            />,
            <FinancialCard
              key="next"
              title="Prossimo Pagamento"
              value={nextPaymentCountdown ?? null}
              subtitle={paymentCycle?.current_cycle.description ?? undefined}
              icon={<span className="text-2xl">📅</span>}
              loading={loading.paymentCycle}
              error={!!errors.paymentCycle}
              errorMessage={errors.paymentCycle || undefined}
            />,
            <FinancialCard
              key="inprogress"
              title="Pagamento in Corso"
              // If count is explicitly 0, show 0; otherwise show null
              value={paymentStatus?.payment_in_progress?.count ?? null}
              subtitle={
                paymentStatus?.payment_in_progress?.total_amount != null
                  ? `${financialUtils.formatCurrency(paymentStatus.payment_in_progress.total_amount)} tot.`
                  : undefined
              }
              icon={<span className="text-2xl">⏳</span>}
              loading={loading.paymentStatus}
              error={!!errors.paymentStatus}
              errorMessage={errors.paymentStatus || undefined}
              noCurrency={true}
            />,
          ].map((card) => (
            <motion.div
              key={(card as any).key}
              variants={{
                hidden: { opacity: 0, y: 14 },
                show: {
                  opacity: 1,
                  y: 0,
                  transition: { type: "spring", stiffness: 200, damping: 22 },
                },
              }}
            >
              {card}
            </motion.div>
          ))}
        </motion.div>

        {/* Stato Pagamenti */}
        <motion.div
          className="grid grid-cols-1 gap-5"
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-40px" }}
          variants={{
            hidden: { opacity: 0 },
            show: { opacity: 1, transition: { staggerChildren: 0.12 } },
          }}
        >
          {/* Payment in Progress */}
          <motion.div
            variants={{
              hidden: { opacity: 0, y: 24 },
              show: {
                opacity: 1,
                y: 0,
                transition: { type: "spring", stiffness: 140, damping: 22 },
              },
            }}
          >
            <Card>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <PaymentStatusBadge
                      status="payment_in_progress"
                      size="sm"
                    />
                    Pagamenti in Corso
                  </h3>
                  <span className="text-2xl font-bold text-yellow-600">
                    {paymentStatus?.payment_in_progress?.count ?? ""}
                  </span>
                </div>

                <div className="mb-4">
                  <div className="text-sm text-gray-600">Importo Totale</div>
                  <div className="text-xl font-mono font-semibold text-gray-900">
                    {paymentStatus?.payment_in_progress?.total_amount != null
                      ? financialUtils.formatCurrency(
                          paymentStatus.payment_in_progress.total_amount,
                        )
                      : ""
                    }
                  </div>
                </div>

                {paymentCycle && (
                  <div className="text-sm text-gray-500">
                    Data Pagamento:{" "}
                    {financialUtils.formatDate(
                      paymentCycle.current_cycle.payment_date,
                    )}
                  </div>
                )}
              </div>
            </Card>
          </motion.div>

          {/* Future Payments */}
          <motion.div
            variants={{
              hidden: { opacity: 0, y: 24 },
              show: {
                opacity: 1,
                y: 0,
                transition: {
                  type: "spring",
                  stiffness: 140,
                  damping: 22,
                  delay: 0.05,
                },
              },
            }}
          >
            <Card>
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <PaymentStatusBadge status="future_payment" size="sm" />
                    Pagamenti Futuri
                  </h3>
                  <span className="text-2xl font-bold text-blue-600">
                    {paymentStatus?.future_payment.count || 0}
                  </span>
                </div>

                <div className="mb-4">
                  <div className="text-sm text-gray-600">Importo Totale</div>
                  <div className="text-xl font-mono font-semibold text-gray-900">
                    {paymentStatus?.future_payment?.total_amount != null
                      ? financialUtils.formatCurrency(
                          paymentStatus.future_payment.total_amount,
                        )
                      : ""
                    }
                  </div>
                </div>

                {paymentCycle && (
                  <div className="text-sm text-gray-500">
                    Prossimo Ciclo:{" "}
                    {financialUtils.formatDate(
                      paymentCycle.next_cycle.payment_date,
                    )}
                  </div>
                )}
              </div>
            </Card>
          </motion.div>
        </motion.div>
        {/* Saldi Pregressi & Deficit */}
        {(summary?.commission_breakdown?.total_negative_balance_applied != null ||
          summary?.commission_breakdown?.total_negative_balance_created != null) && (
          <motion.div
            initial={{ opacity: 0, y: 24 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-40px" }}
            transition={{ type: "spring", stiffness: 140, damping: 20 }}
          >
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">
                  Saldi Pregressi & Deficit
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="rounded-md bg-green-50 p-4 border border-green-100">
                    <div className="text-sm text-green-700">
                      Recupero complessivo
                    </div>
                    <div className="mt-1 text-xl font-mono font-bold text-green-700">
                      {summary?.commission_breakdown?.total_negative_balance_applied != null
                        ? financialUtils.formatCurrency(summary.commission_breakdown.total_negative_balance_applied)
                        : ""
                      }
                    </div>
                    <div className="text-xs text-green-700/70 mt-1">
                      Importi passati recuperati dai pagamenti
                    </div>
                  </div>
                  <div className="rounded-md bg-orange-50 p-4 border border-orange-100">
                    <div className="text-sm text-orange-700">
                      Deficit creati
                    </div>
                    <div className="mt-1 text-xl font-mono font-bold text-orange-700">
                      {summary?.commission_breakdown?.total_negative_balance_created != null
                        ? financialUtils.formatCurrency(summary.commission_breakdown.total_negative_balance_created)
                        : ""
                      }
                    </div>
                    <div className="text-xs text-orange-700/70 mt-1">
                      Saldi negativi che verranno recuperati
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Commissioni & Costi */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-60px" }}
          transition={{ type: "spring", stiffness: 120, damping: 18 }}
        >
          <CommissionBreakdownPanel
            breakdown={summary?.commission_breakdown}
            loading={loading.summary}
            title="Panoramica Commissioni & Costi"
          />
        </motion.div>

        {/* Prenotazioni Recenti */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, margin: "-40px" }}
          transition={{ duration: 0.4 }}
        >
          <ReservationTable
            reservations={recentReservations}
            loading={loading.paymentStatus}
            title="Prenotazioni Recenti"
            onReservationClick={handleReservationClick}
            itemsPerPage={10}
          />
        </motion.div>

        {/* Informazioni Ciclo Pagamenti */}
        {paymentCycle && (
          <motion.div
            initial={{ opacity: 0, y: 32 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ type: "spring", stiffness: 130, damping: 20 }}
          >
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">
                  Calendario Pagamenti
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-4 bg-yellow-50">
                    <h4 className="font-medium text-yellow-800 mb-2">
                      Ciclo Attuale
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-yellow-700">Periodo:</span>
                        <span>{paymentCycle.current_cycle.description}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-yellow-700">Data Pagamento:</span>
                        <span className="font-medium">
                          {financialUtils.formatDate(
                            paymentCycle.current_cycle.payment_date,
                          )}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="border rounded-lg p-4 bg-blue-50">
                    <h4 className="font-medium text-blue-800 mb-2">
                      Prossimo Ciclo
                    </h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span className="text-blue-700">Periodo:</span>
                        <span>{paymentCycle.next_cycle.description}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-blue-700">Data Pagamento:</span>
                        <span className="font-medium">
                          {financialUtils.formatDate(
                            paymentCycle.next_cycle.payment_date,
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-gray-50 rounded">
                  <div className="text-sm text-gray-600 mb-1">
                    Programmazione
                  </div>
                  <div className="text-xs text-gray-500">
                    • 1-15 del mese: pagamento elaborato il 15
                    <br />• 16-fine mese: pagamento elaborato l'ultimo giorno
                    del mese
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Azioni Rapide */}
        <motion.div
          initial={{ opacity: 0, y: 32 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ type: "spring", stiffness: 130, damping: 20 }}
        >
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Azioni Rapide</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                <Button
                  onClick={() => router.push("/dashboard/settings/payouts")}
                  className="flex items-center justify-center gap-2"
                  color="white"
                  backgroundColor="var(--accent)"
                  text="💳 Pagamenti"
                />

                <Button
                  onClick={() => router.push("/dashboard/bookings")}
                  className="flex items-center justify-center gap-2"
                  color="white"
                  backgroundColor="var(--accent)"
                  text="📊 Prenotazioni"
                />
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </MobilePageStart>
  );
};

export default FinancialDashboard;
