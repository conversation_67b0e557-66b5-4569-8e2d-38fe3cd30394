"use client";
import React, { useEffect, useState } from 'react';
import { chekinApi, type ChekinBookingEvent } from '@/services/chekinApi';
import { StatusBadge } from '@/components/status-badge';
import { UserPlus, Shield, BarChart3, Clock, CheckCircle2, Mail, Send, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { resendChekinEmail } from '@/services/api';

interface BookingChekinStatusProps {
  bookingId: string;
  propertyId?: string;
  compact?: boolean;
  className?: string;
  bookingData?: any; // Pass full booking data for email fields
  onBookingUpdate?: () => void; // Callback to refresh booking data
  onNavigateToBookings?: () => void; // Callback to navigate to bookings dashboard
}

// Event type mapping with enhanced visual states
type EventTypeInfo = {
  hasEvents: boolean;
  variant: 'default' | 'yellow' | 'green' | 'black' | 'red';
  count: number;
  latestStatus?: string;
  latestDate?: string;
};

export const BookingChekinStatus: React.FC<BookingChekinStatusProps> = ({
  bookingId,
  propertyId,
  compact = false,
  className = "",
  bookingData: externalBookingData,
  onBookingUpdate,
  onNavigateToBookings
}) => {
  const [bookingData, setBookingData] = useState<ChekinBookingEvent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resendingEmail, setResendingEmail] = useState(false);

  useEffect(() => {
    let mounted = true;

    const fetchData = async () => {
      if (!bookingId) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const bookings = await chekinApi.getEventsByBooking(bookingId);
        if (mounted) {
          setBookingData(bookings[0] || null);
        }
      } catch (err) {
        if (mounted) {
          setError('Errore nel caricamento dei dati Chekin');
          console.error('Error fetching Chekin booking data:', err);
        }
      } finally {
        if (mounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      mounted = false;
    };
  }, [bookingId]);

  // Function to resend Chekin email
  const handleResendEmail = async () => {
    if (!bookingId || resendingEmail) return;
    
    // Get email fields from external booking data if available
    const emailSent = externalBookingData?.chekin_email_sent;
    const sendCount = externalBookingData?.chekin_email_send_count || 0;
    const chekinSynced = externalBookingData?.chekin_synced;
    const customerEmail = externalBookingData?.customer?.email;
    
    // Pre-validation checks
    if (!customerEmail) {
      toast.error('Impossibile inviare email: nessun indirizzo email del cliente');
      return;
    }
    
    if (!chekinSynced) {
      toast.error('Impossibile inviare email: prenotazione non sincronizzata con Chekin');
      return;
    }
    
    if (sendCount >= 3) {
      toast.error('Limite giornaliero raggiunto: massimo 3 email al giorno per prenotazione');
      return;
    }
    
    setResendingEmail(true);
    
    try {
      const result = await resendChekinEmail(bookingId);
      
      if (!result.ok) {
        // Handle validation errors
        if (result.status === 400 && result.data.booking_id) {
          toast.error(result.data.booking_id[0]);
          return;
        }
        
        throw new Error('Errore durante l\'invio dell\'email');
      }
      
      const data = result.data;
      
      if (data.success) {
        toast.success(`Email Chekin inviata con successo a ${data.data.customer_email}`);
        
        // Show warning if email was sent recently
        if (data.warning) {
          toast.error(data.warning, { duration: 4000 });
        }
        
        // Show remaining sends info
        if (data.data.remaining_sends_today !== undefined) {
          toast.success(`Invii rimanenti oggi: ${data.data.remaining_sends_today}`, { duration: 3000 });
        }
        
        // Optionally refresh booking data if callback provided
        if (onBookingUpdate) {
          onBookingUpdate();
        }
        
        // Navigate to bookings dashboard after successful send
        if (onNavigateToBookings) {
          toast.success('Reindirizzamento alla lista prenotazioni...', { duration: 2000 });
          setTimeout(() => {
            onNavigateToBookings();
          }, 2000); // Wait 2 seconds to show success message
        }
      } else {
        toast.error(data.message || 'Errore durante l\'invio dell\'email');
      }
      
    } catch (error: any) {
      console.error('Error resending Chekin email:', error);
      toast.error(error.message || 'Errore di rete durante l\'invio dell\'email');
    } finally {
      setResendingEmail(false);
    }
  };

  // Enhanced logic to determine event status based on actual event types
  const getEventTypeInfo = (eventCategory: string): EventTypeInfo => {
    if (!bookingData || !bookingData.events) {
      return { hasEvents: false, variant: 'default', count: 0 };
    }
    
    let relevantEvents: typeof bookingData.events = [];
    
    switch (eventCategory) {
      case 'Guest':
        relevantEvents = bookingData.events.filter(e => 
          e.event_type.includes('Guest')
        );
        break;
      case 'Police':
        relevantEvents = bookingData.events.filter(e => 
          e.event_type.includes('Police') || e.event_type.includes('Alloggiati')
        );
        break;
      case 'Stat':
        relevantEvents = bookingData.events.filter(e => 
          e.event_type.includes('Stat') || e.event_type.includes('ISTAT')
        );
        break;
    }

    if (relevantEvents.length === 0) {
      return { hasEvents: false, variant: 'default', count: 0 };
    }

    // Get the latest event for this category
    const latestEvent = relevantEvents[relevantEvents.length - 1];
    
    let variant: 'default' | 'yellow' | 'green' | 'black' | 'red' = 'yellow';
    
    // Determine variant based on event type and status
    if (latestEvent.status === 'failed' || latestEvent.event_type.includes('error')) {
      variant = 'red';
    } else if (latestEvent.status === 'processed' || latestEvent.event_type.includes('complete')) {
      variant = 'green';
    } else if (latestEvent.event_type.includes('disconnected')) {
      variant = 'black';
    } else if (latestEvent.status === 'pending') {
      variant = 'yellow';
    }

    return {
      hasEvents: true,
      variant,
      count: relevantEvents.length,
      latestStatus: latestEvent.status,
      latestDate: latestEvent.created_at
    };
  };

  // Get email status information
  const getEmailInfo = (): EventTypeInfo => {
    if (!externalBookingData) {
      return { hasEvents: false, variant: 'default', count: 0 };
    }
    
    const emailSent = externalBookingData.chekin_email_sent;
    const sendCount = externalBookingData.chekin_email_send_count || 0;
    const emailSentAt = externalBookingData.chekin_email_sent_at;
    
    if (!emailSent) {
      return { hasEvents: false, variant: 'default', count: 0 };
    }
    
    // Determine variant based on send status and count
    let variant: 'default' | 'yellow' | 'green' | 'black' | 'red' = 'green';
    
    if (sendCount >= 3) {
      variant = 'yellow'; // Warning: at limit
    } else if (sendCount >= 2) {
      variant = 'green'; // Success but approaching limit
    }
    
    return {
      hasEvents: true,
      variant,
      count: sendCount,
      latestStatus: emailSent ? 'sent' : 'pending',
      latestDate: emailSentAt
    };
  };

  // Special case for guest status
  const getGuestInfo = (): EventTypeInfo => {
    const guestInfo = getEventTypeInfo('Guest');
    
    // Override with booking-level guest_created if available
    if (bookingData?.guest_created && guestInfo.hasEvents) {
      return {
        ...guestInfo,
        variant: 'green'
      };
    }
    
    return guestInfo;
  };

  // Don't render anything only if there's an error or still loading
  // If no data, we'll show greyed out icons
  if (loading || error) {
    return null;
  }

  // If no booking data, create empty state with all icons greyed out
  const hasNoData = !bookingData;
  
  const guestInfo = hasNoData ? { hasEvents: false, variant: 'default' as const, count: 0 } : getGuestInfo();
  const policeInfo = hasNoData ? { hasEvents: false, variant: 'default' as const, count: 0 } : getEventTypeInfo('Police');
  const statInfo = hasNoData ? { hasEvents: false, variant: 'default' as const, count: 0 } : getEventTypeInfo('Stat');
  const emailInfo = getEmailInfo();

  // Enhanced compact view with better visual feedback
  if (compact) {
    return (
      <div className={`flex items-center gap-3 ${className}`}>
        <div className="flex items-center gap-1.5">
          <div className={`relative ${!guestInfo.hasEvents ? 'opacity-30' : ''}`}>
            <StatusBadge 
              icon={UserPlus}
              variant={guestInfo.hasEvents ? guestInfo.variant : 'default'}
              size="sm"
            />
            {guestInfo.hasEvents && guestInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {guestInfo.count}
              </span>
            )}
          </div>
          <span className={`text-xs font-medium ${guestInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
            Ospite
          </span>
        </div>
        
        <div className="flex items-center gap-1.5">
          <div className={`relative ${!policeInfo.hasEvents ? 'opacity-30' : ''}`}>
            <StatusBadge 
              icon={Shield}
              variant={policeInfo.hasEvents ? policeInfo.variant : 'default'}
              size="sm"
            />
            {policeInfo.hasEvents && policeInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {policeInfo.count}
              </span>
            )}
          </div>
          <span className={`text-xs font-medium ${policeInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
            Alloggiati
          </span>
        </div>
        
        <div className="flex items-center gap-1.5">
          <div className={`relative ${!statInfo.hasEvents ? 'opacity-30' : ''}`}>
            <StatusBadge 
              icon={BarChart3}
              variant={statInfo.hasEvents ? statInfo.variant : 'default'}
              size="sm"
            />
            {statInfo.hasEvents && statInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {statInfo.count}
              </span>
            )}
          </div>
          <span className={`text-xs font-medium ${statInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
            ISTAT
          </span>
        </div>
        
        {/* Email Status */}
        <div className="flex items-center gap-1.5">
          <div className={`relative ${!emailInfo.hasEvents ? 'opacity-30' : ''}`}>
            <StatusBadge 
              icon={Mail}
              variant={emailInfo.hasEvents ? emailInfo.variant : 'default'}
              size="sm"
            />
            {emailInfo.hasEvents && emailInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                {emailInfo.count}
              </span>
            )}
          </div>
          <span className={`text-xs font-medium ${emailInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
            Email
          </span>
        </div>
        
        {hasNoData && (
          <span className="text-xs text-gray-400 italic ml-2">
            (Nessun dato)
          </span>
        )}
      </div>
    );
  }

  // Enhanced full view with detailed event information
  return (
    <div className={`bg-gradient-to-br from-white/80 to-indigo-50/30 border border-indigo-200 rounded-lg p-4 shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h4 className="text-sm font-semibold text-[#133157] flex items-center gap-2">
          <CheckCircle2 className="w-4 h-4 text-indigo-600" />
          Stato Registrazioni Chekin
        </h4>
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Clock className="w-3 h-3" />
          <span>{bookingData?.total_events || 0} eventi</span>
        </div>
      </div>
      
      <div className="grid grid-cols-4 gap-4">
        {/* Guest Status */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`relative transition-all duration-200 ${!guestInfo.hasEvents ? 'opacity-30 scale-95' : 'scale-100'}`}>
            <StatusBadge 
              icon={UserPlus}
              variant={guestInfo.hasEvents ? guestInfo.variant : 'default'}
              size="md"
            />
            {guestInfo.hasEvents && guestInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                {guestInfo.count}
              </span>
            )}
          </div>
          <div className="text-center">
            <span className={`text-xs font-medium block ${guestInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
              Ospite
            </span>
            {guestInfo.hasEvents && guestInfo.latestStatus && (
              <span className={`text-xs capitalize ${
                guestInfo.variant === 'green' ? 'text-green-600' : 
                guestInfo.variant === 'yellow' ? 'text-yellow-600' : 
                guestInfo.variant === 'red' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {guestInfo.latestStatus}
              </span>
            )}
          </div>
        </div>
        
        {/* Police Status */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`relative transition-all duration-200 ${!policeInfo.hasEvents ? 'opacity-30 scale-95' : 'scale-100'}`}>
            <StatusBadge 
              icon={Shield}
              variant={policeInfo.hasEvents ? policeInfo.variant : 'default'}
              size="md"
            />
            {policeInfo.hasEvents && policeInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                {policeInfo.count}
              </span>
            )}
          </div>
          <div className="text-center">
            <span className={`text-xs font-medium block ${policeInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
              Alloggiati
            </span>
            {policeInfo.hasEvents && policeInfo.latestStatus && (
              <span className={`text-xs capitalize ${
                policeInfo.variant === 'green' ? 'text-green-600' : 
                policeInfo.variant === 'yellow' ? 'text-yellow-600' : 
                policeInfo.variant === 'red' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {policeInfo.latestStatus}
              </span>
            )}
          </div>
        </div>
        
        {/* Stat Status */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`relative transition-all duration-200 ${!statInfo.hasEvents ? 'opacity-30 scale-95' : 'scale-100'}`}>
            <StatusBadge 
              icon={BarChart3}
              variant={statInfo.hasEvents ? statInfo.variant : 'default'}
              size="md"
            />
            {statInfo.hasEvents && statInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                {statInfo.count}
              </span>
            )}
          </div>
          <div className="text-center">
            <span className={`text-xs font-medium block ${statInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
              ISTAT
            </span>
            {statInfo.hasEvents && statInfo.latestStatus && (
              <span className={`text-xs capitalize ${
                statInfo.variant === 'green' ? 'text-green-600' : 
                statInfo.variant === 'yellow' ? 'text-yellow-600' : 
                statInfo.variant === 'red' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {statInfo.latestStatus}
              </span>
            )}
          </div>
        </div>
        
        {/* Email Status */}
        <div className="flex flex-col items-center space-y-2">
          <div className={`relative transition-all duration-200 ${!emailInfo.hasEvents ? 'opacity-30 scale-95' : 'scale-100'}`}>
            <StatusBadge 
              icon={Mail}
              variant={emailInfo.hasEvents ? emailInfo.variant : 'default'}
              size="md"
            />
            {emailInfo.hasEvents && emailInfo.count > 1 && (
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
                {emailInfo.count}
              </span>
            )}
          </div>
          <div className="text-center">
            <span className={`text-xs font-medium block ${emailInfo.hasEvents ? 'text-gray-700' : 'text-gray-400'}`}>
              Email
            </span>
            {emailInfo.hasEvents && emailInfo.latestStatus && (
              <span className={`text-xs capitalize ${
                emailInfo.variant === 'green' ? 'text-green-600' : 
                emailInfo.variant === 'yellow' ? 'text-yellow-600' : 
                emailInfo.variant === 'red' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {emailInfo.latestStatus}
              </span>
            )}
          </div>
        </div>
      </div>
      
      {/* Email Resend Button */}
      {externalBookingData && (
        <div className="mt-4 pt-3 border-t border-indigo-100">
          <div className="flex items-center justify-between">
            <div className="text-xs text-gray-600">
              <span className="font-medium">Email Chekin:</span>
              {externalBookingData.chekin_email_sent ? (
                <span className="text-green-600 ml-1">
                  Inviata {externalBookingData.chekin_email_send_count || 1} volte
                  {externalBookingData.chekin_email_sent_at && (
                    <span className="text-gray-500 ml-1">
                      ({new Date(externalBookingData.chekin_email_sent_at).toLocaleDateString('it-IT', {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })})
                    </span>
                  )}
                </span>
              ) : (
                <span className="text-gray-500 ml-1">Non ancora inviata</span>
              )}
            </div>
            
            <button
              onClick={handleResendEmail}
              disabled={resendingEmail || !externalBookingData.customer?.email || !externalBookingData.chekin_synced || (externalBookingData.chekin_email_send_count || 0) >= 3}
              className={`flex items-center gap-2 px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${
                resendingEmail 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : !externalBookingData.customer?.email || !externalBookingData.chekin_synced || (externalBookingData.chekin_email_send_count || 0) >= 3
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-indigo-600 text-white hover:bg-indigo-700 active:scale-95'
              }`}
            >
              {resendingEmail ? (
                <>
                  <Loader2 className="w-3 h-3 animate-spin" />
                  Invio...
                </>
              ) : (
                <>
                  <Send className="w-3 h-3" />
                  {externalBookingData.chekin_email_sent ? 'Reinvia' : 'Invia'} Email
                </>
              )}
            </button>
          </div>
          
          {/* Send limit indicator */}
          {externalBookingData.chekin_email_send_count > 0 && (
            <div className="mt-2 text-xs text-gray-500">
              Invii oggi: {externalBookingData.chekin_email_send_count}/3
              {(externalBookingData.chekin_email_send_count || 0) >= 3 && (
                <span className="text-amber-600 ml-2">⚠️ Limite giornaliero raggiunto</span>
              )}
            </div>
          )}
          
          {/* Validation warnings */}
          {!externalBookingData.customer?.email && (
            <div className="mt-2 text-xs text-red-500">⚠️ Nessun indirizzo email del cliente</div>
          )}
          {!externalBookingData.chekin_synced && (
            <div className="mt-2 text-xs text-red-500">⚠️ Prenotazione non sincronizzata con Chekin</div>
          )}
        </div>
      )}
      
      {/* Summary Information */}
      <div className="mt-4 pt-3 border-t border-indigo-100">
        {hasNoData && (
          <div className="text-center py-2">
            <span className="text-xs text-gray-400 italic">
              Nessun dato Chekin disponibile per questa prenotazione
            </span>
          </div>
        )}
        
        {!hasNoData && (
          <>
            <div className="flex justify-between items-center text-xs">
              <div className="flex items-center gap-4">
                <span className="text-gray-600">
                  <span className="font-medium">Stato:</span> {bookingData?.registration_status || 'Non disponibile'}
                </span>
                {bookingData?.guest_created && (
                  <span className="flex items-center gap-1 text-green-600">
                    <CheckCircle2 className="w-3 h-3" />
                    Ospite creato
                  </span>
                )}
                {externalBookingData?.chekin_email_sent && (
                  <span className="flex items-center gap-1 text-blue-600">
                    <Mail className="w-3 h-3" />
                    Email inviata
                  </span>
                )}
              </div>
              {bookingData?.latest_status_update && (
                <span className="text-gray-500">
                  {new Date(bookingData.latest_status_update).toLocaleDateString('it-IT', {
                    day: '2-digit',
                    month: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              )}
            </div>

            {/* Event Details for Active Events Only */}
            {bookingData?.events && bookingData.events.length > 0 && (
              <div className="mt-2 pt-2 border-t border-gray-100">
                <div className="text-xs text-gray-600">
                  <span className="font-medium">Eventi recenti:</span>
                  <div className="mt-1 space-y-1">
                    {bookingData.events.slice(-3).reverse().map((event, index) => (
                      <div key={event.id} className="flex justify-between items-center py-1">
                        <span className="flex items-center gap-2">
                          <div className={`w-1.5 h-1.5 rounded-full ${
                            event.status === 'processed' ? 'bg-green-400' : 
                            event.status === 'pending' ? 'bg-yellow-400' : 
                            event.status === 'failed' ? 'bg-red-400' : 'bg-gray-400'
                          }`}></div>
                          <span className="truncate">{event.event_type}</span>
                        </span>
                        <span className="text-gray-400 text-xs">
                          {new Date(event.created_at).toLocaleDateString('it-IT', {
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default BookingChekinStatus;