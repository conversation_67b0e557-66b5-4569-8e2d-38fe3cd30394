import React from 'react';
import { ChekinStatusBadge, getStatusText } from './ChekinStatusBadge';

interface ChekinStatus {
  guest_created?: boolean;
  guest_creation_date?: string;
  police_connected?: boolean;
  police_registration_status?: 'pending' | 'complete' | 'error';
  stat_connected?: boolean;
  stat_registration_status?: 'pending' | 'complete' | 'error';
  has_active_guest?: boolean;
  latest_status_update?: string;
}

interface ChekinStatusPanelProps {
  status: ChekinStatus;
  className?: string;
}

export const ChekinStatusPanel: React.FC<ChekinStatusPanelProps> = ({ 
  status, 
  className = "" 
}) => {
  const getGuestStatus = () => {
    if (status.guest_created) return 'guest_created';
    return 'unknown';
  };

  const getPoliceStatus = () => {
    if (!status.police_connected) return 'police_disconnected';
    if (status.police_registration_status === 'complete') return 'police_complete';
    if (status.police_registration_status === 'error') return 'police_error';
    return 'police_connected';
  };

  const getStatStatus = () => {
    if (!status.stat_connected) return 'stat_disconnected';
    if (status.stat_registration_status === 'complete') return 'stat_complete';
    if (status.stat_registration_status === 'error') return 'stat_error';
    return 'stat_connected';
  };

  return (
    <div className={`bg-white rounded-lg p-4 ${className}`}>
      <h3 className="text-lg font-semibold mb-4 text-[#133157]">
        Stato Registrazioni Chekin
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Guest Registration */}
        <div className="flex flex-col items-center space-y-3">
          <ChekinStatusBadge 
            iconStatus={getGuestStatus()}
            size="md"
          />
          <div className="text-center">
            <h4 className="text-sm font-medium text-gray-900">
              Registrazione Ospite
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText(getGuestStatus())}
            </p>
            {status.guest_creation_date && (
              <p className="text-xs text-gray-400 mt-1">
                {new Date(status.guest_creation_date).toLocaleDateString('it-IT')}
              </p>
            )}
          </div>
        </div>

        {/* Police Registration (Alloggati) */}
        <div className="flex flex-col items-center space-y-3">
          <ChekinStatusBadge 
            iconStatus={getPoliceStatus()}
            size="md"
          />
          <div className="text-center">
            <h4 className="text-sm font-medium text-gray-900">
              Denuncia Alloggiati
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText(getPoliceStatus())}
            </p>
            <div className="flex items-center justify-center mt-1">
              <div className={`w-2 h-2 rounded-full ${
                status.police_connected ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-xs text-gray-400 ml-1">
                {status.police_connected ? 'Connesso' : 'Disconnesso'}
              </span>
            </div>
          </div>
        </div>

        {/* ISTAT Registration */}
        <div className="flex flex-col items-center space-y-3">
          <ChekinStatusBadge 
            iconStatus={getStatStatus()}
            size="md"
          />
          <div className="text-center">
            <h4 className="text-sm font-medium text-gray-900">
              Comunicazione ISTAT
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              {getStatusText(getStatStatus())}
            </p>
            <div className="flex items-center justify-center mt-1">
              <div className={`w-2 h-2 rounded-full ${
                status.stat_connected ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-xs text-gray-400 ml-1">
                {status.stat_connected ? 'Connesso' : 'Disconnesso'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Status Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Stato Generale:</span>
          <span className={`font-medium ${
            status.guest_created && status.police_connected && status.stat_connected
              ? 'text-green-600' 
              : status.guest_created 
              ? 'text-yellow-600' 
              : 'text-red-600'
          }`}>
            {status.guest_created && status.police_connected && status.stat_connected
              ? 'Completamente Configurato'
              : status.guest_created 
              ? 'Parzialmente Configurato'
              : 'In Attesa di Configurazione'
            }
          </span>
        </div>
        
        {status.latest_status_update && (
          <div className="flex items-center justify-between text-xs text-gray-500 mt-2">
            <span>Ultimo Aggiornamento:</span>
            <span>{new Date(status.latest_status_update).toLocaleString('it-IT')}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChekinStatusPanel;