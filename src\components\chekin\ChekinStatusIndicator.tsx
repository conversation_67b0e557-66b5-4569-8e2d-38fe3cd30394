import React from 'react';
import { ChekinStatusBadge, getStatusText } from './ChekinStatusBadge';

interface ChekinStatusIndicatorProps {
  iconStatus: string;
  processingStatus?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  showRing?: boolean;
  className?: string;
  hasEvents?: boolean; // New prop to indicate if this status has actual events
  eventCount?: number; // New prop to show event count
  opacity?: number; // New prop for custom opacity
}

export const ChekinStatusIndicator: React.FC<ChekinStatusIndicatorProps> = ({
  iconStatus,
  processingStatus = "processed",
  showText = false,
  size = 'sm',
  showRing = true,
  className = "",
  hasEvents = true,
  eventCount = 0,
  opacity
}) => {
  // Determine visual state based on hasEvents
  const containerClass = `inline-flex items-center gap-2 transition-all duration-200 ${
    !hasEvents ? 'opacity-30 scale-95' : 'opacity-100 scale-100'
  } ${className}`;

  // Apply custom opacity if provided
  const style = opacity !== undefined ? { opacity } : undefined;

  return (
    <div className={containerClass} style={style}>
      <div className="relative">
        <ChekinStatusBadge 
          iconStatus={iconStatus}
          processingStatus={hasEvents ? processingStatus : "pending"}
          size={size}
          showRing={showRing && hasEvents}
        />
        {hasEvents && eventCount > 1 && (
          <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full min-w-[16px] h-4 flex items-center justify-center font-medium px-1">
            {eventCount}
          </span>
        )}
      </div>
      {showText && (
        <span className={`text-sm font-medium transition-colors ${
          hasEvents ? 'text-gray-700' : 'text-gray-400'
        }`}>
          {getStatusText(iconStatus)}
        </span>
      )}
    </div>
  );
};

export default ChekinStatusIndicator;