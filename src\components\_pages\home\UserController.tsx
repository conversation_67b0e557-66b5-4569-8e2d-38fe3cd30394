import {
  BellAlertIcon,
  ChatBubbleLeftIcon,
  StarIcon,
} from "@heroicons/react/16/solid";
import { useRouter } from "next/navigation";
import React, { useContext } from "react";
import { UserContext } from "@/components/_context/UserContext";
import { DashboardUnreadContext } from "@/components/_context/DashboardUnreadContext";

interface Props {
  name: string;
  picture: string;
}

function UserController({ name, picture }: Props) {
  const router = useRouter();
  const { hasBillingProfile, isCustomer, isNotOwnerPermission } =
    useContext(UserContext);
  const { unreadCounts } = useContext(DashboardUnreadContext);
  const firstName = name.split(" ")[0];

  return (
    <div
      className="w-full flex justify-between items-center px-3"
      style={{
        minHeight: "100px",
        height: "100px",
      }}
    >
      <div className="flex flex-row gap-2 items-center">
        <div style={{ position: "relative" }}>
          <img
            src={
              picture == ""
                ? "https://samidaiddar.no/sami-lavdi/wp-content/themes/samidaiddar/dist/images/profile-fallback.png"
                : picture
            }
            style={{
              height: "70px",
              width: "70px",
              borderRadius: "50%",
              border: `2px solid ${isNotOwnerPermission ? "#9e9e9e" : "var(--accent)"}`,
              cursor: "pointer",
            }}
            onClick={() => router.push("/dashboard/settings/account")}
          />
          {(!hasBillingProfile || !isCustomer) && !isNotOwnerPermission && (
            <div
              style={{
                position: "absolute",
                top: "5px",
                right: "5px",
                width: "12px",
                height: "12px",
                backgroundColor: "red",
                borderRadius: "50%",
                border: "2px solid white",
              }}
            />
          )}
        </div>
        <div>
          <p style={{ fontSize: "11px", color: "#********" }}>Ciao</p>
          <p style={{ fontSize: "20px", fontWeight: "600" }}>{firstName}</p>
        </div>
      </div>

      <div className="flex flex-row gap-2">
        <div
          className="w-[40px] h-[40px] flex items-center justify-center rounded-[10px]"
          style={{
            background: "var(--background)",
            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
            position: "relative",
          }}
          onClick={() => {
            router.push("/dashboard/notifications");
          }}
        >
          <BellAlertIcon width={20} color="var(--accent)" />
          {unreadCounts.unread_notifications_count > 0 && (
            <div
              style={{
                position: "absolute",
                top: "-8px",
                right: "-8px",
                minWidth: "20px",
                height: "20px",
                backgroundColor: "red",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "10px",
                color: "white",
                fontWeight: "bold",
              }}
            >
              {unreadCounts.unread_notifications_count > 99
                ? "99+"
                : unreadCounts.unread_notifications_count}
            </div>
          )}
        </div>

        <div
          className="w-[40px] h-[40px] flex items-center justify-center rounded-[10px]"
          style={{
            background: "var(--background)",
            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
            position: "relative",
          }}
          onClick={() => {
            router.push("/dashboard/reviews");
          }}
        >
          <StarIcon width={20} color="var(--accent)" />
          {unreadCounts.unread_reviews_count > 0 && (
            <div
              style={{
                position: "absolute",
                top: "-8px",
                right: "-8px",
                minWidth: "20px",
                height: "20px",
                backgroundColor: "red",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "10px",
                color: "white",
                fontWeight: "bold",
              }}
            >
              {unreadCounts.unread_reviews_count > 99
                ? "99+"
                : unreadCounts.unread_reviews_count}
            </div>
          )}
        </div>

        <div
          className="w-[40px] h-[40px] flex items-center justify-center rounded-[10px]"
          style={{
            background: "var(--background)",
            boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.1)",
            position: "relative",
          }}
          onClick={() => {
            router.push("/dashboard/chat");
          }}
        >
          <ChatBubbleLeftIcon width={20} color="var(--accent)" />
          {unreadCounts.unread_support_messages_count > 0 && (
            <div
              style={{
                position: "absolute",
                top: "-8px",
                right: "-8px",
                minWidth: "20px",
                height: "20px",
                backgroundColor: "red",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: "10px",
                color: "white",
                fontWeight: "bold",
              }}
            >
              {unreadCounts.unread_support_messages_count > 99
                ? "99+"
                : unreadCounts.unread_support_messages_count}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default UserController;
