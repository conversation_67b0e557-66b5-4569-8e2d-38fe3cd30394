import React, { useState, useEffect } from "react";
import styles from "./monthly.module.css";
import Button from "@/components/buttons/Button";
import { TagIcon } from "@heroicons/react/24/outline";
import { isToday as isTodayUtil } from "@/utils/dateUtils";
import BookingBlockModal from "@/components/modals/BookingBlockModal";
import BookingBlockDayModal from "@/components/modals/BookingBlockDayModal";
import ReservationDetailsModal from "@/components/modals/ReservationDetailsModal";
import { useCalendar } from "@/components/_context/CalendarContext";

interface EnhancedMonthTableProps {
  onCurrentMonth?: (date: Date) => void;
  onSelectDay: (date: Date) => void;
  selectedStartDate?: Date | null;
  selectedEndDate?: Date | null;
  res?: any[];
  rates: any[];
  property: string;
  onBlockDeactivated?: () => void;
}

const EnhancedMonthTable = ({
  onCurrentMonth,
  onSelectDay,
  selectedEndDate,
  selectedStartDate,
  res,
  rates,
  property,
  onBlockDeactivated,
}: EnhancedMonthTableProps) => {
  // States
  const [currentDate, setCurrentDate] = useState(new Date());
  const [reservations, setReservations] = useState<any[]>([]);
  const [tempSelectedDate, setTempSelectedDate] = useState<Date | null>(null);

  // Modal states
  const [showBlockModal, setShowBlockModal] = useState(false);
  const [selectedBlock, setSelectedBlock] = useState<any>(null);
  const [showBlockDayModal, setShowBlockDayModal] = useState(false);
  const [selectedBlockDay, setSelectedBlockDay] = useState<Date | null>(null);
  const [showReservationModal, setShowReservationModal] = useState(false);
  const [selectedReservation, setSelectedReservation] = useState<any>(null);

  // Calendar context for refreshing data
  const { fetchBlockedDates, fetchBookings, clearCache } = useCalendar();

  // Helper functions for calendar
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };

  const getMonthName = (monthIndex: number): string => {
    const months = [
      "Gennaio",
      "Febbraio",
      "Marzo",
      "Aprile",
      "Maggio",
      "Giugno",
      "Luglio",
      "Agosto",
      "Settembre",
      "Ottobre",
      "Novembre",
      "Dicembre",
    ];
    return months[monthIndex];
  };

  const prevMonth = (
    currentDate: Date,
    setCurrentDate: (date: Date) => void,
  ) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  const nextMonth = (
    currentDate: Date,
    setCurrentDate: (date: Date) => void,
  ) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  const getMonthDays = (year: number, month: number): number[][] => {
    // days of the month splitted up into weeks
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    const days: number[] = [];

    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    for (let i = 0; i < firstDayOfMonth; i++) {
      days.unshift(0);
    }

    const weeks: number[][] = [];
    let week: number[] = [];
    days.forEach((day, index) => {
      week.push(day);
      if ((index + 1) % 7 === 0 || index === days.length - 1) {
        weeks.push(week);
        week = [];
      }
    });

    return weeks;
  };

  // Helper functions for date checks
  const isToday = (day: number, currentDate: Date): boolean => {
    if (!day) return false;
    // Create a date object for the day we're checking
    const dateToCheck = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
    );

    // Use utility function for date comparison
    return isTodayUtil(dateToCheck);
  };

  const isPast = (day: number, currentDate: Date): boolean => {
    const today = new Date();
    const selectedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
    );

    // create a date for the previous day before the current one (ie. yesterday)
    const previousDay = new Date(today);
    previousDay.setDate(today.getDate() - 1);

    // returns true if it's one day before the current date,
    return selectedDate < previousDay;
  };

  const currentMonth = (day: number): boolean => {
    return day !== 0;
  };

  const isFull = (): boolean => {
    return false; // Placeholder for availability check
  };

  // Check if a date has a booking block (excluding end dates)
  const hasBookingBlock = (
    day: number,
  ): {
    hasBlock: boolean;
    blockData: any;
    isEndDate: boolean;
    isStartDate: boolean;
  } => {
    // Return false for invalid days (day === 0)
    if (!res || day === 0)
      return {
        hasBlock: false,
        blockData: null,
        isEndDate: false,
        isStartDate: false,
      };

    // Create date string in YYYY-MM-DD format to match API dates
    const dateToCheckStr = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;

    // Find any block that includes this date
    const block = res.find((item) => {
      if (item.type !== "block") return false;

      const blockStartStr = item.start_date;
      const blockEndStr = item.end_date;

      return dateToCheckStr >= blockStartStr && dateToCheckStr <= blockEndStr;
    });

    if (!block) {
      return {
        hasBlock: false,
        blockData: null,
        isEndDate: false,
        isStartDate: false,
      };
    }

    // Check if this is the start date of a block
    const isStartDate = block.start_date === dateToCheckStr;

    // Check if this is the end date of a block
    const isEndDate = block.end_date === dateToCheckStr;

    return {
      hasBlock: !!block && !isEndDate, // Only true if it's a block day but NOT the end date
      blockData: block,
      isEndDate: isEndDate,
      isStartDate: isStartDate,
    };
  };

  // Check if a date has a reservation
  const hasReservation = (
    day: number,
  ): { hasReservation: boolean; reservationData: any } => {
    // Return false for invalid days (day === 0)
    if (!res || day === 0)
      return { hasReservation: false, reservationData: null };

    // Create date string in YYYY-MM-DD format to match API dates
    const dateToCheckStr = `${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;

    const reservation = res.find((item) => {
      if (item.type !== "reservation") return false;

      const checkinDateStr = item.reservation_data.checkin_date.split("T")[0];
      const checkoutDateStr = item.reservation_data.checkout_date.split("T")[0];

      return (
        dateToCheckStr >= checkinDateStr && dateToCheckStr < checkoutDateStr
      );
    });

    return {
      hasReservation: !!reservation,
      reservationData: reservation,
    };
  };

  // Check if a day has both a booking block and a reservation (clash)
  const checkForClash = (
    day: number,
  ): { hasClash: boolean; reservationData: any } => {
    // Return false for invalid days (day === 0)
    if (!res || day === 0) return { hasClash: false, reservationData: null };

    // Check for booking blocks (excluding end dates)
    const { hasBlock, isEndDate } = hasBookingBlock(day);

    // Check for reservations using the same string comparison approach
    const { hasReservation: hasRes, reservationData } = hasReservation(day);

    // Return true if both exist (and it's not the end date of a block)
    return {
      hasClash: hasBlock && hasRes,
      reservationData: reservationData,
    };
  };

  // Handle day click with special handling for blocks, reservations, and date selection
  const handleDayClick = (day: number) => {
    if (day === 0 || isPast(day, currentDate)) return;

    // Create a date object for the clicked day
    const clickedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
    );

    // First check if there's a clash (both block and reservation)
    const { hasClash: clash, reservationData: clashReservation } =
      checkForClash(day);
    if (clash) {
      // Prioritize showing the reservation modal
      setSelectedReservation(clashReservation);
      setShowReservationModal(true);
      return;
    }

    // Check if day has a reservation (no clash)
    const { hasReservation: hasRes, reservationData } = hasReservation(day);
    if (hasRes) {
      setSelectedReservation(reservationData);
      setShowReservationModal(true);
      return;
    }

    // Check if day has a booking block (no clash)
    const { hasBlock, blockData, isEndDate, isStartDate } =
      hasBookingBlock(day);

    // If it's the start date of a block, show the day options modal
    if (isStartDate) {
      setSelectedBlock(blockData);
      setSelectedBlockDay(clickedDate);
      setShowBlockDayModal(true);
      return;
    }

    // If it's the end date of a block, treat it as a regular day (selectable)
    if (isEndDate) {
      // Handle as a regular date selection - continue to the date selection logic below
      // Don't return here, let it fall through to the date selection logic
    }

    // If it's a block day (but not the start or end date), show the day options modal
    if (hasBlock && !isStartDate && !isEndDate) {
      setSelectedBlock(blockData);
      setSelectedBlockDay(clickedDate);
      setShowBlockDayModal(true);
      return;
    }

    // Handle regular date selection
    handleDateSelection(clickedDate);
  };

  // Extracted date selection logic for cleaner code and better reusability
  const handleDateSelection = (clickedDate: Date) => {
    // Handle date selection for range
    if (!tempSelectedDate) {
      // First date in range
      setTempSelectedDate(clickedDate);
      // Also set the start date in the parent component
      onSelectDay(clickedDate);
    } else {
      // Second date in range
      // Determine which date is earlier to set as start date
      if (clickedDate < tempSelectedDate) {
        // If clicked date is earlier, it becomes the start date
        onSelectDay(clickedDate); // This will set the start date
        // We need to call it again with the temp date as the end date
        setTimeout(() => onSelectDay(tempSelectedDate!), 10); // Non-null assertion is safe here
      } else {
        // If temp date is earlier, it's already set as start date
        // Just set the clicked date as end date
        onSelectDay(clickedDate);
      }

      // Reset temporary selection
      setTempSelectedDate(null);
    }
  };

  // Handle block modal close - ensures selected date state persists
  const handleBlockModalClose = () => {
    setShowBlockModal(false);
    setSelectedBlock(null);
    // Note: The date selection state (tempSelectedDate and parent state)
    // should remain intact to allow continued interaction with the calendar
    // The date was already selected in handleDayClick before showing the modal
  };

  // Handle block day modal close
  const handleBlockDayModalClose = () => {
    setShowBlockDayModal(false);
    setSelectedBlock(null);
    setSelectedBlockDay(null);
  };

  // Handle manage block from day modal
  const handleManageBlockFromDay = () => {
    setShowBlockDayModal(false);
    setShowBlockModal(true);
    // selectedBlock is already set
  };

  // Handle block deactivation
  const handleBlockDeactivated = async () => {
    // Clear cache and refresh data
    clearCache();

    // Call the parent's block deactivation handler if provided
    if (onBlockDeactivated) {
      await onBlockDeactivated();
    } else {
      // Fallback: Refresh the calendar data locally
      if (property) {
        const startDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth(),
          1,
        );
        const endDate = new Date(
          currentDate.getFullYear(),
          currentDate.getMonth() + 1,
          0,
        );

        const startDateStr = startDate.toISOString().split("T")[0];
        const endDateStr = endDate.toISOString().split("T")[0];

        await Promise.all([
          fetchBlockedDates(property, startDateStr, endDateStr),
          fetchBookings(property, startDateStr, endDateStr),
        ]);
      }
    }
  };

  // Handle reservation modal close
  const handleReservationModalClose = () => {
    setShowReservationModal(false);
    setSelectedReservation(null);
  };

  useEffect(() => {
    onCurrentMonth && onCurrentMonth(currentDate);
  }, [currentDate]);

  useEffect(() => {
    setReservations(
      res
        ? res
            .map((reservation) => {
              if (reservation.type === "reservation") {
                return {
                  type: "reservation",
                  start:
                    reservation.reservation_data.checkin_date.split("T")[0],
                  end: reservation.reservation_data.checkout_date.split("T")[0],
                  border: "green",
                  background: "#46d11f16",
                };
              } else if (reservation.type === "block") {
                return {
                  type: "block",
                  start: reservation.start_date,
                  end: reservation.end_date,
                  border: "red",
                  background: "#ff000016",
                };
              }
              return null;
            })
            .filter(Boolean)
        : [],
    );
  }, [res]);

  return (
    <section className={styles.section_calendar}>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          gap: "1rem",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        {/* Calendar section */}
        <div className={styles.calendar}>
          <div>
            <div className="flex flex-row justify-between items-center">
              <h2 className="font-bold tracking-wide">
                {`${getMonthName(currentDate.getMonth())} ${currentDate.getFullYear()}`}
              </h2>

              <div className={styles.calendar_controls}>
                <div className="w-[80px]">
                  <Button
                    onClick={() => prevMonth(currentDate, setCurrentDate)}
                    text={"Indietro"}
                    color="white"
                    backgroundColor="#00000080"
                  />
                </div>
                <div className="w-[80px]">
                  <Button
                    onClick={() => nextMonth(currentDate, setCurrentDate)}
                    text={"Avanti"}
                    color="white"
                    backgroundColor="var(--blue)"
                  />
                </div>
              </div>
            </div>
            <div className={styles.container_calendar}>
              <table className={styles.calendar_table}>
                <thead className={styles.calendar_head}>
                  <tr className={styles.calendar_head_container}>
                    <th className={styles.br}>Dom</th>
                    <th className={styles.th}>Lun</th>
                    <th className={styles.th}>Mar</th>
                    <th className={styles.th}>Mer</th>
                    <th className={styles.th}>Gio</th>
                    <th className={styles.th}>Ven</th>
                    <th className={styles.br_inverted}>Sab</th>
                  </tr>
                </thead>

                <tbody>
                  {getMonthDays(
                    currentDate.getFullYear(),
                    currentDate.getMonth(),
                  ).map((week, weekIndex) => (
                    <tr className={styles.cont_days} key={weekIndex}>
                      {week.map((day, dayIndex) => {
                        const startDateSelected =
                          selectedStartDate && new Date(selectedStartDate);
                        startDateSelected &&
                          startDateSelected.setDate(
                            startDateSelected.getDate() + 1,
                          );
                        // Only create a valid date object for days that exist
                        const dayDateSelected =
                          day !== 0
                            ? new Date(
                                currentDate.getFullYear(),
                                currentDate.getMonth(),
                                day,
                              )
                            : null;

                        // Check for booking blocks, reservations, and clashes
                        const { hasBlock, isEndDate } = hasBookingBlock(day);
                        const { hasReservation: hasRes } = hasReservation(day);
                        const { hasClash: dayHasClash } = checkForClash(day);

                        // Check if this day is the temporary selected date
                        const isTempSelected =
                          tempSelectedDate &&
                          tempSelectedDate.getDate() === day &&
                          tempSelectedDate.getMonth() ===
                            currentDate.getMonth() &&
                          tempSelectedDate.getFullYear() ===
                            currentDate.getFullYear();

                        return (
                          <td
                            key={dayIndex}
                            className={`${
                              day === 0 || !currentMonth(day)
                                ? styles.not_current_month
                                : ""
                            } ${
                              day !== 0 && isToday(day, currentDate)
                                ? styles.today
                                : ""
                            } ${
                              day !== 0 && !isPast(day, currentDate) && isFull()
                                ? styles.day_full
                                : ""
                            } ${
                              day !== 0 && isPast(day, currentDate)
                                ? styles.past_day
                                : styles.validChoice
                            }`}
                            style={{
                              background: isTempSelected
                                ? "rgba(31, 41, 55, 0.3)"
                                : "",
                              cursor: hasBlock || hasRes ? "pointer" : "",
                              // Add a special border for days with clashes
                              border: dayHasClash ? "2px dashed #fcbd4c" : "",
                            }}
                          >
                            <div
                              className={`${
                                day !== 0 && isPast(day, currentDate)
                                  ? styles.disabled
                                  : ""
                              }`}
                              style={{
                                position: "relative",
                                display: "flex",
                                flexDirection: "column",
                                justifyContent: "center",
                                alignItems: "center",
                              }}
                              onClick={() => day !== 0 && handleDayClick(day)}
                            >
                              <div
                                style={{
                                  fontSize: "12px",
                                  transform: "translateY(-3px)",
                                  background: isToday(day, currentDate)
                                    ? "var(--blue)"
                                    : "",
                                  width: "20px",
                                  height: "20px",
                                  borderRadius: "50%",
                                  color: isToday(day, currentDate)
                                    ? "white"
                                    : "black",
                                  display: "flex",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  zIndex: "5",
                                }}
                              >
                                {day === 0 ? "" : day}
                              </div>

                              <p
                                style={{
                                  fontSize: "10px",
                                  color: "#00000080",
                                  transform: "translateY(-8px)",
                                }}
                                className="mt-1"
                              >
                                {rates && rates.length > 0
                                  ? rates.map((rate: any, index: number) => {
                                      const { rates } = rate;

                                      if (
                                        rate.date !==
                                        `${new Date(currentDate).getFullYear()}-${(new Date(currentDate).getMonth() + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`
                                      ) {
                                        return null;
                                      }

                                      const firstKey = Object.keys(rates)[0];
                                      const firstValue = rates[firstKey];

                                      return (
                                        <p key={index}>
                                          {firstValue !== ""
                                            ? `${firstValue} €`
                                            : ""}
                                        </p>
                                      );
                                    })
                                  : "--- €"}
                              </p>

                              {reservations.map((reservation, index) => {
                                const startDate = new Date(reservation.start);
                                const endDate = new Date(reservation.end);
                                endDate.setDate(endDate.getDate() - 1);

                                // Only create a valid date object for days that exist
                                const dayDate =
                                  day !== 0
                                    ? new Date(
                                        currentDate.getFullYear(),
                                        currentDate.getMonth(),
                                        day,
                                      )
                                    : null;
                                return (
                                  <div key={index}>
                                    {day !== 0 && // Only show for valid days
                                      new Date(reservation.start).getDate() ===
                                        day &&
                                      new Date(reservation.start).getMonth() ===
                                        currentDate.getMonth() &&
                                      new Date(
                                        reservation.start,
                                      ).getFullYear() ===
                                        currentDate.getFullYear() && (
                                        <div
                                          className={styles.reservationBar}
                                          style={{
                                            borderRadius: "20px 0 0 20px",
                                            width: "60%",
                                            transform:
                                              "translateY(-8px) translateX(20px)",

                                            borderTop: `${reservation.border} 1.5px solid`,
                                            borderBottom: `${reservation.border}  1.5px solid`,
                                            borderLeft: `${reservation.border}  1.5px solid`,

                                            display: "flex",
                                            justifyContent: "center",
                                            alignItems: "center",
                                            background: `${reservation.background}`,
                                          }}
                                        >
                                          <TagIcon
                                            width={12}
                                            color={`${reservation.border}`}
                                            style={{ transform: "translateY" }}
                                          />
                                          <p className={styles.nameReservation}>
                                            {/* Federico Lacchini */}
                                          </p>
                                        </div>
                                      )}

                                    {
                                      // Check if the current day tile is between the start and end dates (inclusive)
                                      day !== 0 && // Only show for valid days
                                        dayDate && // Ensure dayDate is not null
                                        dayDate >= startDate &&
                                        dayDate < endDate &&
                                        // Ensure we're in the same year
                                        dayDate.getFullYear() ===
                                          startDate.getFullYear() && (
                                          <div
                                            className={styles.reservationBar}
                                            style={{
                                              width: "100%",
                                              borderTop: `${reservation.border} 1.5px solid`,
                                              borderBottom: `${reservation.border} 1.5px solid`,
                                              transform: "translateY(-8px)",
                                              background: `${reservation.background}`,
                                            }}
                                          />
                                        )
                                    }
                                    {day !== 0 && // Only show for valid days
                                      new Date(reservation.end).getDate() ===
                                        day &&
                                      new Date(reservation.end).getMonth() ===
                                        currentDate.getMonth() &&
                                      new Date(
                                        reservation.end,
                                      ).getFullYear() ===
                                        currentDate.getFullYear() && (
                                        <div
                                          className={styles.reservationBar}
                                          style={{
                                            borderRadius: "0 20px 20px 0",
                                            width: "30%",

                                            borderTop: `${reservation.border} 1.5px solid`,
                                            borderBottom: `${reservation.border} 1.5px solid`,
                                            borderRight: `${reservation.border} 1.5px solid`,

                                            transform: "translateY(-8px)",
                                            background:
                                              reservation.border === "red" &&
                                              reservation.type === "block"
                                                ? "rgba(255, 107, 107, 0.1)" // Lighter background for block end dates
                                                : `${reservation.background}`,
                                          }}
                                        />
                                      )}
                                  </div>
                                );
                              })}

                              {day !== 0 && // Only show for valid days
                                selectedStartDate &&
                                !selectedEndDate &&
                                selectedStartDate.getDate() === day && (
                                  <div
                                    className={styles.selectedRange}
                                    style={{
                                      borderRadius: "10px",
                                    }}
                                  ></div>
                                )}

                              {
                                // Check if the current day tile is between the start and end dates (inclusive)
                                day !== 0 && // Only show for valid days
                                  startDateSelected &&
                                  selectedEndDate &&
                                  dayDateSelected && // Ensure dayDateSelected is not null
                                  startDateSelected && // Ensure startDateSelected is not null
                                  dayDateSelected >= startDateSelected &&
                                  dayDateSelected < selectedEndDate && (
                                    <div
                                      className={styles.selectedRange}
                                      style={{}}
                                    ></div>
                                  )
                              }

                              {day !== 0 && // Only show for valid days
                                selectedStartDate &&
                                selectedEndDate &&
                                selectedStartDate.getDate() === day &&
                                selectedStartDate.getMonth() ===
                                  currentDate.getMonth() && (
                                  <div
                                    className={styles.selectedRange}
                                    style={{
                                      borderRadius: "10px 0 0 10px",
                                    }}
                                  ></div>
                                )}

                              {day !== 0 && // Only show for valid days
                                selectedStartDate &&
                                selectedEndDate &&
                                selectedEndDate.getDate() === day &&
                                selectedEndDate.getMonth() ===
                                  currentDate.getMonth() && (
                                  <div
                                    className={styles.selectedRange}
                                    style={{
                                      borderRadius: "0 10px 10px 0",
                                    }}
                                  ></div>
                                )}
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Block Modal */}
      {showBlockModal && selectedBlock && (
        <BookingBlockModal
          isOpen={showBlockModal}
          onClose={handleBlockModalClose}
          blockData={{
            id: selectedBlock.id,
            start_date: selectedBlock.start_date,
            end_date: selectedBlock.end_date,
            is_active: selectedBlock.is_active,
            reason: selectedBlock.reason || "",
          }}
          onDeactivate={handleBlockDeactivated}
        />
      )}

      {/* Booking Block Day Modal */}
      {showBlockDayModal && selectedBlock && selectedBlockDay && (
        <BookingBlockDayModal
          isOpen={showBlockDayModal}
          onClose={handleBlockDayModalClose}
          selectedDate={selectedBlockDay}
          blockData={{
            id: selectedBlock.id,
            start_date: selectedBlock.start_date,
            end_date: selectedBlock.end_date,
            is_active: selectedBlock.is_active,
            reason: selectedBlock.reason || "",
          }}
          property={property}
          onManageBlock={handleManageBlockFromDay}
        />
      )}

      {/* Reservation Details Modal */}
      {showReservationModal && selectedReservation && (
        <ReservationDetailsModal
          isOpen={showReservationModal}
          onClose={handleReservationModalClose}
          reservationData={selectedReservation}
        />
      )}
    </section>
  );
};

export default EnhancedMonthTable;
