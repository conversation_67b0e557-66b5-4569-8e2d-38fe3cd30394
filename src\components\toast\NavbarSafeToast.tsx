import toast, { Toast } from "react-hot-toast";
import { formatErrorMessage } from "@/utils/errorHandler";

interface NavbarSafeToastProps {
  message: string;
  type: "success" | "error" | "info";
  duration?: number;
  style?: React.CSSProperties;
}

interface NavbarSafeErrorToastProps {
  error: any;
  duration?: number;
  style?: React.CSSProperties;
}

/**
 * A custom toast that appears above the navbar
 * @param message The message to display
 * @param type The type of toast (success, error, info)
 * @param duration Optional duration in milliseconds
 * @param style Optional custom styles
 */
export const showNavbarSafeToast = ({
  message,
  type,
  duration = 5000,
  style = {},
}: NavbarSafeToastProps) => {
  const getColor = () => {
    switch (type) {
      case "success":
        return "#4CAF50";
      case "error":
        return "#F44336";
      case "info":
      default:
        return "#2196F3";
    }
  };

  toast.custom(
    (t: Toast) => (
      <div
        className={`${
          t.visible ? "animate-enter" : "animate-leave"
        } max-w-md w-full shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
        style={{
          backgroundColor: "white",
          transform: `translateY(${t.visible ? "0" : "100%"})`,
          transition: "transform 0.3s ease-in-out",
        }}
      >
        <div className="flex-1 w-0 p-4">
          <div className="flex items-start">
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium" style={{ color: getColor() }}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </p>
              <p className="mt-1 text-sm text-gray-500">{message}</p>
            </div>
          </div>
        </div>
        <div className="flex border-l border-gray-200">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-500 focus:outline-none"
          >
            Chiudi
          </button>
        </div>
      </div>
    ),
    {
      position: "bottom-center",
      duration: duration,
      style: {
        bottom: "80px", // Position above the navbar
        zIndex: 20000, // Higher than navbar's z-index
        ...style,
      },
    },
  );
};

/**
 * A custom toast for displaying structured error messages above the navbar
 * @param error The error object from the API
 * @param duration Optional duration in milliseconds
 * @param style Optional custom styles
 */
export const showNavbarSafeErrorToast = ({
  error,
  duration = 5000,
  style = {},
}: NavbarSafeErrorToastProps) => {
  // Format the error message
  const errorMessage = formatErrorMessage(error);

  // Split message by newlines to handle multi-line error messages
  const messageLines = errorMessage.split("\n");

  toast.custom(
    (t: Toast) => (
      <div
        className={`${
          t.visible ? "animate-enter" : "animate-leave"
        } max-w-md w-full shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
        style={{
          backgroundColor: "white",
          transform: `translateY(${t.visible ? "0" : "100%"})`,
          transition: "transform 0.3s ease-in-out",
        }}
      >
        <div className="flex-1 w-0 p-4">
          <div className="flex items-start">
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-red-600">Errore</p>
              <div className="mt-1 text-sm text-gray-700">
                {messageLines.length === 1 ? (
                  <p>{errorMessage}</p>
                ) : (
                  <ul className="list-disc pl-4 space-y-1">
                    {messageLines.map((line, index) => (
                      <li key={index}>{line}</li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex border-l border-gray-200">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-500 focus:outline-none"
          >
            Chiudi
          </button>
        </div>
      </div>
    ),
    {
      position: "bottom-center",
      duration: duration,
      style: {
        bottom: "80px", // Position above the navbar
        zIndex: 20000, // Higher than navbar's z-index
        ...style,
      },
    },
  );
};
