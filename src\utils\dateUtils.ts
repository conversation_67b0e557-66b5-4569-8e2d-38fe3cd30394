/**
 * Utility functions for date operations
 */

/**
 * Checks if two dates are the same day (ignoring time)
 * @param date1 First date to compare
 * @param date2 Second date to compare
 * @returns boolean indicating if the dates are the same day
 */
export const isSameDay = (date1: Date, date2: Date): boolean => {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
};

/**
 * Checks if a date is today
 * @param date Date to check
 * @returns boolean indicating if the date is today
 */
export const isToday = (date: Date): boolean => {
  return isSameDay(date, new Date());
};

/**
 * Formats a date as YYYY-MM-DD
 * @param date Date to format
 * @returns Formatted date string
 */
export const formatDateYYYYMMDD = (date: Date): string => {
  return date.toISOString().split("T")[0];
};

/**
 * Creates a date range between start and end dates
 * @param startDate Start date of the range
 * @param endDate End date of the range
 * @returns Array of dates in the range (inclusive)
 */
export const getDateRange = (startDate: Date, endDate: Date): Date[] => {
  const dates: Date[] = [];
  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
};

/**
 * Gets the first day of the month for a given date
 * @param date Date to get the first day of the month for
 * @returns Date object representing the first day of the month
 */
export const getFirstDayOfMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth(), 1);
};

/**
 * Gets the last day of the month for a given date
 * @param date Date to get the last day of the month for
 * @returns Date object representing the last day of the month
 */
export const getLastDayOfMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
};

/**
 * Calculates the start and end dates of the current month
 * @param date Date in the month to calculate for
 * @returns Object with start and end dates as YYYY-MM-DD strings
 */
export const getMonthDateRange = (
  date: Date,
): { start: string; end: string } => {
  const start = getFirstDayOfMonth(date);
  const end = getLastDayOfMonth(date);

  return {
    start: formatDateYYYYMMDD(start),
    end: formatDateYYYYMMDD(end),
  };
};
