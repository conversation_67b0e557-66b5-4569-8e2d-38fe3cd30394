// Chekin Webhook API Service
import { apiClient } from './apiClient';

// Simple cache to prevent duplicate requests
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class ChekinApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_CACHE_TIME = 30000; // 30 seconds

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_CACHE_TIME): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  // Check if a request is in progress to prevent duplicate calls
  private inProgressRequests = new Set<string>();
  
  async getOrFetch<T>(key: string, fetcher: () => Promise<T>, ttl?: number): Promise<T> {
    // Check cache first
    const cached = this.get<T>(key);
    if (cached) {
      console.log(`Cache hit for ${key}`);
      return cached;
    }

    // Check if request is already in progress
    if (this.inProgressRequests.has(key)) {
      console.log(`Request already in progress for ${key}, waiting...`);
      // Wait a bit and try cache again
      await new Promise(resolve => setTimeout(resolve, 100));
      const retryCache = this.get<T>(key);
      if (retryCache) return retryCache;
    }

    // Mark request as in progress
    this.inProgressRequests.add(key);
    
    try {
      console.log(`Fetching fresh data for ${key}`);
      const data = await fetcher();
      this.set(key, data, ttl);
      return data;
    } finally {
      this.inProgressRequests.delete(key);
    }
  }
}

const cache = new ChekinApiCache();

export interface ChekinStatusSummary {
  total_events: number;
  pending_events: number;
  processed_events: number;
  failed_events: number;
  guest_events: number;
  reservation_events: number;
  police_events: number;
  stat_events: number;
  latest_event: {
    id: string;
    webhook_id: string;
    event_type: string;
    property_name: string;
    status: string;
    created_at: string;
  };
}

export interface ChekinPropertyEvent {
  property_id: string;
  property_name: string;
  total_events: number;
  latest_guest_event?: {
    id: string;
    webhook_id: string;
    event_type: string;
    status: string;
    created_at: string;
  };
  latest_police_event?: {
    id: string;
    webhook_id: string;
    event_type: string;
    status: string;
    created_at: string;
  };
  latest_stat_event?: {
    id: string;
    webhook_id: string;
    event_type: string;
    status: string;
    created_at: string;
  };
  has_active_guest: boolean;
  police_connected: boolean;
  stat_connected: boolean;
  police_registration_status: 'pending' | 'complete' | 'error';
  stat_registration_status: 'pending' | 'complete' | 'error';
}

export interface ChekinBookingEvent {
  booking_id: string;
  guest_name: string;
  checkin_date: string;
  checkout_date: string;
  total_events: number;
  guest_created: boolean;
  guest_creation_date?: string;
  latest_status_update?: string;
  registration_status: 'pending' | 'partial' | 'complete' | 'error';
  events: Array<{
    id: string;
    webhook_id: string;
    event_type: string;
    status: string;
    created_at: string;
  }>;
}



export const chekinApi = {
  // Get status summary
  async getStatusSummary(): Promise<ChekinStatusSummary> {
    return cache.getOrFetch(
      'status-summary',
      async () => {
        try {
          const response = await apiClient.get('/integrations/chekin/webhooks/events/status_summary/');
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();
          console.log('Chekin status summary:', data);
          return data;
        } catch (error) {
          console.error('Error fetching Chekin status summary:', error);
          // Return mock data as fallback
          return {
            total_events: 0,
            pending_events: 0,
            processed_events: 0,
            failed_events: 0,
            guest_events: 0,
            reservation_events: 0,
            police_events: 0,
            stat_events: 0,
            latest_event: {
              id: '',
              webhook_id: '',
              event_type: 'unknown',
              property_name: '',
              status: 'pending',
              created_at: new Date().toISOString()
            }
          };
        }
      },
      20000 // 20 second cache for status summary
    );
  },

  // Get events by property
  async getEventsByProperty(propertyId?: string): Promise<ChekinPropertyEvent[]> {
    const cacheKey = `property-events-${propertyId || 'all'}`;
    return cache.getOrFetch(
      cacheKey,
      async () => {
        const endpoint = propertyId 
          ? `/integrations/chekin/webhooks/events/by_property/?property_id=${propertyId}`
          : `/integrations/chekin/webhooks/events/by_property/`;
        
        const response = await apiClient.get(endpoint);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Chekin events by property:', data);
        return data;
      },
      15000 // 15 second cache for property events
    );
  },

  // Get events by booking
  async getEventsByBooking(bookingId?: string): Promise<ChekinBookingEvent[]> {
    const cacheKey = `booking-events-${bookingId || 'all'}`;
    return cache.getOrFetch(
      cacheKey,
      async () => {
        const endpoint = bookingId 
          ? `/integrations/chekin/webhooks/events/by_booking/?booking_id=${bookingId}`
          : `/integrations/chekin/webhooks/events/by_booking/`;
        
        const response = await apiClient.get(endpoint);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Chekin events by booking:', data);
        return data;
      },
      15000 // 15 second cache for booking events
    );
  },

  // Helper to determine status from event type
  getIconStatusFromEventType(eventType: string): string {
    const eventTypeMap: Record<string, string> = {
      'Guest.created': 'guest_created',
      'Reservation.updated': 'reservation_updated',
      'PoliceAccount.connected': 'police_connected',
      'PoliceAccount.disconnected': 'police_disconnected',
      'PoliceRegistration.complete': 'police_complete',
      'PoliceRegistration.error': 'police_error',
      'StatAccount.connected': 'stat_connected',
      'StatAccount.disconnected': 'stat_disconnected',
      'StatRegistration.complete': 'stat_complete',
      'StatRegistration.error': 'stat_error'
    };
    
    return eventTypeMap[eventType] || 'unknown';
  },

  // Helper to get overall status variant for StatusBadge
  getStatusVariant(status: string, eventType?: string): 'default' | 'yellow' | 'green' | 'black' | 'red' {
    if (status === 'failed' || eventType?.includes('error')) return 'red';
    if (status === 'pending') return 'yellow';
    if (status === 'processed' || eventType?.includes('complete')) return 'green';
    if (eventType?.includes('disconnected')) return 'black';
    return 'default';
  },

  // Cache management methods
  clearCache(): void {
    cache.clear();
    console.log('Chekin API cache cleared');
  },

  // Force refresh by clearing cache and fetching new data
  async forceRefresh(propertyId?: string, bookingId?: string): Promise<{
    statusSummary: ChekinStatusSummary;
    propertyEvents?: ChekinPropertyEvent[];
    bookingEvents?: ChekinBookingEvent[];
  }> {
    this.clearCache();
    
    const result: any = {
      statusSummary: await this.getStatusSummary()
    };

    if (propertyId) {
      result.propertyEvents = await this.getEventsByProperty(propertyId);
    }

    if (bookingId) {
      result.bookingEvents = await this.getEventsByBooking(bookingId);
    }

    return result;
  }
};