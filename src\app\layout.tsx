import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/components/_context/AuthContext";
import { ReviewsProvider } from "@/components/_context/ReviewsContext";
const inter = Inter({ subsets: ["latin"] });

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: process.env.NEXT_PUBLIC_PLATFORM_NAME,
  description: "La piattaforma per gestire in maniera facile le tue proprietà",
  other: {
    // Add FedCM support
    "google-signin-client_id": process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || "",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthProvider>
          <ReviewsProvider>
            <Toaster
              position="bottom-center"
              reverseOrder={false}
              containerStyle={{
                bottom: 80, // Position above the navbar
                zIndex: 20000, // Higher than navbar's z-index (10000)
              }}
              toastOptions={{
                duration: 5000,
                style: {
                  width: "100%",
                  height: "60px",
                  background: "#FEFEFE",
                  color: "#000",
                  boxShadow: "0px 0px 10px rgba(0, 0, 0, 0.1)",
                  fontFamily:
                    'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
                  zIndex: 20000, // Ensure toast is above navbar
                },
              }}
            />
            {children}
          </ReviewsProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
