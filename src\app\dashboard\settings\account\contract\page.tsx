"use client";

import React, { useState, useContext, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  Shield,
  PlusCircle,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { useContract } from "@/hooks/useContract";
import { UserContext } from "@/components/_context/UserContext";
import ContractContent from "@/components/contract/ContractContent";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import Loader1 from "@/components/loaders/Loader1";

export default function ContractPage() {
  const router = useRouter();
  const {
    hasBillingProfile,
    isCustomer,
    hasContract,
    fetchProfile,
    profileLoaded,
  } = useContext(UserContext);
  const { downloadContract, generateContract, statusSummary } = useContract();
  const [isGenerating, setIsGenerating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [initialLoadStarted, setInitialLoadStarted] = useState(false);

  // Fetch data only once when component mounts
  useEffect(() => {
    if (!initialLoadStarted) {
      setInitialLoadStarted(true);
      fetchProfile();
    }
  }, [fetchProfile, initialLoadStarted]);

  // Show loading state while initial data is being fetched
  if (!profileLoaded) {
    return (
      <MobilePageStart>
        <HeaderPage
          title="Contratto di Partnership"
          actionLeftIcon={() => {
            router.back();
          }}
          actionRightIcon={() => {
            router.push(`/dashboard`);
          }}
        />
        <div className="mt-4 px-4 h-full w-full flex flex-col items-center justify-center">
          <Loader1 />
          <p className="text-gray-600 mt-4">
            Caricamento informazioni contratto...
          </p>
        </div>
      </MobilePageStart>
    );
  }

  const canGenerateContract = hasBillingProfile && !hasContract;

  const handleGenerateContract = async () => {
    if (!hasBillingProfile) {
      toast.error("Completa prima il tuo profilo di fatturazione");
      return;
    }
    setIsGenerating(true);
    try {
      const success = await generateContract();
      if (success) {
        toast.success("Contratto disponibile");
      } else {
        toast.error("Impossibile generare il contratto");
      }
    } catch (e) {
      toast.error("Errore durante la generazione del contratto");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadContract = async () => {
    setIsDownloading(true);
    try {
      await downloadContract();
    } catch (error) {
      toast.error("Errore durante il download del contratto");
    } finally {
      setIsDownloading(false);
    }
  };

  const getStatusIcon = () => {
    if (hasContract && hasBillingProfile)
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (hasBillingProfile && !hasContract)
      return <FileText className="h-5 w-5 text-blue-500" />;
    return <AlertCircle className="h-5 w-5 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (hasContract && hasBillingProfile) return "Contratto disponibile";
    if (!hasBillingProfile) return "Completa il profilo di fatturazione";
    if (!hasContract) return "Nessun contratto disponibile";
    return "Caricamento stato contratto...";
  };

  const isContractAvailable = hasContract && hasBillingProfile;
  const showContractGeneration = !isContractAvailable;

  return (
    <MobilePageStart>
      <HeaderPage
        title="Contratto di Partnership"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
        {/* Status Banner */}
        <div
          className={`border rounded-lg p-4 ${
            isContractAvailable
              ? "bg-green-50 border-green-200"
              : hasBillingProfile
                ? "bg-blue-50 border-blue-200"
                : "bg-yellow-50 border-yellow-200"
          }`}
        >
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <h3 className="font-semibold text-gray-900">{getStatusText()}</h3>
              <p className="text-sm text-gray-600">{statusSummary.message}</p>
            </div>
          </div>
        </div>

        {/* Contract Content */}
        <ContractContent />

        {/* Generation and Actions */}
        {showContractGeneration && (
          <div className="bg-white border border-gray-200 rounded-lg p-6 space-y-6">
            <div className="border-t border-gray-200 pt-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <span>Generazione Contratto</span>
              </h3>
              {!hasBillingProfile && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800">
                        Profilo incompleto
                      </h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        Per generare il contratto è necessario completare il
                        profilo di fatturazione.
                      </p>
                      <button
                        onClick={() =>
                          router.push("/dashboard/settings/account")
                        }
                        className="text-sm text-yellow-800 hover:text-yellow-900 underline mt-2 inline-block"
                      >
                        Completa il profilo di fatturazione →
                      </button>
                    </div>
                  </div>
                </div>
              )}
              {hasBillingProfile && (
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Premi il pulsante qui sotto per generare il tuo contratto.
                    Dopo la generazione potrai scaricare il PDF del contratto.
                  </p>
                  <button
                    onClick={handleGenerateContract}
                    disabled={!canGenerateContract || isGenerating}
                    className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors font-medium"
                  >
                    <PlusCircle className="h-5 w-5" />
                    <span>
                      {isGenerating ? "Generazione..." : "Genera contratto"}
                    </span>
                  </button>
                  <p className="text-xs text-gray-500 text-center">
                    Dopo la generazione potrai scaricare il PDF del contratto.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Download Action for Available Contract */}
        {isContractAvailable && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="font-semibold text-gray-900 mb-4 flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span>Contratto disponibile</span>
            </h3>
            <p className="text-gray-600 mb-4">
              Il tuo contratto è stato generato e puoi scaricare in qualsiasi
              momento il documento PDF contenente i dettagli dell'accordo di
              partnership con Heibooky.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleDownloadContract}
                disabled={isDownloading}
                className="flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 disabled:cursor-not-allowed text-white px-6 py-3 rounded-lg transition-colors font-medium"
              >
                <Download className="h-4 w-4" />
                <span>
                  {isDownloading
                    ? "Download in corso..."
                    : "Scarica PDF contratto"}
                </span>
              </button>
            </div>
          </div>
        )}

        {/* Contract Details for Existing Contracts */}
        {hasContract && (
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="font-semibold text-gray-900 mb-4">
              Dettagli contratto
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Stato:</span>
                <span
                  className={`ml-2 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800`}
                >
                  Disponibile
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Tipo:</span>
                <span className="ml-2 text-gray-600">
                  Contratto di Partnership
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Back to Settings */}
        <div className="flex justify-center pb-8">
          <button
            onClick={() => router.push("/dashboard/settings/account")}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Torna alle impostazioni account</span>
          </button>
        </div>
      </div>
    </MobilePageStart>
  );
}
