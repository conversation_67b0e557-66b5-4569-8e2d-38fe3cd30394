"use client";
import React, { useCallback } from "react";
import {
  Filter,
  X,
  Check,
  Clock,
  Sparkles,
  Edit,
  Ban,
  Calendar,
} from "lucide-react";
import { AnimatePresence, motion } from "framer-motion";
import type { BookingFilters } from "@/types/booking";
import { statusConfig, typeConfig } from "@/types/booking";
import InputSelectLabel from "@/components/inputs/inputSelectLabel";
import DateInput from "@/components/inputs/DateInput";
import { DATE_PRESETS } from "@/utils/dateValidation";

interface BookingFiltersComponentProps {
  filters: BookingFilters;
  activeFilters: BookingFilters;
  onFilterChange: (key: keyof BookingFilters, value: any) => void;
  onClearFilter: (key: keyof BookingFilters) => void;
  onApplyFilters: () => void;
  onClearAllFilters: () => void;
  isOpen: boolean;
  onToggle: () => void;
  hasActiveFilters: boolean;
  isLoading?: boolean;
  properties?: Array<{ id: string; name: string }>;
}

const BookingFiltersComponent: React.FC<BookingFiltersComponentProps> = ({
  filters,
  activeFilters,
  onFilterChange,
  onClearFilter,
  onApplyFilters,
  onClearAllFilters,
  isOpen,
  onToggle,
  hasActiveFilters,
  isLoading = false,
  properties = [],
}) => {
  const hasUnappliedChanges =
    JSON.stringify(filters) !== JSON.stringify(activeFilters);

  const appliedFilterCount = Object.entries(activeFilters).filter(
    ([_, v]) => !!v,
  ).length;

  const handleApply = useCallback(() => {
    if (hasUnappliedChanges) {
      onApplyFilters();
    }
    // Always close after attempting apply
    onToggle();
  }, [hasUnappliedChanges, onApplyFilters, onToggle]);

  const handleClearAll = useCallback(() => {
    onClearAllFilters();
  }, [onClearAllFilters]);

  return (
    <div className="relative flex flex-col items-center">
      {/* Filter Toggle Button */}
      <button
        onClick={onToggle}
        className={`group relative flex items-center gap-1.5 pl-3 pr-4 py-1.5 rounded-full font-semibold transition-colors shadow-sm border text-xs
            ${
              hasActiveFilters
                ? "bg-[#133157] text-white border-[#133157]"
                : "bg-white text-[#133157] border-gray-200 hover:border-[#133157]/60"
            }
            ${isLoading ? "opacity-50 cursor-not-allowed" : "active:scale-95"}
          `}
        style={{ minHeight: 32, height: 32 }}
        disabled={isLoading}
        aria-label="Apri filtri prenotazioni"
      >
        <div
          className={`flex items-center justify-center w-6 h-6 rounded-full transition-colors
            ${hasActiveFilters ? "bg-white/20" : "bg-[#133157]/5 group-hover:bg-[#133157]/10"}`}
        >
          <Filter className="w-3 h-3" />
        </div>
        <span className="text-xs tracking-wide">Filtri</span>
        {appliedFilterCount > 0 && (
          <span className="ml-1 text-[10px] font-medium px-1.5 py-0.5 rounded-full bg-white/20 backdrop-blur-sm">
            {appliedFilterCount}
          </span>
        )}
        {hasUnappliedChanges && (
          <span className="absolute -top-1 -right-1 w-2.5 h-2.5 rounded-full bg-amber-400 animate-pulse shadow ring-2 ring-white" />
        )}
      </button>

      {/* Active Filters Preview */}
      {hasActiveFilters && !isOpen && (
        <div className="mt-1 w-full flex flex-col items-center space-y-1">
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value) return null;
            let label = value;
            let config: any = {};
            let emoji = "🔍";

            if (
              key === "status" &&
              statusConfig[value as keyof typeof statusConfig]
            ) {
              config = statusConfig[value as keyof typeof statusConfig];
              label = config.label;
              emoji =
                value === "new"
                  ? "✨"
                  : value === "cancelled"
                    ? "❌"
                    : value === "completed"
                      ? "✅"
                      : "⏳";
            } else if (
              key === "type" &&
              typeConfig[value as keyof typeof typeConfig]
            ) {
              config = typeConfig[value as keyof typeof typeConfig];
              label = config.label;
              emoji = value === "manual" ? "✋" : "🌐";
            } else if (key === "property" && properties.length > 0) {
              const property = properties.find((p) => p.id === value);
              label = property ? property.name : value;
              config = { color: "#6B7280", bgColor: "#F3F4F6" };
              emoji = "🏨";
            } else if (key === "start_date") {
              const date = new Date(value);
              label = `Da ${date.toLocaleDateString("it-IT")}`;
              config = { color: "#059669", bgColor: "#D1FAE5" };
              emoji = "📅";
            } else if (key === "end_date") {
              const date = new Date(value);
              label = `Fino ${date.toLocaleDateString("it-IT")}`;
              config = { color: "#DC2626", bgColor: "#FEE2E2" };
              emoji = "📅";
            }

            return (
              <div
                key={key}
                className="flex items-center justify-center gap-1 px-3 py-1 rounded-full text-[10px] font-medium border shadow-sm backdrop-blur-sm"
                style={{
                  backgroundColor: config.bgColor || "#F3F4F6",
                  color: config.darkColor || config.color || "#374151",
                  borderColor: (config.color || "#D1D5DB") + "55",
                  minWidth: "110px",
                }}
              >
                <span className="leading-none">{emoji}</span>
                <span className="truncate max-w-[80px] text-center">
                  {label}
                </span>
              </div>
            );
          })}
        </div>
      )}

      {/* Filter Panel */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              key="backdrop"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/40 backdrop-blur-[1px] z-[998]"
              onClick={onToggle}
            />

            {/* Bottom Sheet */}
            <motion.div
              key="sheet"
              initial={{ y: "100%", opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: "100%", opacity: 0 }}
              transition={{ type: "spring", stiffness: 280, damping: 30 }}
              className="fixed inset-x-0 z-[999]"
              style={{
                bottom: 70,
                maxHeight: "calc(100dvh - 6px)",
                pointerEvents: "none",
              }}
              aria-modal="true"
              role="dialog"
            >
              <div
                className="mx-auto w-full max-w-md rounded-t-3xl bg-white shadow-2xl border-t border-gray-100 relative flex flex-col"
                style={{
                  height: "100%",
                  maxHeight: "inherit",
                  pointerEvents: "auto",
                }}
              >
                {/* Drag handle */}
                <div className="pt-3 pb-1 flex justify-center">
                  <div className="h-1.5 w-14 rounded-full bg-gray-300" />
                </div>
                {/* Header */}
                <div className="px-5 flex items-start justify-between pb-3">
                  <div>
                    <h3 className="text-lg font-semibold text-[#133157] flex items-center gap-2">
                      Filtri prenotazioni
                      {appliedFilterCount > 0 && (
                        <span className="text-xs font-medium text-white bg-[#133157] px-2 py-0.5 rounded-full">
                          {appliedFilterCount}
                        </span>
                      )}
                    </h3>
                    <p className="text-[12px] text-gray-500 mt-0.5">
                      Affina i risultati con pochi tap
                    </p>
                  </div>
                  <button
                    onClick={onToggle}
                    className="p-2 hover:bg-gray-100 rounded-full text-gray-500 transition-colors"
                    aria-label="Chiudi filtri"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                {/* Content Scroll Area */}
                {/* Increase available scroll height since we lifted the sheet above the navbar */}
                <div
                  className="px-5 pb-40 overflow-y-auto space-y-6"
                  data-testid="filters-scroll"
                  style={{ maxHeight: "calc(100dvh - 200px)" }}
                >
                  {/* Status Filter */}
                  <div>
                    <h4 className="text-xs font-semibold tracking-wide text-gray-600 uppercase mb-2">
                      Stato Prenotazione
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(statusConfig).map(([key, config]) => {
                        const isSelected = filters.status === key;
                        return (
                          <button
                            key={key}
                            onClick={() =>
                              onFilterChange("status", isSelected ? "" : key)
                            }
                            className={`group relative flex items-center gap-2 px-3 py-2 rounded-lg border text-left transition-all text-sm
                              ${isSelected ? "border-transparent ring-2 ring-offset-0 ring-[#133157] shadow-sm" : "border-gray-200 hover:border-[#133157]/40"}`}
                            style={{
                              backgroundColor: isSelected
                                ? config.bgColor
                                : "white",
                              color: isSelected
                                ? config.darkColor
                                : config.color,
                            }}
                          >
                            <div
                              className={`w-8 h-8 flex items-center justify-center rounded-md text-base ${isSelected ? "bg-white/80" : "bg-gray-100"}`}
                            >
                              {key === "new" && (
                                <Sparkles className="w-4 h-4" />
                              )}
                              {key === "modified" && (
                                <Edit className="w-4 h-4" />
                              )}
                              {key === "request" && (
                                <Clock className="w-4 h-4" />
                              )}
                              {key === "cancelled" && (
                                <Ban className="w-4 h-4" />
                              )}
                              {key === "completed" && (
                                <Check className="w-4 h-4" />
                              )}
                            </div>
                            <span className="font-medium truncate">
                              {config.label}
                            </span>
                            {isSelected && (
                              <Check className="w-4 h-4 absolute top-1 right-1 text-[#133157]" />
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Type Filter */}
                  <div>
                    <h4 className="text-xs font-semibold tracking-wide text-gray-600 uppercase mb-2">
                      Tipo
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(typeConfig).map(([key, config]) => {
                        const isSelected = filters.type === key;
                        return (
                          <button
                            key={key}
                            onClick={() =>
                              onFilterChange("type", isSelected ? "" : key)
                            }
                            className={`relative flex items-center gap-2 px-3 py-2 rounded-lg border text-left transition-all text-sm
                              ${isSelected ? "border-transparent ring-2 ring-[#133157] shadow-sm" : "border-gray-200 hover:border-[#133157]/40"}`}
                            style={{
                              backgroundColor: isSelected
                                ? config.bgColor
                                : "white",
                              color: isSelected
                                ? config.darkColor
                                : config.color,
                            }}
                          >
                            <div
                              className={`w-8 h-8 flex items-center justify-center rounded-md text-base ${isSelected ? "bg-white/80" : "bg-gray-100"}`}
                            >
                              {key === "manual" ? "✋" : "🌐"}
                            </div>
                            <div className="flex flex-col flex-1 min-w-0">
                              <span className="font-medium truncate">
                                {config.label}
                              </span>
                              <span className="text-[11px] text-gray-500 truncate">
                                {key === "manual"
                                  ? "Inserite manualmente"
                                  : "Da canali OTA"}
                              </span>
                            </div>
                            {isSelected && (
                              <Check className="w-4 h-4 absolute top-1 right-1 text-[#133157]" />
                            )}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Date Range Filter */}
                  <div>
                    <h4 className="text-xs font-semibold tracking-wide text-gray-600 uppercase mb-2 flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Periodo di Soggiorno
                    </h4>

                    {/* Quick Date Presets */}
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-2">
                        {[
                          { label: "Oggi", preset: DATE_PRESETS.today },
                          {
                            label: "Questa sett.",
                            preset: DATE_PRESETS.thisWeek,
                          },
                          {
                            label: "Questo mese",
                            preset: DATE_PRESETS.thisMonth,
                          },
                          {
                            label: "Prossimi 30gg",
                            preset: DATE_PRESETS.next30Days,
                          },
                        ].map((item) => (
                          <button
                            key={item.label}
                            onClick={() => {
                              const { start_date, end_date } = item.preset();
                              onFilterChange("start_date", start_date);
                              onFilterChange("end_date", end_date);
                            }}
                            className="px-3 py-2 text-xs font-medium border border-gray-200 rounded-lg bg-white text-[#133157] hover:bg-[#133157]/5 hover:border-[#133157]/40 transition-colors"
                          >
                            {item.label}
                          </button>
                        ))}
                      </div>

                      {/* Clear dates button when dates are set */}
                      {(filters.start_date || filters.end_date) && (
                        <button
                          onClick={() => {
                            onFilterChange("start_date", "");
                            onFilterChange("end_date", "");
                          }}
                          className="w-full px-3 py-2 text-xs font-medium border border-red-200 rounded-lg bg-red-50 text-red-600 hover:bg-red-100 hover:border-red-300 transition-colors flex items-center justify-center gap-2"
                        >
                          <X className="w-3 h-3" />
                          Cancella date
                        </button>
                      )}
                    </div>

                    <div className="space-y-4 mt-4">
                      <DateInput
                        label="Data di check-in"
                        value={filters.start_date || ""}
                        onChange={(value) =>
                          onFilterChange("start_date", value)
                        }
                        placeholder="Seleziona data inizio"
                        max={filters.end_date || undefined}
                        className="w-full"
                      />
                      <DateInput
                        label="Data di check-out"
                        value={filters.end_date || ""}
                        onChange={(value) => onFilterChange("end_date", value)}
                        placeholder="Seleziona data fine"
                        min={filters.start_date || undefined}
                        className="w-full"
                      />
                      {(filters.start_date || filters.end_date) && (
                        <div className="flex items-center justify-between pt-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="text-xs text-blue-700 flex items-center gap-1 font-medium">
                            <Calendar className="w-3 h-3" />
                            <span>
                              {filters.start_date && filters.end_date
                                ? `Dal ${new Date(filters.start_date).toLocaleDateString("it-IT")} al ${new Date(filters.end_date).toLocaleDateString("it-IT")}`
                                : filters.start_date
                                  ? `Da ${new Date(filters.start_date).toLocaleDateString("it-IT")}`
                                  : filters.end_date
                                    ? `Fino al ${new Date(filters.end_date).toLocaleDateString("it-IT")}`
                                    : ""}
                            </span>
                          </div>
                          <button
                            onClick={() => {
                              onFilterChange("start_date", "");
                              onFilterChange("end_date", "");
                            }}
                            className="text-xs text-blue-600 hover:text-blue-800 font-medium flex items-center gap-1 hover:bg-blue-100 px-2 py-1 rounded transition-colors"
                          >
                            <X className="w-3 h-3" />
                            Cancella
                          </button>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Property Filter */}
                  {properties.length > 0 && (
                    <div>
                      <h4 className="text-xs font-semibold tracking-wide text-gray-600 uppercase mb-2">
                        Proprietà
                      </h4>
                      <InputSelectLabel
                        placeholder="Tutte le proprietà"
                        label="Tutte le proprietà"
                        value={(() => {
                          if (!filters.property) return "";
                          const found = properties.find(
                            (p) => p.id === filters.property,
                          );
                          return found ? found.name : "";
                        })()}
                        onSelect={(val) => {
                          // Map selected label back to property id
                          if (!val) return onFilterChange("property", "");
                          const found = properties.find((p) => p.name === val);
                          onFilterChange("property", found ? found.id : "");
                        }}
                        options={[
                          { value: "", label: "Tutte le proprietà" },
                          ...properties.map((p) => ({
                            value: p.name,
                            label: p.name,
                          })),
                        ]}
                        styleContainer={{ marginBottom: 0 }}
                        style={{ borderRadius: "12px", padding: "0 14px" }}
                      />
                    </div>
                  )}
                </div>

                {/* Sticky Action Bar */}
                <div className="absolute inset-x-0 bottom-0 w-full px-4 pb-[calc(env(safe-area-inset-bottom,0px)+8px)] pt-3 bg-gradient-to-t from-white via-white to-white/90 border-t border-gray-200 flex gap-3 z-10">
                  {hasActiveFilters && (
                    <button
                      onClick={handleClearAll}
                      className="px-4 py-2.5 rounded-lg text-sm font-medium border border-gray-300 text-gray-600 hover:bg-gray-100 flex-1"
                      type="button"
                    >
                      Reset
                    </button>
                  )}
                  <button
                    onClick={handleApply}
                    className={`flex-1 px-4 py-2.5 rounded-lg text-sm font-semibold shadow-sm text-white transition-colors
                      ${hasUnappliedChanges ? "bg-[#133157] hover:bg-[#0f2745]" : "bg-[#133157]/50 cursor-default"}`}
                    type="button"
                  >
                    Applica
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default BookingFiltersComponent;
