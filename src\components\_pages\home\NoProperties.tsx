import Button from "@/components/buttons/Button";
import { PlusIcon } from "@heroicons/react/16/solid";

import { useRouter } from "next/navigation";
import React from "react";

function NoProperties() {
  const router = useRouter();

  return (
    <div
      className="flex flex-col px-3"
      style={{
        minHeight: "calc(100% - 100px)",
        height: "calc(100% - 100px)",
        width: "100%",

        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
    >
      <p>Inizia aggiungendo la tua prima proprietà</p>
      <br />
      <div
        style={{
          width: "300px",
        }}
      >
        <Button
          color="white"
          backgroundColor="var(--blue)"
          text="Aggiungi proprietà"
          icon={<PlusIcon width={17} />}
          fontSize="14px"
          onClick={() => {
            router.push("/dashboard/setup/name");
          }}
        />
      </div>
    </div>
  );
}

export default NoProperties;
