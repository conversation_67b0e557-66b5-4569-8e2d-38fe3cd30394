import { cn } from "@/utils/cn";
import type { LucideIcon } from "lucide-react";

interface StatusBadgeProps {
  icon: LucideIcon;
  variant?: "default" | "yellow" | "green" | "black" | "red";
  size?: "sm" | "md" | "lg";
  className?: string;
}

const variantStyles = {
  default: "border-blue-500 ring-4 ring-blue-500/20",
  yellow: "border-yellow-400 ring-4 ring-yellow-400/30",
  green: "border-green-500 ring-4 ring-green-500/30",
  black: "border-gray-900 ring-4 ring-gray-900/30",
  red: "border-red-500 ring-4 ring-red-500/30",
};

const iconColors = {
  default: "text-blue-600",
  yellow: "text-yellow-600", 
  green: "text-green-600",
  black: "text-gray-900",
  red: "text-red-600",
};

const sizeStyles = {
  sm: "p-2",
  md: "p-4",
  lg: "p-6"
};

const iconSizes = {
  sm: "h-4 w-4",
  md: "h-6 w-6", 
  lg: "h-8 w-8"
};

export function StatusBadge({ 
  icon: Icon, 
  variant = "default", 
  size = "md",
  className 
}: StatusBadgeProps) {
  return (
    <div
      className={cn(
        "relative flex items-center justify-center rounded-full border-4 bg-white transition-all",
        sizeStyles[size],
        variantStyles[variant],
        className,
      )}
    >
      <Icon 
        className={cn(iconSizes[size], iconColors[variant])} 
        strokeWidth={1.5} 
      />
    </div>
  );
}