import React from "react";
import styles from "./input.module.css";
import { AlertTriangle } from "lucide-react";

interface InputControllerProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  maximum?: number;
  minimum?: number;
  error?: string;
  hasError?: boolean;
  onValidation?: (isValid: boolean, errorMessage?: string) => void;
}

function InputCounter({
  label,
  value,
  onChange,
  maximum,
  minimum,
  error,
  hasError,
  onValidation,
}: InputControllerProps) {
  const handleChange = (newValue: number) => {
    onChange(newValue);

    // Trigger validation callback if provided
    if (onValidation) {
      let isValid = true;
      let errorMessage = "";

      if (minimum !== undefined && newValue < minimum) {
        isValid = false;
        errorMessage = `Il valore deve essere almeno ${minimum}`;
      } else if (maximum !== undefined && newValue > maximum) {
        isValid = false;
        errorMessage = `Il valore non può superare ${maximum}`;
      }

      onValidation(isValid, errorMessage);
    }
  };

  const containerStyle =
    hasError || error
      ? {
          borderColor: "#ef4444",
          borderWidth: "2px",
          borderStyle: "solid",
        }
      : {};

  return (
    <div className={styles.inputCounter}>
      <div className="flex items-center gap-2 mb-2">
        <p
          style={{
            fontSize: "14px",
            fontWeight: hasError || error ? "600" : "400",
            color: hasError || error ? "#ef4444" : "inherit",
          }}
        >
          {label}
        </p>
        {(hasError || error) && (
          <AlertTriangle size={16} style={{ color: "#ef4444" }} />
        )}
      </div>

      <div
        className="bg-[white] px-4 py-3 rounded-[20px] w-full flex items-center justify-center gap-4"
        style={containerStyle}
      >
        <button
          onClick={() => {
            if (value <= 0) return;
            if (minimum && value <= minimum) return;
            handleChange(value - 1);
          }}
          style={{
            backgroundColor: "#E5E5E5",
          }}
          disabled={minimum !== undefined && value <= minimum}
        >
          -
        </button>
        <p
          style={{
            color: hasError || error ? "#ef4444" : "inherit",
            fontWeight: hasError || error ? "600" : "400",
          }}
        >
          {value}
        </p>
        <button
          onClick={() => {
            if (maximum && value >= maximum) return;
            handleChange(value + 1);
          }}
          disabled={maximum !== undefined && value >= maximum}
        >
          +
        </button>
      </div>

      {error && (
        <p className="text-red-500 text-xs mt-1 font-medium">{error}</p>
      )}
    </div>
  );
}

export default InputCounter;
