import { apiClient } from "./apiClient";

/**
 * Financial API service for Heibooky Property Management System
 * Handles all financial-related API calls including payments, earnings, and commissions
 */

// Types for financial data
export interface PendingBalance {
  pending_balance: string;
  currency: string;
  calculation_period: string;
  last_updated: string;
}

export interface TotalEarnings {
  total_earnings: string;
  currency: string;
  calculation_type: string;
  last_updated: string;
  total_reservations: number;
}

export interface CommissionBreakdown {
  total_ota_commission: string;
  total_heibooky_commission: string;
  total_payment_fees: string;
  total_vat: string;
  total_taxes: string;
  total_activation_fees: string;
  total_cleaning_costs: string;
  // New optional fields for negative balance tracking
  total_negative_balance_applied?: string;
  total_negative_balance_created?: string;
}

export interface NextPaymentInfo {
  next_payment_date: string;
  days_until_payment: string;
  current_cycle: string;
}

export interface FinancialSummary {
  pending_balance: string;
  total_earnings: string;
  commission_breakdown: CommissionBreakdown;
  next_payment_info: NextPaymentInfo;
  currency: string;
  last_updated: string;
}

export interface PaymentDetails {
  payment_type: string;
  calculation_type: string;
  client_price: number;
  base_price: number;
  ota_commission: number;
  heibooky_commission: number;
  payment_fee_3_5: number;
  payment_charge: number;
  activation_fee: number;
  vat_22: number;
  iva_amount: number;
  substitute_tax_21: number;
  owner_tax: number;
  total_owner: number;
  net_total_for_owner: number;
  total_owner_payout: number;
  // New optional fields for radical pricing changes
  negative_balance_applied?: number;
  negative_balance_created?: number;
  owner_payout_after_negative_recovery?: number;
  activation_fee_tracking: {
    activation_fee_applied: number;
    activation_fee_remaining_before: number;
    activation_fee_remaining_after: number;
    is_activation_fee_recovery: boolean;
  };
  breakdown: {
    client_price: number;
    ota_commission_15_percent: number;
    heibooky_commission_8_percent: number;
    payment_fee_3_5_percent: number;
    vat_22_percent_on_commissions: number;
    substitute_tax_21_percent: number;
    total_deductions: number;
    activation_fee_deducted: number;
    final_owner_payout: number;
    // New optional breakdown details
    negative_balance_recovery_applied?: number;
    owner_payout_after_negative_recovery?: number;
  };
  summary: {
    total_commissions: number;
    total_taxes: number;
    total_fees: number;
    grand_total_deductions: number;
  };
}

export interface Reservation {
  id: string;
  guest_name: string;
  checkout_date: string;
  net_amount: string;
  payment_status: string;
  checkin_date: string;
  total_price: string;
  net_price: string;
  payment_type: string;
  payment_details: PaymentDetails;
  payment_status_display: string;
  next_payment_info: NextPaymentInfo;
}

export interface PaymentStatus {
  payment_in_progress: {
    count: number;
    total_amount: string;
    reservations: Reservation[];
  };
  future_payment: {
    count: number;
    total_amount: string;
    reservations: Reservation[];
  };
}

export interface PaymentCycle {
  current_cycle: {
    start_date: string;
    end_date: string;
    payment_date: string;
    description: string;
  };
  next_cycle: {
    start_date: string;
    end_date: string;
    payment_date: string;
    description: string;
  };
  days_until_next_payment: number;
}

export interface ActivationFeeStatus {
  activation_fee_active: boolean;
  original_fee_amount: string;
  remaining_fee_amount: string;
  total_recovered_amount: string;
  recovery_percentage: number;
  is_fully_recovered: boolean;
  recovery_started_at: string;
  fully_recovered_at: string | null;
  message: string;
}

/**
 * Get pending balance from reservations completed in the last 15 days
 */
export const getPendingBalance = async (
  propertyIds?: string[],
): Promise<PendingBalance> => {
  try {
    const queryParams = propertyIds
      ? `?property_ids=${propertyIds.join(",")}`
      : "";
    const response = await apiClient.get(
      `/booking/financial/pending-balance/${queryParams}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch pending balance: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching pending balance:", error);
    throw error;
  }
};

/**
 * Get total lifetime earnings from the platform
 */
export const getTotalEarnings = async (
  propertyIds?: string[],
): Promise<TotalEarnings> => {
  try {
    const queryParams = propertyIds
      ? `?property_ids=${propertyIds.join(",")}`
      : "";
    const response = await apiClient.get(
      `/booking/financial/total-earnings/${queryParams}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch total earnings: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching total earnings:", error);
    throw error;
  }
};

/**
 * Get comprehensive financial summary including all metrics
 */
export const getFinancialSummary = async (
  propertyIds?: string[],
): Promise<FinancialSummary> => {
  try {
    const queryParams = propertyIds
      ? `?property_ids=${propertyIds.join(",")}`
      : "";
    const response = await apiClient.get(
      `/booking/financial/summary/${queryParams}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch financial summary: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching financial summary:", error);
    throw error;
  }
};

/**
 * Get payment status information for reservations
 */
export const getPaymentStatus = async (
  status?: "payment_in_progress" | "future_payment",
  propertyIds?: string[],
  limit?: number,
): Promise<PaymentStatus> => {
  try {
    const params = new URLSearchParams();
    if (status) params.append("status", status);
    if (propertyIds) params.append("property_ids", propertyIds.join(","));
    if (limit) params.append("limit", limit.toString());

    const queryString = params.toString();
    const response = await apiClient.get(
      `/booking/financial/payment-status/${queryString ? `?${queryString}` : ""}`,
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch payment status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching payment status:", error);
    throw error;
  }
};

/**
 * Get current payment cycle information
 */
export const getPaymentCycleInfo = async (): Promise<PaymentCycle> => {
  try {
    const response = await apiClient.get(
      "/booking/financial/payment-cycle-info/",
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch payment cycle info: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching payment cycle info:", error);
    throw error;
  }
};

/**
 * Get detailed payment information for a specific reservation
 */
export const getReservationPaymentInfo = async (
  reservationId: string,
): Promise<any> => {
  try {
    const response = await apiClient.get(
      `/booking/financial/reservation/${reservationId}/payment-info/`,
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch reservation payment info: ${response.status}`,
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching reservation payment info:", error);
    throw error;
  }
};

/**
 * Get detailed commission breakdown for financial reporting
 */
export const getCommissionBreakdown = async (
  propertyIds?: string[],
  period?: "last_30_days" | "last_90_days" | "ytd" | "all_time",
): Promise<any> => {
  try {
    const params = new URLSearchParams();
    if (propertyIds) params.append("property_ids", propertyIds.join(","));
    if (period) params.append("period", period);

    const queryString = params.toString();
    const response = await apiClient.get(
      `/booking/financial/commission-breakdown/${queryString ? `?${queryString}` : ""}`,
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch commission breakdown: ${response.status}`,
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching commission breakdown:", error);
    throw error;
  }
};

/**
 * Get activation fee status for properties
 */
export const getActivationFeeStatus = async (
  propertyId: string,
): Promise<ActivationFeeStatus> => {
  try {
    const response = await apiClient.get(
      `/booking/financial/activation-fee-status/?property_id=${propertyId}`,
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch activation fee status: ${response.status}`,
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching activation fee status:", error);
    throw error;
  }
};

/**
 * Financial calculator utilities
 */
export const financialUtils = {
  /**
   * Format currency amount with proper decimals and symbol
   */
  formatCurrency: (
    amount: string | number,
    currency: string = "EUR",
  ): string => {
    const numAmount = typeof amount === "string" ? parseFloat(amount) : amount;
    return new Intl.NumberFormat("en-EU", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(numAmount);
  },

  /**
   * Calculate percentage of total
   */
  calculatePercentage: (part: number, total: number): number => {
    if (total === 0) return 0;
    return (part / total) * 100;
  },

  /**
   * Format percentage with proper decimals
   */
  formatPercentage: (percentage: number): string => {
    return `${percentage.toFixed(1)}%`;
  },

  /**
   * Calculate days until payment
   */
  calculateDaysUntilPayment: (paymentDate: string): number => {
    const today = new Date();
    const payment = new Date(paymentDate);
    const diffTime = payment.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  },

  /**
   * Format date for display
   */
  formatDate: (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-EU", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  },

  /**
   * Validate financial calculation
   */
  validateCalculation: (details: PaymentDetails): boolean => {
    const {
      client_price,
      ota_commission,
      heibooky_commission,
      payment_fee_3_5,
      vat_22,
      substitute_tax_21,
      activation_fee,
      total_owner,
    } = details;

    const expectedDeductions =
      ota_commission +
      heibooky_commission +
      payment_fee_3_5 +
      vat_22 +
      substitute_tax_21 +
      activation_fee;
    const expectedOwnerAmount = client_price - expectedDeductions;

    // Allow for small floating point differences
    const difference = Math.abs(expectedOwnerAmount - total_owner);
    return difference < 0.01;
  },
};

// Historical earnings interface
export interface HistoricalEarnings {
  current_period: {
    earnings: string;
    period_start: string;
    period_end: string;
  };
  previous_period: {
    earnings: string;
    period_start: string;
    period_end: string;
  };
  trend: {
    percentage_change: number;
    is_positive: boolean;
    comparison_period: string;
  };
}

/**
 * Get historical earnings comparison for trend calculation
 */
export const getHistoricalEarnings = async (
  propertyIds?: string[],
  period: "last_30_days" | "last_90_days" = "last_30_days",
): Promise<HistoricalEarnings> => {
  try {
    const params = new URLSearchParams();
    if (propertyIds) params.append("property_ids", propertyIds.join(","));
    params.append("period", period);

    const queryString = params.toString();
    const response = await apiClient.get(
      `/booking/financial/historical-earnings/?${queryString}`,
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch historical earnings: ${response.status}`,
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching historical earnings:", error);
    throw error;
  }
};
