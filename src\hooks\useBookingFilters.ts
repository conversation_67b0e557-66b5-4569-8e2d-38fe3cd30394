"use client";
import { useState, useEffect, useCallback } from "react";
import { getAllBookings } from "@/services/api";
import type {
  BookingFilters,
  Booking,
  BookingApiResponse,
} from "@/types/booking";
import toast from "react-hot-toast";

interface UseBookingFiltersReturn {
  bookings: Booking[];
  isLoading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalCount: number;
  pageSize: number;
  filters: BookingFilters;
  activeFilters: BookingFilters;
  setFilter: (key: keyof BookingFilters, value: any) => void;
  clearFilter: (key: keyof BookingFilters) => void;
  clearAllFilters: () => void;
  applyFilters: () => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  refetch: () => void;
  hasActiveFilters: boolean;
}

export const useBookingFilters = (
  initialFilters: BookingFilters = {},
  autoFetch: boolean = true,
): UseBookingFiltersReturn => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [pageSize, setPageSizeState] = useState(10);

  // Separate pending filters from applied filters
  const [filters, setFilters] = useState<BookingFilters>(initialFilters);
  const [activeFilters, setActiveFilters] =
    useState<BookingFilters>(initialFilters);

  const fetchBookings = useCallback(
    async (
      page: number = currentPage,
      size: number = pageSize,
      filtersToApply: BookingFilters = activeFilters,
    ) => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await getAllBookings(page, size, filtersToApply);

        setBookings(response.results);
        setCurrentPage(response.currentPage);
        setTotalPages(response.totalPages);
        setTotalCount(response.count);
        setPageSizeState(response.pageSize);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to fetch bookings";
        setError(errorMessage);
        toast.error("Errore nel caricamento delle prenotazioni");
        console.error("Error fetching bookings:", err);
      } finally {
        setIsLoading(false);
      }
    },
    [currentPage, pageSize, activeFilters],
  );

  const setFilter = useCallback((key: keyof BookingFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  const clearFilter = useCallback((key: keyof BookingFilters) => {
    setFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);

  const clearAllFilters = useCallback(() => {
    setFilters({});
    setActiveFilters({});
    setCurrentPage(1);
  }, []);

  const applyFilters = useCallback(() => {
    setActiveFilters(filters);
    setCurrentPage(1);
  }, [filters]);

  const setPage = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const setPageSize = useCallback((size: number) => {
    setPageSizeState(size);
    setCurrentPage(1); // Reset to first page when changing page size
  }, []);

  const refetch = useCallback(() => {
    fetchBookings(currentPage, pageSize, activeFilters);
  }, [fetchBookings, currentPage, pageSize, activeFilters]);

  // Check if there are any active filters
  const hasActiveFilters = Object.keys(activeFilters).some(
    (key) =>
      activeFilters[key as keyof BookingFilters] !== undefined &&
      activeFilters[key as keyof BookingFilters] !== null &&
      activeFilters[key as keyof BookingFilters] !== "",
  );

  // Auto-fetch on dependency changes
  useEffect(() => {
    if (autoFetch) {
      fetchBookings(currentPage, pageSize, activeFilters);
    }
  }, [currentPage, pageSize, activeFilters, autoFetch, fetchBookings]);

  return {
    bookings,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalCount,
    pageSize,
    filters,
    activeFilters,
    setFilter,
    clearFilter,
    clearAllFilters,
    applyFilters,
    setPage,
    setPageSize,
    refetch,
    hasActiveFilters,
  };
};
