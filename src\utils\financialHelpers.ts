/**
 * Financial utilities and helpers for the Heibooky PMS Financial Dashboard
 * Contains common functions for financial calculations, formatting, and validation
 */

import { PaymentDetails, CommissionBreakdown } from "@/services/financialApi";

/**
 * Extended financial utilities beyond the API service
 */
export const financialHelpers = {
  /**
   * Calculate the effective commission rate for a reservation
   */
  calculateEffectiveCommissionRate: (
    paymentDetails: PaymentDetails,
  ): number => {
    const totalCommissions =
      paymentDetails.ota_commission +
      paymentDetails.heibooky_commission +
      paymentDetails.payment_fee_3_5;
    return (totalCommissions / paymentDetails.client_price) * 100;
  },

  /**
   * Calculate the net profit margin for the owner
   */
  calculateOwnerProfitMargin: (paymentDetails: PaymentDetails): number => {
    return (paymentDetails.total_owner / paymentDetails.client_price) * 100;
  },

  /**
   * Get payment status color theme
   */
  getPaymentStatusTheme: (status: string) => {
    const normalizedStatus = status.toLowerCase();

    switch (normalizedStatus) {
      case "payment_in_progress":
        return {
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          textColor: "text-yellow-800",
          iconColor: "text-yellow-600",
          progressColor: "bg-yellow-500",
        };
      case "future_payment":
        return {
          bgColor: "bg-blue-50",
          borderColor: "border-blue-200",
          textColor: "text-blue-800",
          iconColor: "text-blue-600",
          progressColor: "bg-blue-500",
        };
      case "completed":
        return {
          bgColor: "bg-green-50",
          borderColor: "border-green-200",
          textColor: "text-green-800",
          iconColor: "text-green-600",
          progressColor: "bg-green-500",
        };
      case "cancelled":
        return {
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          textColor: "text-red-800",
          iconColor: "text-red-600",
          progressColor: "bg-red-500",
        };
      default:
        return {
          bgColor: "bg-gray-50",
          borderColor: "border-gray-200",
          textColor: "text-gray-800",
          iconColor: "text-gray-600",
          progressColor: "bg-gray-500",
        };
    }
  },

  /**
   * Format large numbers with appropriate units (K, M, B)
   */
  formatLargeNumber: (num: number): string => {
    if (num >= 1e9) {
      return (num / 1e9).toFixed(1) + "B";
    }
    if (num >= 1e6) {
      return (num / 1e6).toFixed(1) + "M";
    }
    if (num >= 1e3) {
      return (num / 1e3).toFixed(1) + "K";
    }
    return num.toString();
  },

  /**
   * Calculate payment cycle progress
   */
  calculatePaymentCycleProgress: (currentCycle: {
    start_date: string;
    end_date: string;
    payment_date: string;
  }): number => {
    const now = new Date();
    const start = new Date(currentCycle.start_date);
    const end = new Date(currentCycle.end_date);

    if (now < start) return 0;
    if (now > end) return 100;

    const totalDuration = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();

    return Math.round((elapsed / totalDuration) * 100);
  },

  /**
   * Validate commission breakdown totals
   */
  validateCommissionBreakdown: (
    breakdown: CommissionBreakdown,
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    // Check for negative values
    Object.entries(breakdown).forEach(([key, value]) => {
      if (typeof value === "string" && parseFloat(value) < 0) {
        errors.push(`${key} cannot be negative`);
      }
    });

    // Check for reasonable commission percentages (should not exceed 100% of typical booking values)
    const totalCommissions =
      parseFloat(breakdown.total_ota_commission) +
      parseFloat(breakdown.total_heibooky_commission) +
      parseFloat(breakdown.total_payment_fees);

    if (totalCommissions > 500000) {
      // Arbitrary large number check
      errors.push("Total commissions seem unusually high");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Generate financial summary statistics
   */
  generateFinancialStats: (reservations: any[]) => {
    if (!reservations || reservations.length === 0) {
      return {
        averageBookingValue: 0,
        averageCommissionRate: 0,
        averageOwnerPayout: 0,
        totalRevenue: 0,
        totalCommissions: 0,
        reservationCount: 0,
      };
    }

    const stats = reservations.reduce(
      (acc, reservation) => {
        const paymentDetails = reservation.payment_details;
        if (!paymentDetails) return acc;

        const bookingValue = parseFloat(reservation.total_price || "0");
        const ownerPayout = paymentDetails.total_owner || 0;
        const commissions =
          (paymentDetails.ota_commission || 0) +
          (paymentDetails.heibooky_commission || 0) +
          (paymentDetails.payment_fee_3_5 || 0);

        return {
          totalRevenue: acc.totalRevenue + bookingValue,
          totalCommissions: acc.totalCommissions + commissions,
          totalOwnerPayout: acc.totalOwnerPayout + ownerPayout,
          count: acc.count + 1,
        };
      },
      { totalRevenue: 0, totalCommissions: 0, totalOwnerPayout: 0, count: 0 },
    );

    return {
      averageBookingValue:
        stats.count > 0 ? stats.totalRevenue / stats.count : 0,
      averageCommissionRate:
        stats.totalRevenue > 0
          ? (stats.totalCommissions / stats.totalRevenue) * 100
          : 0,
      averageOwnerPayout:
        stats.count > 0 ? stats.totalOwnerPayout / stats.count : 0,
      totalRevenue: stats.totalRevenue,
      totalCommissions: stats.totalCommissions,
      reservationCount: stats.count,
    };
  },

  /**
   * Format date range for display
   */
  formatDateRange: (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const formatOptions: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
    };

    if (start.getFullYear() !== end.getFullYear()) {
      formatOptions.year = "numeric";
    }

    return `${start.toLocaleDateString("en-US", formatOptions)} - ${end.toLocaleDateString("en-US", formatOptions)}`;
  },

  /**
   * Calculate compound annual growth rate (CAGR) for earnings
   */
  calculateCAGR: (
    beginningValue: number,
    endingValue: number,
    numberOfYears: number,
  ): number => {
    if (beginningValue <= 0 || numberOfYears <= 0) return 0;
    return (
      (Math.pow(endingValue / beginningValue, 1 / numberOfYears) - 1) * 100
    );
  },

  /**
   * Determine urgency level for payments
   */
  getPaymentUrgency: (daysUntilPayment: number): "high" | "medium" | "low" => {
    if (daysUntilPayment <= 1) return "high";
    if (daysUntilPayment <= 3) return "medium";
    return "low";
  },

  /**
   * Format payment cycle description
   */
  formatPaymentCycleDescription: (
    startDate: string,
    endDate: string,
  ): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const startDay = start.getDate();
    const endDay = end.getDate();
    const month = start.toLocaleDateString("en-US", { month: "long" });
    const year = start.getFullYear();

    return `${startDay}${getDaySuffix(startDay)}-${endDay}${getDaySuffix(endDay)} ${month} ${year}`;
  },

  /**
   * Estimate next payment amount based on current reservations
   */
  estimateNextPaymentAmount: (
    reservations: any[],
    paymentCycle: any,
  ): number => {
    if (!reservations || !paymentCycle) return 0;

    const cycleStart = new Date(paymentCycle.current_cycle.start_date);
    const cycleEnd = new Date(paymentCycle.current_cycle.end_date);

    return reservations
      .filter((reservation) => {
        const checkoutDate = new Date(reservation.checkout_date);
        return checkoutDate >= cycleStart && checkoutDate <= cycleEnd;
      })
      .reduce((total, reservation) => {
        return total + (reservation.payment_details?.total_owner || 0);
      }, 0);
  },
};

/**
 * Helper function to get day suffix (st, nd, rd, th)
 */
function getDaySuffix(day: number): string {
  if (day >= 11 && day <= 13) return "th";
  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
}

/**
 * Constants for financial calculations
 */
export const FINANCIAL_CONSTANTS = {
  // Commission rates
  OTA_COMMISSION_RATE: 0.15, // 15%
  HEIBOOKY_COMMISSION_RATE: 0.08, // 8%
  PAYMENT_FEE_RATE: 0.035, // 3.5%

  // Tax rates
  VAT_RATE: 0.22, // 22%
  SUBSTITUTE_TAX_RATE: 0.21, // 21%

  // Payment cycle days
  PAYMENT_CYCLE_DAYS: 15,

  // Formatting
  CURRENCY_CODE: "EUR",
  DECIMAL_PLACES: 2,

  // Validation limits
  MAX_REASONABLE_BOOKING_VALUE: 10000, // €10,000
  MIN_BOOKING_VALUE: 1, // €1
  MAX_COMMISSION_RATE: 0.5, // 50%
};

/**
 * Export commonly used validation functions
 */
export const validators = {
  isValidCurrency: (amount: string | number): boolean => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    return (
      !isNaN(num) &&
      num >= 0 &&
      num <= FINANCIAL_CONSTANTS.MAX_REASONABLE_BOOKING_VALUE
    );
  },

  isValidPercentage: (percentage: number): boolean => {
    return !isNaN(percentage) && percentage >= 0 && percentage <= 100;
  },

  isValidCommissionRate: (rate: number): boolean => {
    return (
      !isNaN(rate) &&
      rate >= 0 &&
      rate <= FINANCIAL_CONSTANTS.MAX_COMMISSION_RATE
    );
  },

  isValidDate: (dateString: string): boolean => {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  },
};
