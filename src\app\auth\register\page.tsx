"use client";

import styles from "../login/page.module.css";
import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import Input from "@/components/inputs/input";
import Button from "@/components/buttons/Button";
import InputPhone from "@/components/inputs/inputPhone";
import Link from "next/link";
import { register, deleteToken, viewInvite, teamSignup } from "@/services/api";
import toast from "react-hot-toast";
import AuthLogo from "@/components/auth/AuthLogo";
import GoogleAuthButton from "@/components/buttons/GoogleAuthButton";
import { formatErrorMessage } from "@/utils/errorHandler";
import { showErrorToast } from "@/components/toast/ErrorToast";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Home />
    </Suspense>
  );
}

type ErrorKeys =
  | "name"
  | "surname"
  | "email"
  | "phone"
  | "terms"
  | "password"
  | "confirmPassword";

function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteId = searchParams.get("invite_id");

  useEffect(() => {
    // Load Iubenda script
    const script = document.createElement("script");
    script.src = "https://cdn.iubenda.com/iubenda.js";
    document.body.appendChild(script);

    return () => {
      // Cleanup on unmount
      document.body.removeChild(script);
    };
  }, []);

  // Form States
  const [name, setName] = useState("");
  const [surname, setSurname] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [country, setCountry] = useState("+39");
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Terms acceptance is currently not enforced but kept for future use
  const [,/* acceptedTerms */] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isEmailReadOnly, setIsEmailReadOnly] = useState(false);
  const [teamName, setTeamName] = useState("");

  // Password visibility toggles
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Error States
  const [errors, setErrors] = useState({
    name: "",
    surname: "",
    email: "",
    phone: "",
    terms: "",
    password: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (inviteId) {
      const validateInvite = async () => {
        try {
          const response = await viewInvite(inviteId);
          if (response.status === 200) {
            setEmail(response.data.email);
            setIsEmailReadOnly(true);
            setTeamName(response.data.team_name || "questo team");
          } else {
            // Invalid invite, clear ID from URL
            router.replace("/auth/register");
          }
        } catch (error) {
          router.replace("/auth/register");
        }
      };
      validateInvite();
    }
  }, [inviteId, router]);

  // Clear specific error when field is updated
  const clearError = (field: ErrorKeys) => {
    if (errors[field]) {
      setErrors({ ...errors, [field]: "" });
    }
  };

  // Validation helpers
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  };

  const isValidPhone = (phone: string) => {
    // Ensure phone has at least 6 digits (basic validation)
    return phone.trim().replace(/\D/g, "").length >= 6;
  };

  const validatePassword = (password: string) => {
    return (
      password.length >= 8 &&
      /[A-Z]/.test(password) &&
      /[a-z]/.test(password) &&
      /\d/.test(password) &&
      /[!@#$%^&*]/.test(password)
    );
  };

  const validateForm = () => {
    const newErrors = { ...errors };
    let isValid = true;

    // Name validation
    if (!name.trim()) {
      newErrors.name = "Il nome è obbligatorio";
      isValid = false;
    } else {
      newErrors.name = "";
    }

    // Surname validation
    if (!surname.trim()) {
      newErrors.surname = "Il cognome è obbligatorio";
      isValid = false;
    } else {
      newErrors.surname = "";
    }

    // Email validation
    if (!email.trim()) {
      newErrors.email = "L'email è obbligatoria";
      isValid = false;
    } else if (!isValidEmail(email)) {
      newErrors.email = "Formato email non valido";
      isValid = false;
    } else {
      newErrors.email = "";
    }

    // Phone validation
    if (!phone.trim()) {
      newErrors.phone = "Il telefono è obbligatorio";
      isValid = false;
    } else if (!isValidPhone(phone)) {
      newErrors.phone = "Numero di telefono non valido";
      isValid = false;
    } else {
      newErrors.phone = "";
    }

    if (inviteId) {
      if (!password) {
        newErrors.password = "La password è obbligatoria";
        isValid = false;
      } else if (!validatePassword(password)) {
        newErrors.password =
          "La password deve contenere almeno 8 caratteri, una lettera maiuscola, minuscola, un numero e un carattere speciale";
        isValid = false;
      }

      if (!confirmPassword) {
        newErrors.confirmPassword = "Conferma la password";
        isValid = false;
      } else if (password !== confirmPassword) {
        newErrors.confirmPassword = "Le password non corrispondono";
        isValid = false;
      }
    }

    // Terms validation - uncomment if you want to enforce terms acceptance
    // if (!acceptedTerms) {
    //   newErrors.terms = 'Devi accettare i termini e le condizioni'
    //   isValid = false
    // } else {
    //   newErrors.terms = ''
    // }

    setErrors(newErrors);
    return isValid;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return false;
    }

    try {
      setIsSubmitting(true);
      const fullName = `${name.trim()} ${surname.trim()}`;
      const formattedPhone = `${country} ${phone.trim()}`;

      if (inviteId) {
        // Team signup flow
        const response = await teamSignup(
          inviteId,
          fullName,
          email.trim(),
          formattedPhone,
          password,
        );
        if (response.status === 201) {
          toast.success("Registrazione avvenuta con successo");
          deleteToken();
          router.push("/auth/login");
          return true;
        } else {
          // Handle structured error response
          const errorMessage = formatErrorMessage(response.data || {});
          showErrorToast({ message: errorMessage });
          return false;
        }
      } else {
        // Normal signup flow
        const response = await register(fullName, email.trim(), formattedPhone);
        if (response.user) {
          toast.success("Registrazione avvenuta con successo");
          deleteToken();
          router.push("/auth/verify?email=" + encodeURIComponent(email.trim()));
          return true;
        } else {
          // Handle structured error response
          const errorMessage = formatErrorMessage(response);
          showErrorToast({ message: errorMessage });
          return false;
        }
      }
    } catch (error: any) {
      console.error("Registration error:", error);
      // Handle error from exception
      const errorData = error?.response?.data || error?.message || {};
      const errorMessage = formatErrorMessage(errorData);
      showErrorToast({ message: errorMessage });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission (for Enter key support)
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleRegister();
  };

  // Add password fields to the form when inviteId exists
  const renderPasswordFields = () => {
    if (!inviteId) return null;

    return (
      <>
        <div className="relative">
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              placeholder="Password"
              value={password}
              onChange={(value) => {
                setPassword(value);
                clearError("password");
              }}
              error={errors.password}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none"
              aria-label={
                showPassword ? "Nascondi password" : "Mostra password"
              }
            >
              {showPassword ? <EyeOffIcon size={18} /> : <EyeIcon size={18} />}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-500 text-xs mt-1">{errors.password}</p>
          )}
        </div>

        <div className="relative">
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Conferma Password"
              value={confirmPassword}
              onChange={(value) => {
                setConfirmPassword(value);
                clearError("confirmPassword");
              }}
              error={errors.confirmPassword}
            />
            <button
              type="button"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 focus:outline-none"
              aria-label={
                showConfirmPassword ? "Nascondi password" : "Mostra password"
              }
            >
              {showConfirmPassword ? (
                <EyeOffIcon size={18} />
              ) : (
                <EyeIcon size={18} />
              )}
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="text-red-500 text-xs mt-1">
              {errors.confirmPassword}
            </p>
          )}
        </div>
      </>
    );
  };

  // Card for team invite
  const renderTeamInviteCard = () => {
    if (!inviteId) return null;

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h3 className="text-blue-800 font-medium text-sm mb-1">
          Invito al team
        </h3>
        <p className="text-blue-700 text-xs">
          Registrandoti accetti automaticamente l'invito a unirti a{" "}
          <strong>{teamName}</strong>.
        </p>
      </div>
    );
  };

  // Add these handlers for Google Auth
  const handleGoogleAuthSuccess = () => {
    router.push("/dashboard");
  };

  const handleGoogleAuthError = (errorMessage: string) => {
    showErrorToast({
      message: errorMessage || "Errore durante l'autenticazione con Google",
    });
  };

  return (
    <MobilePage>
      <AuthLogo />
      <h1 className={styles.title}>Iscrizione</h1>

      <form
        className={styles.form}
        onSubmit={(e) => {
          e.preventDefault();
          handleRegister();
        }}
      >
        <div className="w-full flex flex-col gap-4">
          <div className={styles.link4} style={{ textAlign: "center" }}>
            Inserisci i tuoi dati per creare un account gratis
          </div>
          {renderTeamInviteCard()}
          <div className="relative">
            <Input
              type="text"
              placeholder="Nome"
              value={name}
              onChange={(value) => {
                setName(value);
                clearError("name");
              }}
              error={errors.name}
            />
            {errors.name && (
              <p className="text-red-500 text-xs mt-1">{errors.name}</p>
            )}
          </div>
          <div className="relative">
            <Input
              type="text"
              placeholder="Cognome"
              value={surname}
              onChange={(value) => {
                setSurname(value);
                clearError("surname");
              }}
              error={errors.surname}
            />
            {errors.surname && (
              <p className="text-red-500 text-xs mt-1">{errors.surname}</p>
            )}
          </div>
          <div className="relative">
            <Input
              type="email"
              placeholder="E-mail"
              value={email}
              onChange={(value) => {
                if (!isEmailReadOnly) {
                  setEmail(value);
                  clearError("email");
                }
              }}
              error={errors.email}
              readOnly={isEmailReadOnly}
              className={isEmailReadOnly ? "bg-gray-100" : ""}
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>
          <div className="relative">
            <InputPhone
              value={phone}
              placeholder="Telefono"
              onChange={(value) => {
                setPhone(value);
                clearError("phone");
              }}
              onChangeCountry={(value) => {
                setCountry(value);
                clearError("phone");
              }}
              error={errors.phone}
            />
            {errors.phone && (
              <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
            )}
          </div>
          {renderPasswordFields()}{" "}
          <div className={styles.link3} style={{ textAlign: "center" }}>
            Inviando questo modulo, confermi di aver letto e accettato la
            nostra&nbsp;
            <a
              href="https://www.iubenda.com/privacy-policy/35231362"
              className="iubenda-white iubenda-noiframe iubenda-embed iubenda-noiframe"
              style={{
                fontSize: "12px",
                textDecoration: "underline",
                color: "var(--blue)",
              }}
              title="Privacy Policy"
            >
              Informativa sulla privacy
            </a>
            &nbsp;e la&nbsp;
            <a
              href="https://www.iubenda.com/privacy-policy/35231362/cookie-policy"
              className="iubenda-white iubenda-noiframe iubenda-embed iubenda-noiframe"
              style={{
                fontSize: "12px",
                textDecoration: "underline",
                color: "var(--blue)",
              }}
              title="Cookie Policy"
            >
              Cookie Policy
            </a>
            {errors.terms && (
              <p className="text-red-500 text-xs mt-1">{errors.terms}</p>
            )}
          </div>
          <div className="flex justify-center">
            <Button
              text="Iscriviti"
              onClick={handleRegister}
              color="white"
              backgroundColor="var(--blue)"
              fontSize="15px"
              disabled={isSubmitting}
              className="transition-all duration-200 hover:opacity-90"
              showLoading={true}
              loadingText="Registrazione..."
            />
          </div>
          {!inviteId && (
            <>
              <div className={styles.divider}>
                <span>oppure</span>
              </div>

              <div className="flex justify-center">
                <GoogleAuthButton
                  text="Iscriviti con Google"
                  onSuccess={handleGoogleAuthSuccess}
                  onError={handleGoogleAuthError}
                />
              </div>
            </>
          )}
          <div className={styles.link4} style={{ textAlign: "center" }}>
            Nessun costo fisso, Cancella in qualsiasi momento
          </div>
        </div>
      </form>

      <div className={styles.link2}>
        Hai già un account?{" "}
        <Link className={styles.link} href="/auth/login">
          Accedi
        </Link>
      </div>
    </MobilePage>
  );
}
