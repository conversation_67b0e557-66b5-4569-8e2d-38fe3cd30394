"use client";
import React, { useEffect, useState, useContext, useRef } from "react";
import { updateProfile } from "@/services/api";
import { UserContext } from "@/components/_context/UserContext";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import { useRouter } from "next/navigation";
import ButtonSection from "@/components/buttons/ButtonSection1";
import { CameraIcon } from "lucide-react";
import toast from "react-hot-toast";
import Loader1 from "@/components/loaders/Loader1";
import LogoutButton from "@/components/auth/LogoutButton";

function Page() {
  const router = useRouter();
  const {
    name,
    image,
    email,
    phone,
    isCustomer,
    hasBillingProfile,
    staffProperties,
    isNotOwnerPermission,
    fetchProfile,
    hasContract,
  } = useContext(UserContext);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  // Removed handleLogout function as we're using the LogoutButton component

  useEffect(() => {
    const loadProfile = async () => {
      setIsLoading(true);
      try {
        await fetchProfile();
        setProfileImage(image);
      } catch (error) {
        console.error("Error fetching profile data:", error);
        toast.error("Failed to load profile information");
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, []);

  const handleImageClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file type
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png"];
    if (!allowedTypes.includes(file.type)) {
      toast.error("Solo immagini JPG, JPEG o PNG sono permesse");
      return;
    }

    // Check file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("L'immagine non può superare i 5MB");
      return;
    }

    try {
      // Show loading state
      toast.loading("Caricamento immagine in corso...");

      // Create a preview for immediate feedback
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Update profile with new image
      const updatedProfile = await updateProfile(name || "", file);

      if (updatedProfile) {
        await fetchProfile(); // Refresh context after update
        toast.dismiss();
        toast.success("Immagine profilo aggiornata con successo");
      }
    } catch (error) {
      toast.dismiss();
      toast.error("Errore durante l'aggiornamento dell'immagine");
      console.error("Error uploading image:", error);
    }
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Account"
        actionLeftIcon={() => {
          router.push("/dashboard");
        }}
        actionRightIcon={() => {
          router.push("/dashboard/settings");
        }}
      />

      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
          {/* Profile Image Section */}
          <div className="w-full flex flex-col items-center justify-center gap-3 mb-4">
            <div className="relative cursor-pointer" onClick={handleImageClick}>
              <div
                className="h-24 w-24 rounded-full flex items-center justify-center overflow-hidden"
                style={{
                  border: `2px solid ${isNotOwnerPermission ? "#9e9e9e" : "var(--accent)"}`,
                  background: "#f0f0f0",
                }}
              >
                <img
                  src={
                    profileImage ||
                    image ||
                    "https://samidaiddar.no/sami-lavdi/wp-content/themes/samidaiddar/dist/images/profile-fallback.png"
                  }
                  alt="Profile"
                  className="h-full w-full object-cover"
                />
              </div>
              <div
                className="absolute bottom-0 right-0 rounded-full bg-white p-1 shadow-md"
                style={{
                  border: "1px solid #e0e0e0",
                }}
              >
                <CameraIcon
                  size={16}
                  color={isNotOwnerPermission ? "#9e9e9e" : "var(--accent)"}
                />
              </div>
            </div>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              onChange={handleImageChange}
            />
            <div className="text-center">
              <h3 className="font-medium text-lg">{name || "--"}</h3>
              <p className="text-sm text-gray-500">{email || "--"}</p>
              {phone && <p className="text-sm text-gray-500">{phone}</p>}
              {isNotOwnerPermission && (
                <div className="mt-2 p-2 bg-gray-50 rounded-md text-sm text-gray-600 max-w-xs">
                  <p>
                    Hai accesso limitato. Alcune funzionalità sono disabilitate.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Properties Section */}
          {staffProperties && staffProperties.length > 0 && (
            <div className="flex flex-col gap-2 bg-gray-50 p-3 rounded-lg">
              <h4 className="text-[14px] font-medium">Le tue proprietà</h4>
              <div className="flex flex-col gap-2">
                {staffProperties.map((property) => {
                  // Determine permission badge color
                  const isCleaningStaff =
                    property.permissions.includes("Cleaning Staff") &&
                    property.permissions.length === 1;
                  const permissionColor = isCleaningStaff
                    ? "#9e9e9e"
                    : "var(--accent)";

                  // Translate permissions to Italian
                  const translatePermission = (perm: string) => {
                    switch (perm) {
                      case "Property Configuration":
                        return "Configurazione Proprietà";
                      case "Booking Management":
                        return "Gestione Prenotazioni";
                      case "Rates Management":
                        return "Gestione Tariffe";
                      case "Room Configuration":
                        return "Configurazione Camere";
                      case "View Only":
                        return "Solo Visualizzazione";
                      case "Cleaning Staff":
                        return "Personale di Pulizia";
                      default:
                        return perm;
                    }
                  };

                  return (
                    <div
                      key={property.property_id}
                      className={`flex justify-between items-center p-3 rounded-md shadow-sm ${
                        property.is_active === false ? "bg-gray-50" : "bg-white"
                      }`}
                      style={{
                        borderLeft: `3px solid ${permissionColor}`,
                        opacity: property.is_active === false ? 0.7 : 1,
                        filter:
                          property.is_active === false
                            ? "grayscale(100%)"
                            : "none",
                        position: "relative",
                        overflow: "hidden",
                      }}
                    >
                      {property.is_active === false && (
                        <div
                          className="absolute top-0 right-0 bg-gray-200 text-gray-700 px-2 py-1 text-xs transform rotate-45 translate-x-2 translate-y-1"
                          style={{
                            width: "120px",
                            textAlign: "center",
                          }}
                        >
                          Disattivata
                        </div>
                      )}
                      <div>
                        <p className="font-medium">{property.property_name}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {property.permissions.map(
                            (permission: string, index: number) => (
                              <span
                                key={index}
                                className="text-xs px-2 py-1 rounded-full"
                                style={{
                                  backgroundColor:
                                    permission === "Cleaning Staff"
                                      ? "#f5f5f5"
                                      : "#e6f7ff",
                                  color:
                                    permission === "Cleaning Staff"
                                      ? "#757575"
                                      : "#0077cc",
                                }}
                              >
                                {translatePermission(permission)}
                              </span>
                            ),
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        {property.is_active === false ? (
                          <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full animate-pulse">
                            Disattivata
                          </span>
                        ) : property.is_onboarded ? (
                          <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            Attivo
                          </span>
                        ) : (
                          <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                            Inattivo
                          </span>
                        )}
                        {isCleaningStaff && (
                          <span className="text-xs text-gray-500">
                            Accesso limitato
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Password Section */}
          <div>
            <h4
              className="text-[14px]"
              style={{ fontWeight: "500", marginBottom: "6px" }}
            >
              Modifica Password
            </h4>
            <ButtonSection
              completed={true}
              title="Password"
              desciption="********"
              href={`/dashboard/settings/account/password`}
            />
          </div>

          {/* Personal Information Section */}
          <div className="flex flex-col gap-2">
            <h4
              className="text-[14px]"
              style={{
                fontWeight: "500",
                marginBottom: "6px",
                opacity: isNotOwnerPermission ? 0.7 : 1,
              }}
            >
              Informazioni Personali
              {isNotOwnerPermission && (
                <span className="ml-2 text-xs text-gray-500">
                  (Accesso limitato)
                </span>
              )}
            </h4>
            <ButtonSection
              completed={hasBillingProfile}
              title="Informazioni di Fatturazione"
              href={`/dashboard/settings/account/billing`}
              status={!hasBillingProfile ? "required" : "completed"}
              statusColor={!hasBillingProfile ? "red" : "var(--accent)"}
              disabled={isNotOwnerPermission}
              onClick={
                isNotOwnerPermission
                  ? () =>
                      toast.error(
                        "Non hai i permessi necessari per accedere a questa sezione",
                      )
                  : undefined
              }
            />
            <ButtonSection
              completed={true}
              title="Informazioni Bancarie"
              href={`/dashboard/settings/account/bank`}
              disabled={isNotOwnerPermission}
              onClick={
                isNotOwnerPermission
                  ? () =>
                      toast.error(
                        "Non hai i permessi necessari per accedere a questa sezione",
                      )
                  : undefined
              }
            />
          </div>

          {/* Agreements Section */}
          <div className="flex flex-col gap-2">
            <h4
              className="text-[14px]"
              style={{
                fontWeight: "500",
                marginBottom: "6px",
                opacity: isNotOwnerPermission ? 0.7 : 1,
              }}
            >
              Accordi
              {isNotOwnerPermission && (
                <span className="ml-2 text-xs text-gray-500">
                  (Accesso limitato)
                </span>
              )}
            </h4>
            <ButtonSection
              completed={hasContract}
              title="Il tuo Contratto"
              desciption={
                hasContract
                  ? "Visualizza e scarica il tuo contratto"
                  : "Genera e firma il contratto"
              }
              href={`/dashboard/settings/account/contract`}
              status={hasContract ? "completed" : "required"}
              statusColor={hasContract ? "var(--accent)" : "red"}
              disabled={isNotOwnerPermission}
              onClick={
                isNotOwnerPermission
                  ? () =>
                      toast.error(
                        "Non hai i permessi necessari per accedere a questa sezione",
                      )
                  : undefined
              }
            />
          </div>

          {/* Account Management Section */}
          <div className="flex flex-col gap-2 mt-4">
            <h4
              className="text-[14px]"
              style={{ fontWeight: "500", marginBottom: "6px" }}
            >
              Gestione Account
            </h4>

            {/* Property Deactivation Button */}
            <ButtonSection
              completed={true}
              title="Disattiva Proprietà"
              desciption="Richiedi la disattivazione di una proprietà"
              href={`/dashboard/settings/account/property-deactivation`}
              disabled={isNotOwnerPermission}
              onClick={
                isNotOwnerPermission
                  ? () =>
                      toast.error(
                        "Non hai i permessi necessari per accedere a questa sezione",
                      )
                  : undefined
              }
            />

            {/* Improved Logout Button */}
            <LogoutButton
              text="Disconnetti"
              backgroundColor="#FEF2F2"
              color="#EF4444"
              fontSize="14px"
              showIcon={true}
              fullWidth={true}
              containerStyle={{
                marginBottom: "16px",
              }}
            />
          </div>
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
