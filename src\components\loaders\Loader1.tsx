"use client";
import React, { useEffect, useState } from "react";
import styles from "./Loader1.module.css";
import dynamic from "next/dynamic";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import loadingAnimation from "../../../public/animations/loading.json";

interface Props {
  className?: string;
}

const Loader1: React.FC<Props> = ({ className }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null; // Avoid rendering on server

  return (
    <div className={`${styles["keyhole-loader"]} ${className || ""}`}>
      <Lottie
        animationData={loadingAnimation}
        loop={true}
        style={{
          width: "400px",
          height: "400px",
          filter: "drop-shadow(0 0 10px rgba(252, 189, 76, 0.5))",
        }}
      />
    </div>
  );
};

export default Loader1;
