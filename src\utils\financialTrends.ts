/**
 * Financial trend calculation utilities
 */

export interface TrendData {
  value: number;
  isPositive: boolean;
  period: string;
}

/**
 * Calculate percentage change between two values
 */
export const calculatePercentageChange = (
  current: number,
  previous: number,
): number => {
  if (previous === 0) {
    return current > 0 ? 100 : 0;
  }
  return ((current - previous) / previous) * 100;
};

/**
 * Calculate trend based on current and previous period values
 */
export const calculateTrend = (
  current: string | number,
  previous: string | number,
  period: string = "periodo precedente",
): TrendData | null => {
  const currentValue = typeof current === "string" ? parseFloat(current) : current;
  const previousValue = typeof previous === "string" ? parseFloat(previous) : previous;

  if (isNaN(currentValue) || isNaN(previousValue)) {
    return null;
  }

  const percentageChange = calculatePercentageChange(currentValue, previousValue);
  
  // Don't show trend for very small changes (less than 0.1%)
  if (Math.abs(percentageChange) < 0.1) {
    return null;
  }

  return {
    value: Math.abs(Math.round(percentageChange * 10) / 10),
    isPositive: percentageChange >= 0,
    period,
  };
};

/**
 * Calculate monthly growth rate based on earnings data
 */
export const calculateMonthlyGrowthRate = (
  monthlyEarnings: Array<{ month: string; earnings: number }>,
): TrendData | null => {
  if (monthlyEarnings.length < 2) {
    return null;
  }

  // Get last two months
  const latest = monthlyEarnings[monthlyEarnings.length - 1];
  const previous = monthlyEarnings[monthlyEarnings.length - 2];

  return calculateTrend(latest.earnings, previous.earnings, "mese scorso");
};

/**
 * Calculate trend based on booking velocity (bookings per time period)
 */
export const calculateBookingVelocityTrend = (
  currentPeriodBookings: number,
  previousPeriodBookings: number,
  periodDays: number = 30,
): TrendData | null => {
  const currentVelocity = currentPeriodBookings / periodDays;
  const previousVelocity = previousPeriodBookings / periodDays;

  return calculateTrend(
    currentVelocity,
    previousVelocity,
    `${periodDays} giorni`,
  );
};

/**
 * Determine trend color class based on metric type and trend direction
 */
export const getTrendColorClass = (
  trend: TrendData,
  metricType: "earnings" | "bookings" | "expenses" = "earnings",
): string => {
  const isGoodTrend = metricType === "expenses" ? !trend.isPositive : trend.isPositive;
  return isGoodTrend ? "text-green-600" : "text-red-600";
};

/**
 * Format trend display text
 */
export const formatTrendText = (trend: TrendData): string => {
  const direction = trend.isPositive ? "+" : "-";
  return `${direction}${trend.value}%`;
};