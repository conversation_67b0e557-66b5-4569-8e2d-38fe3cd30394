/* Template Card Styles */
.templateCard {
  width: 100%;
  max-width: 100%;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
  overflow: hidden;
  word-wrap: break-word;
  hyphens: auto;
}

@media (min-width: 640px) {
  .templateCard {
    padding: 1.25rem;
  }
}

.templateCard .content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  width: 100%;
}

@media (min-width: 640px) {
  .templateCard .content {
    gap: 1rem;
  }
}

.templateCard .iconContainer {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
}

@media (min-width: 640px) {
  .templateCard .iconContainer {
    width: 40px;
    height: 40px;
  }
}

.templateCard .textContent {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.templateCard .title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

@media (min-width: 640px) {
  .templateCard .title {
    font-size: 1rem;
  }
}

.templateCard .description {
  font-size: 0.75rem;
  color: #6b7280;
  line-height: 1.5;
  word-break: break-word;
  hyphens: auto;
}

@media (min-width: 640px) {
  .templateCard .description {
    font-size: 0.875rem;
  }
}

.templateCard .readMoreBtn {
  font-size: 0.75rem;
  color: #fcb51f;
  font-weight: 500;
  margin-top: 0.5rem;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
  transition: color 0.2s;
}

.templateCard .readMoreBtn:hover {
  color: #fcb51f;
}

.templateCard .downloadBtn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
  background-color: #133157;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.templateCard .downloadBtn:hover:not(:disabled) {
  background-color: #fcb51f;
}

.templateCard .downloadBtn:active:not(:disabled) {
  background-color: #133157;
}

.templateCard .downloadBtn:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.templateCard .downloadBtn svg {
  flex-shrink: 0;
}

/* Ensure no content overflows */
.templateCard * {
  max-width: 100%;
  box-sizing: border-box;
}

/* Handle very small screens */
@media (max-width: 320px) {
  .templateCard {
    padding: 0.75rem;
  }

  .templateCard .title {
    font-size: 0.8125rem;
  }

  .templateCard .description {
    font-size: 0.6875rem;
  }

  .templateCard .downloadBtn {
    font-size: 0.8125rem;
    padding: 0.5rem 0.75rem;
  }

  .templateCard .content {
    gap: 0.5rem;
  }

  .templateCard .iconContainer {
    width: 32px;
    height: 32px;
  }
}
