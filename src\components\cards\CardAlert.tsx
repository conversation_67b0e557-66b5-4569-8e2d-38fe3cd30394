import React, { CSSProperties } from "react";
import styles from "./card.module.css";
import Link from "next/link";
import { InformationCircleIcon } from "@heroicons/react/24/outline";

interface CardAlertProps {
  color: "red" | "orange" | "green" | "blue" | "gray";
  title?: string;
  message?: string;
  buttonText?: string;
  buttonClick?: () => void;
  learnMoreLink?: string;
  learnMoreText?: string;
  style?: CSSProperties;
}

function CardAlert({
  color,
  title,
  message,
  buttonText,
  learnMoreText,
  buttonClick,
  learnMoreLink,
  style,
}: CardAlertProps) {
  const getColor = (componentColor: string) => {
    switch (componentColor) {
      case "red":
        return {
          full: "rgb(255, 99, 71)",
          opacity: "rgba(255, 99, 71, 0.15)",
        };

      case "orange":
        return {
          full: "rgb(255, 165, 0)",
          opacity: "rgba(255, 165, 0, 0.15)",
        };

      case "green":
        return {
          full: "rgb(50, 205, 50)",
          opacity: "rgba(50, 205, 50, 0.15)",
        };

      default:
        return {
          full: "rgb(128, 128, 128)", // Color gris por defecto
          opacity: "rgba(128, 128, 128, 0.30)",
        };
    }
  };

  const parsedColor = getColor(color);

  return (
    <div
      className={styles.containerCardAlert}
      style={{
        ...style,
      }}
    >
      <div
        className={styles.cardAlert}
        style={{
          borderColor: parsedColor.full,
          background: parsedColor.opacity,
        }}
      >
        <div className={styles.iconContainer}>
          <InformationCircleIcon
            className={styles.icon}
            style={{
              color: color,
              strokeWidth: 2,
            }}
          />
        </div>
        <div className={styles.info}>
          {title && <p className={styles.title}>{title || "Title"}</p>}
          {message && (
            <p className={styles.message}>
              {message || "Message"}
              &nbsp;
              {learnMoreLink && (
                <Link className={styles.link} href={learnMoreLink || "/"}>
                  {learnMoreText ? learnMoreText : "Scopri di più"}
                </Link>
              )}
            </p>
          )}
        </div>
        {buttonText && (
          <div className={styles.button} onClick={buttonClick}>
            {buttonText || "Button"}
          </div>
        )}
      </div>
    </div>
  );
}

export default CardAlert;
