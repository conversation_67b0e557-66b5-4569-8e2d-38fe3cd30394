.section_calendar {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;

  width: 100%;
  gap: 20px;

  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;

  justify-content: center;
  align-items: center;

  margin-top: 20px;
}

.calendar {
  width: 100%;

  font-family: Arial, sans-serif;
  color: rgb(0, 0, 0);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.container_calendar {
  border-radius: 20px;
}

.calendar_table {
  width: 100%;
}

.calendar_table th {
  padding: 20px 0px;
  text-align: center;
  transition: 300ms;
}

.calendar_table td {
  height: 60px;
  width: 50px;
  padding: 0px 0px;
  text-align: center;
  transition: 300ms;
  border-bottom: 1px solid rgba(0, 0, 0, 0.292);
  border-radius: 10px;
  /* cursor: pointer; */
}

.calendar_badge {
  height: 20px;
  max-width: 60px;

  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  gap: 3px;
  align-items: center;
  justify-content: center;
}

.event_badge_pin {
  height: 5px;
  width: 5px;
  background-color: white;
  border-radius: 50%;
}

.past_day {
  color: #3e2d2d;
  opacity: 0.2;
}

.disabled {
  cursor: default;
}

.today {
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
  color: rgb(152, 152, 152);
}

.not_current_month {
  color: #ccc;
  pointer-events: none;
}

.calendar_head_container {
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
  width: 100%;

  .calendar_head_container th {
    border-bottom: 0.5px solid #e5e7eb;
    padding: 20px 8px;
  }

  .th {
    font-size: 13px;
    font-weight: 200;
    border-bottom: 1px solid rgba(0, 0, 0, 0.098);
  }

  .br {
    font-size: 13px;
    font-weight: 200;
    border-bottom: 1px solid rgba(0, 0, 0, 0.098);
  }

  .br_inverted {
    font-size: 13px;
    font-weight: 200;
    border-bottom: 1px solid rgba(0, 0, 0, 0.098);
  }
}

.calendar_controls {
  width: auto;
  display: flex;

  align-items: center;
  justify-content: center;
  gap: 5px;

  .calendar_controls button {
    width: 200px;
    height: 100%;
    background-color: #00000010;
    border-radius: 5px;
  }
}

.day_full {
  background-color: rgba(255, 77, 0, 0.481);
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  margin: 2px;
}

.dayTile {
  width: 350px;
  height: 40px;
}

.reservationBar {
  height: 18px;

  position: absolute;
  left: 0;
  z-index: 5;
}

.nameReservation {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 60px; /* Adjust width as needed */
  display: block; /* or inline-block */

  font-size: 13px;
  position: absolute;
  left: 24px;
  color: #46d11f;
  font-weight: 600;
  z-index: 5;
}

.selectedRange {
  position: absolute;
  background-color: rgba(31, 41, 55, 0.566);
  z-index: 0;
  height: 50px;
  width: 100%;

  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
