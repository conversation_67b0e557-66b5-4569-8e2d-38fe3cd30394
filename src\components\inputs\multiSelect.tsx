import React, { useEffect } from "react";
import styles from "./input.module.css";

interface MultiSelectProps {
  isMultiSelect: boolean;
  options: {
    id: string;
    title: string;
    description?: string;
  }[];

  onChange: (selected: string /* Pass the optionId */[]) => void;
  value: string[] /* Pass the array of selection optionId */;
  title: string;

  optionsStyle?: {
    fontSizeTitle: string;
    fontSizeDescription: string;
  };

  unselectingAnswer?: number; //give the index of the answer that if selected unchecks all the others (requires isMultiSelect to be true) !! INDEX START FROM 1 (order of answers)
}

function MultiSelect({
  isMultiSelect,
  options,
  onChange,
  value,
  title,
  optionsStyle,
  unselectingAnswer,
}: MultiSelectProps) {
  return (
    <div className="mt-2 text-[14px]">
      <label htmlFor="multiSelect">{title}</label>

      <section className="mt-4">
        {options.map((option: any, index: number) => {
          return (
            <div
              key={option.id}
              className="px-4 flex flex-row gap-2 items-center my-2 bg-[white] py-2 rounded-[20px]"
              style={{ minHeight: "50px" }}
              onClick={() => {
                if (isMultiSelect) {
                  // Check if the current option is the unselecting answer
                  if (unselectingAnswer && unselectingAnswer === index + 1) {
                    // If the unselecting option is already selected, clear all selections
                    if (value.includes(option.id)) {
                      onChange([]);
                    } else {
                      onChange([option.id]); // Select only this option
                    }
                    return;
                  }

                  // Handle the case where another option is selected and the unselecting answer should be removed
                  if (unselectingAnswer) {
                    const unselectingOption = options[unselectingAnswer - 1]; // Get the actual unselecting option
                    const filteredSelection = value.filter(
                      (val) => val !== unselectingOption.id,
                    );

                    if (value.includes(option.id)) {
                      onChange(
                        filteredSelection.filter((val) => val !== option.id),
                      );
                    } else {
                      onChange([...filteredSelection, option.id]);
                    }
                    return;
                  }

                  if (value.includes(option.id)) {
                    onChange(value.filter((val) => val !== option.id));
                  } else {
                    onChange([...value, option.id]);
                  }
                } else {
                  onChange([option.id]);
                }
              }}
            >
              <div
                className={`rounded-[50%] border-[2px] ${value.includes(option.id) ? "border-[var(--accent)]" : "border-[#000000]"} flex items-center justify-center`}
                style={{ minHeight: "15px", minWidth: "15px" }}
              >
                {value.includes(option.id) && (
                  <div className="rounded-[50%] w-[5px] h-[5px] bg-[var(--accent)] flex items-center justify-center" />
                )}
              </div>

              <label htmlFor={option.id}>
                <h3 style={{ fontSize: optionsStyle?.fontSizeTitle ?? "" }}>
                  {option.title}
                </h3>
                <p
                  className="text-[11px] text-[#00000090]"
                  style={{ fontSize: optionsStyle?.fontSizeDescription ?? "" }}
                >
                  {option.description}
                </p>
              </label>
            </div>
          );
        })}
      </section>
    </div>
  );
}

export default MultiSelect;
