"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import ButtonSection from "@/components/buttons/ButtonSection";
import ButtonSectionFunction from "@/components/buttons/ButtonSectionFunction";
import CardAlert from "@/components/cards/CardAlert";
import Loader1 from "@/components/loaders/Loader1";
import {
  getSettingsBubbleStatus,
  metadataViewPropertyData,
  propertyOnBoarding,
} from "@/services/api";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import SuccessAnimation from "@/components/animations/SuccessAnimation";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [propertyData, setPropertyData] = React.useState<any>(null);
  const [statusSettings, setStatusSettings] = React.useState({
    amenities: false,
    general_info: false,
    guest_arrival: false,
    location: false,
    pictures: false,
    rates: false,
    rooms: false,
    billing_complete: false,
    contract_signed: false,
    chekin_enabled: false,
  });

  const [isLoading, setIsLoading] = React.useState<boolean>(true);
  const [isTarifAvailable, setIsTarifAvailable] = useState(true);
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);

  const handleFetchProperty = async () => {
    setIsLoading(true);
    const callMetadata = await metadataViewPropertyData(params.propertyId);
    const statusSetting = await getSettingsBubbleStatus(params.propertyId);

    if (!callMetadata) {
      toast.error("Errore nel caricamento dei dati della proprietà");
    }
    setPropertyData(callMetadata);
    setStatusSettings({
      amenities: statusSetting.data.amenities,
      general_info: statusSetting.data.general_info,
      guest_arrival: statusSetting.data.guest_arrival,
      location: statusSetting.data.location,
      pictures: statusSetting.data.pictures,
      rates: statusSetting.data.rates,
      rooms: statusSetting.data.rooms,
      billing_complete: statusSetting.data.billing_complete,
      contract_signed: statusSetting.data.contract_signed,
      chekin_enabled: statusSetting.data.chekin_enabled,
    });
    setIsLoading(false);
  };

  const handleSyncProperty = async () => {
    try {
      const responseStatus = await propertyOnBoarding(params.propertyId);

      // Check response status
      if (responseStatus === 202) {
        // Success - show animation instead of toast
        setShowSuccessAnimation(true);
      } else if (responseStatus === 400) {
        // Handle validation errors
        toast.error("Errore: Property is not ready for onboarding");
        setIsLoading(false);
      } else if (responseStatus === 403) {
        // Handle permission denied
        toast.error("Errore: Property is deactivated");
        setIsLoading(false);
      } else {
        // Handle other errors
        toast.error("Errore durante la sincronizzazione della proprietà");
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Error syncing property:", error);
      toast.error("Si è verificato un errore durante la sincronizzazione");
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleFetchProperty();
  }, []);

  return (
    <MobilePageStart>
      {/* Success Animation */}
      <SuccessAnimation
        isVisible={showSuccessAnimation}
        onComplete={() => {
          setIsLoading(false);
        }}
      />

      <HeaderPage
        title="Panoramica"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <>
          <div className="mt-4 px-4 h-full w-full flex flex-col gap-2">
            <img
              src={
                propertyData?.property?.cover_image ||
                propertyData?.property?.photos?.[0]?.image ||
                "https://news.airbnb.com/wp-content/uploads/sites/4/2023/04/Private-Room-Cape-Town.jpeg?w=512"
              }
              className="h-[400px] rounded-[10px]"
              style={{
                objectFit: "cover",
              }}
              alt={propertyData?.property?.name || "Property image"}
            />
          </div>

          <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
            <ButtonSection
              completed={statusSettings.general_info}
              title="Informazioni Generali"
              href={`/dashboard/property/${params.propertyId}/general`}
              ticker
            />

            <ButtonSection
              completed={statusSettings.location}
              title="Posizione"
              href={`/dashboard/property/${params.propertyId}/position`}
              ticker
            />

            <ButtonSection
              completed={statusSettings.amenities}
              title="Servizi"
              href={`/dashboard/property/${params.propertyId}/amenities`}
              ticker
            />

            <ButtonSection
              completed={statusSettings.rooms}
              title="Stanza"
              href={`/dashboard/property/${params.propertyId}/rooms`}
              ticker
            />

            <ButtonSection
              completed={statusSettings.pictures}
              title="Foto"
              href={`/dashboard/property/${params.propertyId}/photos`}
              ticker
            />

            <ButtonSection
              completed={statusSettings.guest_arrival}
              title="Arrivo Ospiti"
              href={`/dashboard/property/${params.propertyId}/arrival`}
              ticker
            />

            <ButtonSectionFunction
              completed={statusSettings.rates}
              title="Piano tariffario"
              onClick={() => {
                router.push(`/dashboard/property/${params.propertyId}/tarifs`);
              }}
              ticker
              desciption={!isTarifAvailable ? "Creare almeno una stanza" : null}
              disabled={!isTarifAvailable}
            />

            <ButtonSection
              completed={true}
              title="Permessi"
              href={`/dashboard/property/${params.propertyId}/permissions`}
            />

            {statusSettings.chekin_enabled && !propertyData?.property?.is_domorent && propertyData?.property?.is_onboarded && (
              <ButtonSection
                completed={true}
                title="Check-in Online"
                desciption="Gestisci Chekin per questa proprietà"
                href={`/dashboard/property/${params.propertyId}/chekin`}
              />
            )}

            <ButtonSection
              completed={true}
              title="Tariffe & Stagioni"
              href={`/dashboard/calendar/pricing/?property=${params.propertyId}`}
            />

            {!statusSettings.billing_complete && (
              <>
                <CardAlert
                  title="Impostazioni di fatturazione incomplete"
                  message="Per sincronizzare la proprietà, completa prima le impostazioni di fatturazione"
                  color="orange"
                />
                <a
                  href="/dashboard/settings/account/billing"
                  className="text-blue-500 underline text-sm text-center block"
                >
                  Clicca qui per aggiornare le tue informazioni di fatturazione
                </a>
              </>
            )}
            {!statusSettings.contract_signed && (
              <>
                <CardAlert
                  title="Contratto non firmato"
                  message="Per sincronizzare la proprietà, completa prima di firmare il contratto"
                  color="orange"
                />
                <a
                  href="/dashboard/settings/account/contract"
                  className="text-blue-500 underline text-sm text-center block"
                >
                  Clicca qui per firmare il contratto
                </a>
              </>
            )}

            <ButtonSectionFunction
              completed={propertyData?.property?.is_onboarded}
              title="Sincronizza Proprietà"
              onClick={async () => {
                if (!statusSettings.billing_complete) {
                  toast.error(
                    "Perfavore, settare le impostazioni di fatturazione nelle impostazioni Account",
                  );
                  return;
                }
                await handleSyncProperty();
              }}
              ticker
              desciption="Proprietà e sincronizzazione"
              disabled={
                isLoading ||
                !statusSettings.location ||
                !statusSettings.amenities ||
                !statusSettings.general_info ||
                !statusSettings.guest_arrival ||
                !statusSettings.pictures ||
                !statusSettings.rates ||
                !statusSettings.rooms ||
                !statusSettings.billing_complete ||
                !statusSettings.contract_signed ||
                propertyData?.property?.is_onboarded
              }
            />
          </div>
        </>
      )}
    </MobilePageStart>
  );
}

export default Page;
