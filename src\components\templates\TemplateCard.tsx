"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import { Template } from "@/services/api";
import styles from "./TemplateCard.module.css";

interface TemplateCardProps {
  template: Template;
  isExpanded: boolean;
  isDownloading: boolean;
  maxDescriptionLength: number;
  onToggleDescription: (templateId: number) => void;
  onDownload: (fileUrl: string, title: string, templateId: number) => void;
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  isExpanded,
  isDownloading,
  maxDescriptionLength,
  onToggleDescription,
  onDownload,
}) => {
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "contract":
        return "📄";
      case "rules":
        return "📋";
      default:
        return "📎";
    }
  };

  const shouldShowReadMore = (description: string) => {
    return description.length > maxDescriptionLength;
  };

  const getDisplayDescription = (description: string, templateId: number) => {
    if (!shouldShowReadMore(description)) return description;

    if (isExpanded) {
      return description;
    }

    return description.substring(0, maxDescriptionLength) + "...";
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-full"
    >
      <div className={styles.templateCard}>
        <div className={styles.content}>
          <div className={styles.iconContainer}>
            <Image
              src="/icons/pdf.svg"
              alt="PDF"
              width={36}
              height={36}
              className="w-full h-full object-contain"
            />
          </div>
          <div className={styles.textContent}>
            <div className="flex items-center gap-2 mb-2 min-w-0">
              <span className="text-base sm:text-lg flex-shrink-0">
                {getCategoryIcon(template.category)}
              </span>
              <h3 className={styles.title}>{template.title}</h3>
            </div>
            <div className={styles.description}>
              <motion.div
                layout
                initial={false}
                animate={{ height: "auto" }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
              >
                <p>
                  {getDisplayDescription(template.description, template.id)}
                </p>
              </motion.div>

              {shouldShowReadMore(template.description) && (
                <motion.button
                  onClick={() => onToggleDescription(template.id)}
                  className={styles.readMoreBtn}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isExpanded ? "Mostra meno" : "Leggi di più"}
                </motion.button>
              )}
            </div>
          </div>
        </div>

        <button
          onClick={() => onDownload(template.file, template.title, template.id)}
          disabled={isDownloading}
          className={styles.downloadBtn}
        >
          {isDownloading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Scaricando...</span>
            </>
          ) : (
            <>
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                <polyline points="7 10 12 15 17 10" />
                <line x1="12" y1="15" x2="12" y2="3" />
              </svg>
              <span>Scarica PDF</span>
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default TemplateCard;
