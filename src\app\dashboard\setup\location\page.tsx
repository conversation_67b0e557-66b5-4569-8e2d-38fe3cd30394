"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { locationCreaLocation } from "@/services/api";
import toast from "react-hot-toast";
import dynamic from "next/dynamic";
import Button from "@/components/buttons/Button";
import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import CardAlert from "@/components/cards/CardAlert";
const InputMapNoSSR = dynamic(
  () => import("@/components/inputs/inputCardMap"),
  { ssr: false },
);

function Page() {
  const router = useRouter();

  const [city, setCity] = useState("");
  const [zip, setZip] = useState("");
  const [street, setStreet] = useState("");

  const [cords, setCords] = useState({ lat: 45.464664, lng: 9.18854 });
  const [isMapOpen, setIsMapOpen] = useState(false);

  // Validation helper
  const validateFields = () => {
    if (!city.trim()) {
      toast.error("La città è obbligatoria");
      return false;
    }
    if (!zip.trim() || !/^\d{4,10}$/.test(zip)) {
      toast.error("Codice postale non valido");
      return false;
    }
    if (!street.trim() || street.length < 3) {
      toast.error("L'indirizzo deve contenere almeno 3 caratteri");
      return false;
    }
    return true;
  };

  const handleFetchCordinates = async () => {
    if (!validateFields()) return;

    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${street},${city}${zip}&key=${process.env.NEXT_PUBLIC_GOOGLE_API_KEY}`,
      {
        method: "GET",
      },
    );

    const data = await response.json();
    if (data.status !== "OK") {
      toast.error("Indirizzo non trovato");
      return;
    }

    if (data.results.length > 0) {
      const { location } = data.results[0].geometry;
      setCords({ lat: location.lat, lng: location.lng });
      setIsMapOpen(true);
    }
  };

  const handleCreateLocation = async () => {
    if (!validateFields()) return;

    const call = await locationCreaLocation(
      street,
      zip,
      city,
      "Italy",
      cords.lat,
      cords.lng,
    );

    if (call.status === 200 || call.status === 201) {
      localStorage.setItem("locationId", call.data.id);
      router.push("/dashboard/setup/legal");
    } else {
      toast.error("Errore nella creazione della location, riprovare");
    }
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Posizione"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push("/dashboard")}
      />

      {!isMapOpen && (
        <div className="mt-4 px-4 h-full w-full flex flex-col justify-between">
          <div className="flex flex-col gap-4">
            <InputLabelDescription
              label="In che città si trova?"
              placeholder="Es. Milano"
              value={city}
              onChange={setCity}
            />

            <InputLabelDescription
              label="Codice postale"
              placeholder="Es. 21300"
              value={zip}
              onChange={setZip}
            />

            <InputLabelDescription
              label="Indirizzo"
              placeholder="Es. Via della spiga"
              value={street}
              onChange={setStreet}
            />

            <Button
              color="white"
              backgroundColor="var(--blue)"
              text="Continua"
              fontSize="14px"
              onClick={handleFetchCordinates}
            />
          </div>
        </div>
      )}

      {isMapOpen && (
        <div className="mt-4 px-4 h-full w-full flex flex-col justify-start">
          <CardAlert
            title="Attenzione"
            message="Controlla sulla mappa se la posizione trovata corrisponde a quella della proprietà"
            color="green"
            style={{ marginBottom: "20px" }}
          />

          <InputMapNoSSR
            style={{ background: "none" }}
            position={{
              lat: cords.lat.toString(),
              lng: cords.lng.toString(),
            }}
            onChange={(lat: string, lng: string) => {
              setCords({
                lat: Number(lat),
                lng: Number(lng),
              });
            }}
          />

          <br />
          <Button
            color="white"
            backgroundColor="var(--blue)"
            text="Continua"
            fontSize="14px"
            onClick={handleCreateLocation}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
