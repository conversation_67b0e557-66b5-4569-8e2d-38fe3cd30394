"use client";
import React, { forwardRef } from "react";
import { Calendar } from "lucide-react";

interface DateInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  min?: string;
  max?: string;
  className?: string;
  disabled?: boolean;
}

const DateInput = forwardRef<HTMLInputElement, DateInputProps>(
  (
    {
      value,
      onChange,
      placeholder = "Seleziona data",
      label,
      min,
      max,
      className = "",
      disabled = false,
    },
    ref,
  ) => {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    };

    return (
      <div className={`relative ${className}`}>
        {label && (
          <label className="block text-xs font-semibold tracking-wide text-gray-600 uppercase mb-2">
            {label}
          </label>
        )}
        <div className="relative">
          <input
            ref={ref}
            type="date"
            value={value}
            onChange={handleChange}
            min={min}
            max={max}
            disabled={disabled}
            className="w-full px-4 py-3 text-sm border border-gray-200 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#133157] focus:border-transparent disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed"
            style={{
              colorScheme: "light",
              appearance: "none",
              WebkitAppearance: "none",
            }}
          />
          <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
        </div>
      </div>
    );
  },
);

DateInput.displayName = "DateInput";

export default DateInput;
