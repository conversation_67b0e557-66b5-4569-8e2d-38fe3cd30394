/**
 * Date validation utilities for booking filters
 */

export interface DateRangeValidation {
  isValid: boolean;
  error?: string;
}

/**
 * Validates a date range for booking filters
 * @param startDate - Start date in YYYY-MM-DD format
 * @param endDate - End date in YYYY-MM-DD format
 * @returns Validation result with error message if invalid
 */
export const validateDateRange = (
  startDate?: string,
  endDate?: string,
): DateRangeValidation => {
  // If no dates provided, it's valid (no filter)
  if (!startDate && !endDate) {
    return { isValid: true };
  }

  // Validate date format (YYYY-MM-DD)
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;

  if (startDate && !dateRegex.test(startDate)) {
    return {
      isValid: false,
      error:
        "Formato data di inizio non valido. Utilizzare il formato AAAA-MM-GG.",
    };
  }

  if (endDate && !dateRegex.test(endDate)) {
    return {
      isValid: false,
      error:
        "Formato data di fine non valido. Utilizzare il formato AAAA-MM-GG.",
    };
  }

  // Validate that dates are valid dates
  if (startDate) {
    const startDateObj = new Date(startDate);
    if (isNaN(startDateObj.getTime())) {
      return {
        isValid: false,
        error: "Data di inizio non valida.",
      };
    }
  }

  if (endDate) {
    const endDateObj = new Date(endDate);
    if (isNaN(endDateObj.getTime())) {
      return {
        isValid: false,
        error: "Data di fine non valida.",
      };
    }
  }

  // Validate that start date is not after end date
  if (startDate && endDate) {
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    if (startDateObj > endDateObj) {
      return {
        isValid: false,
        error: "La data di inizio non può essere successiva alla data di fine.",
      };
    }
  }

  return { isValid: true };
};

/**
 * Formats a date string for display in Italian locale
 * @param dateString - Date in YYYY-MM-DD format
 * @returns Formatted date string or empty string if invalid
 */
export const formatDateForDisplay = (dateString?: string): string => {
  if (!dateString) return "";

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";

    return date.toLocaleDateString("it-IT", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  } catch {
    return "";
  }
};

/**
 * Gets today's date in YYYY-MM-DD format
 */
export const getTodayString = (): string => {
  return new Date().toISOString().split("T")[0];
};

/**
 * Gets a date string for a number of days from today
 * @param days - Number of days to add (can be negative)
 */
export const getDateStringFromToday = (days: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date.toISOString().split("T")[0];
};

/**
 * Common date range presets
 */
export const DATE_PRESETS = {
  today: () => {
    const today = getTodayString();
    return { start_date: today, end_date: today };
  },

  thisWeek: () => {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const startOfWeek = new Date(today);
    // Calculate days to subtract to get to Monday (1 = Monday, 0 = Sunday)
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    startOfWeek.setDate(today.getDate() - daysToMonday);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);

    return {
      start_date: startOfWeek.toISOString().split("T")[0],
      end_date: endOfWeek.toISOString().split("T")[0],
    };
  },

  thisMonth: () => {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    return {
      start_date: startOfMonth.toISOString().split("T")[0],
      end_date: endOfMonth.toISOString().split("T")[0],
    };
  },

  next30Days: () => {
    const today = getTodayString();
    const future = getDateStringFromToday(30);

    return {
      start_date: today,
      end_date: future,
    };
  },

  next7Days: () => {
    const today = getTodayString();
    const future = getDateStringFromToday(7);

    return {
      start_date: today,
      end_date: future,
    };
  },
};
