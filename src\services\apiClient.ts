import { fetchWithAuth } from "./apiInterceptor";

const API_URL = process.env.NEXT_PUBLIC_API_URL;

/**
 * API client with built-in authentication and token refresh
 */
export const apiClient = {
  /**
   * Make a GET request
   * @param endpoint API endpoint (without base URL)
   * @param options Additional fetch options
   * @returns Promise with response
   */ get: async (endpoint: string, options: RequestInit = {}) => {
    // Ensure proper URL construction by adding slash if needed
    const baseUrl = API_URL?.endsWith("/") ? API_URL : `${API_URL}/`;
    const cleanEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}${cleanEndpoint}`;

    const response = await fetchWithAuth(url, {
      method: "GET",
      ...options,
    });

    return response;
  },

  /**
   * Make a POST request
   * @param endpoint API endpoint (without base URL)
   * @param data Request body data
   * @param options Additional fetch options
   * @returns Promise with response
   */ post: async (endpoint: string, data: any, options: RequestInit = {}) => {
    // Ensure proper URL construction by adding slash if needed
    const baseUrl = API_URL?.endsWith("/") ? API_URL : `${API_URL}/`;
    const cleanEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}${cleanEndpoint}`;

    // Determine if we're sending JSON or FormData
    let headers = { ...options.headers };
    let body: any = data;

    if (!(data instanceof FormData)) {
      headers = {
        "Content-Type": "application/json",
        ...headers,
      };
      body = JSON.stringify(data);
    }

    const response = await fetchWithAuth(url, {
      method: "POST",
      headers,
      body,
      ...options,
    });

    return response;
  },

  /**
   * Make a PUT request
   * @param endpoint API endpoint (without base URL)
   * @param data Request body data
   * @param options Additional fetch options
   * @returns Promise with response
   */ put: async (endpoint: string, data: any, options: RequestInit = {}) => {
    // Ensure proper URL construction by adding slash if needed
    const baseUrl = API_URL?.endsWith("/") ? API_URL : `${API_URL}/`;
    const cleanEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}${cleanEndpoint}`;

    // Determine if we're sending JSON or FormData
    let headers = { ...options.headers };
    let body: any = data;

    if (!(data instanceof FormData)) {
      headers = {
        "Content-Type": "application/json",
        ...headers,
      };
      body = JSON.stringify(data);
    }

    const response = await fetchWithAuth(url, {
      method: "PUT",
      headers,
      body,
      ...options,
    });

    return response;
  },

  /**
   * Make a PATCH request
   * @param endpoint API endpoint (without base URL)
   * @param data Request body data
   * @param options Additional fetch options
   * @returns Promise with response
   */ patch: async (endpoint: string, data: any, options: RequestInit = {}) => {
    // Ensure proper URL construction by adding slash if needed
    const baseUrl = API_URL?.endsWith("/") ? API_URL : `${API_URL}/`;
    const cleanEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}${cleanEndpoint}`;

    // Determine if we're sending JSON or FormData
    let headers = { ...options.headers };
    let body: any = data;

    if (!(data instanceof FormData)) {
      headers = {
        "Content-Type": "application/json",
        ...headers,
      };
      body = JSON.stringify(data);
    }

    const response = await fetchWithAuth(url, {
      method: "PATCH",
      headers,
      body,
      ...options,
    });

    return response;
  },

  /**
   * Make a DELETE request
   * @param endpoint API endpoint (without base URL)
   * @param data Optional request body data
   * @param options Additional fetch options
   * @returns Promise with response
   */ delete: async (
    endpoint: string,
    data?: any,
    options: RequestInit = {},
  ) => {
    // Ensure proper URL construction by adding slash if needed
    const baseUrl = API_URL?.endsWith("/") ? API_URL : `${API_URL}/`;
    const cleanEndpoint = endpoint.startsWith("/")
      ? endpoint.slice(1)
      : endpoint;
    const url = `${baseUrl}${cleanEndpoint}`;

    let requestOptions: RequestInit = {
      method: "DELETE",
      ...options,
    };

    if (data) {
      // Determine if we're sending JSON or FormData
      let headers = { ...options.headers };
      let body: any = data;

      if (!(data instanceof FormData)) {
        headers = {
          "Content-Type": "application/json",
          ...headers,
        };
        body = JSON.stringify(data);
      }

      requestOptions = {
        ...requestOptions,
        headers,
        body,
      };
    }

    const response = await fetchWithAuth(url, requestOptions);

    return response;
  },
};
