import React, { useState, useRef } from "react";
import toast from "react-hot-toast";
import dynamic from "next/dynamic";
import { formatFileSize } from "@/utils/fileUtils";
import { PendingAttachmentList } from "./PendingAttachment";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import loadingAnimation from "../../../public/animations/loading.json";

interface EnhancedMessageInputProps {
  message: string;
  setMessage: (message: string) => void;
  attachments: File[] | null;
  setAttachments: (attachments: File[] | null) => void;
  onSendMessage: () => Promise<void> | void;
  onUserTyping: () => void;
  isConnected: boolean;
  disabled?: boolean;
}

export const EnhancedMessageInput: React.FC<EnhancedMessageInputProps> = ({
  message,
  setMessage,
  attachments,
  setAttachments,
  onSendMessage,
  onUserTyping,
  isConnected,
  disabled = false,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const selectedFiles = Array.from(files);

    // File validation logic here
    const maxSize = 10 * 1024 * 1024; // 10MB
    const oversizedFiles = selectedFiles.filter((file) => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      toast.error(
        `File troppo grandi: ${oversizedFiles.map((f) => f.name).join(", ")}. Dimensione massima: 10MB`,
      );
      return;
    }

    const totalSize = selectedFiles.reduce((acc, file) => acc + file.size, 0);
    const maxTotalSize = 50 * 1024 * 1024; // 50MB

    if (totalSize > maxTotalSize) {
      toast.error(
        `Dimensione totale troppo grande: ${formatFileSize(totalSize)}. Massimo: 50MB`,
      );
      return;
    }

    const allowedTypes = [
      "image/",
      "application/pdf",
      "text/",
      "application/msword",
      "application/vnd.openxmlformats-officedocument",
      "application/zip",
      "application/x-rar",
      "audio/",
      "video/",
    ];

    const invalidFiles = selectedFiles.filter(
      (file) => !allowedTypes.some((type) => file.type.startsWith(type)),
    );
    if (invalidFiles.length > 0) {
      toast.error(
        `Tipo di file non supportato: ${invalidFiles.map((f) => f.name).join(", ")}`,
      );
      return;
    }

    setAttachments(selectedFiles);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleRemoveAttachment = (index: number) => {
    if (attachments) {
      const newAttachments = attachments.filter((_, i) => i !== index);
      setAttachments(newAttachments.length > 0 ? newAttachments : null);
    }
  };

  const handleSendMessage = async () => {
    if (isSending || disabled) return;

    try {
      setIsSending(true);
      const result = onSendMessage();
      if (result instanceof Promise) {
        await result;
      }
    } catch (error) {
      console.error("Error sending message:", error);
    } finally {
      setIsSending(false);
    }
  };

  const canSend =
    (message.trim() || (attachments && attachments.length > 0)) &&
    !disabled &&
    !isSending;

  return (
    <div
      className={`border-t border-gray-200 bg-white ${isDragOver ? "bg-blue-50 border-blue-300" : ""}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* File drop overlay */}
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-100 bg-opacity-75 flex items-center justify-center z-10">
          <div className="text-blue-600 text-lg font-medium">
            Drop files here to attach
          </div>
        </div>
      )}

      {/* Attachments preview */}
      {attachments && attachments.length > 0 && (
        <div className="p-3 border-b border-gray-100">
          <PendingAttachmentList
            files={attachments}
            onRemoveFile={handleRemoveAttachment}
            layout="horizontal"
            size="small"
            maxDisplay={6}
          />
        </div>
      )}

      {/* Input area */}
      <div className="flex items-center space-x-2 p-3">
        {/* File attachment button */}
        <button
          onClick={() => fileInputRef.current?.click()}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          title="Attach file"
          disabled={disabled}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            fill="var(--accent)"
            className="w-6 h-6"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Z"
            />
          </svg>
        </button>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,application/pdf,.doc,.docx,.txt,.zip,.rar,audio/*,video/*"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />

        {/* Text input */}
        <div className="flex-1 relative">
          <input
            type="text"
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
              if (e.target.value.trim() && isConnected) {
                onUserTyping();
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter" && canSend) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            placeholder="Scrivi un messaggio..."
            disabled={disabled}
            className="w-full bg-gray-100 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
          />

          {/* Connection status indicator in input */}
          {!isConnected && (
            <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
              <div
                className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"
                title="Connecting..."
              ></div>
            </div>
          )}
        </div>

        {/* Send button */}
        <button
          onClick={handleSendMessage}
          disabled={!canSend}
          className={`p-2 rounded-full transition-all duration-200 ${
            canSend
              ? "cursor-pointer hover:bg-gray-100 active:scale-95"
              : "cursor-not-allowed opacity-50"
          }`}
          title="Send message"
        >
          {isSending ? (
            <Lottie
              animationData={loadingAnimation}
              loop={true}
              style={{
                width: "24px",
                height: "24px",
                filter:
                  "brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%) contrast(97%)",
              }}
            />
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill={canSend ? "var(--accent)" : "#ccc"}
              className="w-6 h-6"
            >
              <path d="M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z" />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
};
