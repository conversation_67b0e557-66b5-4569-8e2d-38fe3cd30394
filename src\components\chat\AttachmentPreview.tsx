import React, { useState } from "react";
import { SupportAttachment } from "@/types/support";
import {
  isImageFile,
  downloadFile,
  getFileIcon,
  getFileType,
  getFileColor,
  formatFileSize,
  getTruncatedFileName,
  canPreview,
} from "@/utils/fileUtils";

interface AttachmentPreviewProps {
  attachment: SupportAttachment;
  fileUrl?: string;
  isUserMessage?: boolean;
  showSize?: boolean;
  size?: "small" | "medium" | "large";
  onClick?: () => void;
}

export const AttachmentPreview: React.FC<AttachmentPreviewProps> = ({
  attachment,
  fileUrl,
  isUserMessage = false,
  showSize = true,
  size = "medium",
  onClick,
}) => {
  const [imageError, setImageError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const fileName = attachment.file_name;
  const url = fileUrl || attachment.file_url;
  const isImage = isImageFile(fileName) && !imageError;
  const fileType = getFileType(fileName);
  const fileColor = getFileColor(fileName);
  const truncatedName = getTruncatedFileName(
    fileName,
    size === "small" ? 15 : 25,
  );
  const canPreviewFile = canPreview(fileName);

  // Size configurations
  const sizeConfig = {
    small: {
      container: "w-16 h-16",
      image: "w-16 h-16",
      icon: "text-lg",
      text: "text-xs",
      padding: "p-1",
    },
    medium: {
      container: "w-20 h-20",
      image: "w-20 h-20",
      icon: "text-xl",
      text: "text-xs",
      padding: "p-2",
    },
    large: {
      container: "w-24 h-24",
      image: "w-24 h-24",
      icon: "text-2xl",
      text: "text-sm",
      padding: "p-2",
    },
  };

  const config = sizeConfig[size];

  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      downloadFile(url, fileName);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="relative group">
      <div
        className={`
          ${config.container} rounded-lg cursor-pointer transition-all duration-200 
          ${isHovered ? "transform scale-105 shadow-lg" : "shadow-sm"}
          ${isUserMessage ? "bg-white/20" : "bg-gray-50"}
          hover:shadow-md flex flex-col items-center justify-center
          border border-gray-200 hover:border-gray-300
        `}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        title={`${fileName}${showSize && attachment.file_size ? ` (${formatFileSize(attachment.file_size)})` : ""}`}
      >
        {/* File Content */}
        {isImage ? (
          <img
            src={url}
            alt={fileName}
            className={`${config.image} object-cover rounded-lg`}
            onError={handleImageError}
            loading="lazy"
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full">
            <div className={`${config.icon} mb-1`} style={{ color: fileColor }}>
              {getFileIcon(fileName)}
            </div>
            {size !== "small" && (
              <div
                className={`${config.text} text-center text-gray-700 max-w-full overflow-hidden`}
              >
                <div className="truncate">{truncatedName}</div>
                {showSize && attachment.file_size && (
                  <div className="text-gray-500 text-xs mt-1">
                    {formatFileSize(attachment.file_size)}
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Hover Overlay */}
        <div
          className={`
          absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 
          rounded-lg transition-all duration-200 flex items-center justify-center
          ${isHovered ? "opacity-100" : "opacity-0"}
        `}
        >
          <div className="text-white text-sm font-medium">
            {canPreviewFile ? "👁️ Anteprima" : "📥 Scarica"}
          </div>
        </div>

        {/* File Type Badge */}
        {size !== "small" && (
          <div
            className="absolute -top-1 -right-1 px-1 py-0.5 rounded text-xs font-bold text-white shadow-sm"
            style={{ backgroundColor: fileColor, fontSize: "8px" }}
          >
            {fileName.split(".").pop()?.toUpperCase()}
          </div>
        )}
      </div>

      {/* Loading State */}
      {!url && (
        <div
          className={`${config.container} absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center`}
        >
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400"></div>
        </div>
      )}
    </div>
  );
};

interface AttachmentListProps {
  attachments: SupportAttachment[];
  sentAttachmentUrls?: { [key: string]: string };
  isUserMessage?: boolean;
  layout?: "horizontal" | "vertical" | "grid";
  size?: "small" | "medium" | "large";
  maxDisplay?: number;
  onAttachmentClick?: (attachment: SupportAttachment, url: string) => void;
}

export const AttachmentList: React.FC<AttachmentListProps> = ({
  attachments,
  sentAttachmentUrls = {},
  isUserMessage = false,
  layout = "horizontal",
  size = "medium",
  maxDisplay,
  onAttachmentClick,
}) => {
  const displayAttachments = maxDisplay
    ? attachments.slice(0, maxDisplay)
    : attachments;
  const remainingCount =
    maxDisplay && attachments.length > maxDisplay
      ? attachments.length - maxDisplay
      : 0;

  const layoutClasses = {
    horizontal: "flex flex-row gap-2 overflow-x-auto",
    vertical: "flex flex-col gap-2",
    grid: "grid grid-cols-2 gap-2",
  };

  const handleAttachmentClick = (attachment: SupportAttachment) => {
    const url = attachment.file_url || sentAttachmentUrls[attachment.file_name];
    if (onAttachmentClick && url) {
      onAttachmentClick(attachment, url);
    }
  };

  if (attachments.length === 0) return null;

  return (
    <div className="mt-3">
      <div className={layoutClasses[layout]}>
        {displayAttachments.map((attachment, index) => (
          <AttachmentPreview
            key={`${attachment.id || index}-${attachment.file_name}`}
            attachment={attachment}
            fileUrl={
              attachment.file_url || sentAttachmentUrls[attachment.file_name]
            }
            isUserMessage={isUserMessage}
            size={size}
            onClick={() => handleAttachmentClick(attachment)}
          />
        ))}

        {/* Show remaining count */}
        {remainingCount > 0 && (
          <div
            className={`
            ${size === "small" ? "w-16 h-16" : size === "medium" ? "w-20 h-20" : "w-24 h-24"}
            rounded-lg bg-gray-100 border-2 border-dashed border-gray-300
            flex items-center justify-center cursor-pointer hover:bg-gray-200
            transition-colors duration-200
          `}
          >
            <div className="text-center">
              <div className="text-lg">📎</div>
              <div className="text-xs text-gray-600">+{remainingCount}</div>
            </div>
          </div>
        )}
      </div>

      {/* Attachment Summary */}
      {attachments.length > 1 && (
        <div className="mt-2 text-xs text-gray-500">
          {attachments.length} allegat{attachments.length === 1 ? "o" : "i"}
        </div>
      )}
    </div>
  );
};

export default AttachmentPreview;
