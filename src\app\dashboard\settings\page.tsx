"use client";
import React from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import Navbar from "@/components/_globals/navbar";
import HeaderPage from "@/components/_globals/headerPage";
import { useRouter } from "next/navigation";
import ButtonSection from "@/components/buttons/ButtonSection";
import LogoutButton from "@/components/auth/LogoutButton";

function Page() {
  const router = useRouter();

  return (
    <MobilePageStart>
      <HeaderPage
        title="Altro"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push("/dashboard");
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
        <ButtonSection
          completed={false}
          title="Account"
          href={`/dashboard/settings/account`}
        />
        <ButtonSection
          completed={true}
          title="Dashboard Finanziario"
          href={`/dashboard/financial`}
        />
        <ButtonSection
          completed={true}
          title="Pagamenti"
          href={`/dashboard/settings/payouts`}
        />
        <ButtonSection
          completed={true}
          title="Aiuto & Supporto"
          href={`/dashboard/settings/help`}
        />
        <ButtonSection
          completed={true}
          title="Contattaci"
          href={`/dashboard/chat/`}
        />
        <ButtonSection
          completed={true}
          title="Recensioni"
          href={`/dashboard/reviews/`}
        />

        {/* Spacer to push logout button to bottom */}
        <div className="flex-grow max-h-[200px]"></div>

        {/* Logout button at the bottom */}
        <LogoutButton
          text="Logout"
          backgroundColor="#113158"
          color="white"
          fontSize="15px"
          showIcon={true}
          containerStyle={{
            marginBottom: "10px", // Add space for the navbar
          }}
        />
      </div>
      <Navbar />
    </MobilePageStart>
  );
}

export default Page;
