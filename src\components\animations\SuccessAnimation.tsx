"use client";
import { useEffect, useState, useRef } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";

// Dynamically import <PERSON><PERSON> to avoid SSR issues
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

// Import the animation
import syncSuccessAnimation from "../../../public/animations/sync_success.json";

interface SuccessAnimationProps {
  onComplete: () => void;
  isVisible: boolean;
}

const SuccessAnimation = ({ onComplete, isVisible }: SuccessAnimationProps) => {
  const [isClient, setIsClient] = useState(false);
  const [animationCompleted, setAnimationCompleted] = useState(false);
  const [showButton, setShowButton] = useState(false);
  const lottieRef = useRef<any>(null);
  const router = useRouter();

  // Set up completion listener and fallback timer
  useEffect(() => {
    if (isVisible && isClient) {
      // Fallback timer to ensure completion callback runs
      // Animation should take about 3-4 seconds to complete
      const fallbackTimer = setTimeout(() => {
        console.log("Animation completion triggered by fallback timer");
        handleAnimationComplete();
      }, 3000); // 3 seconds fallback

      return () => clearTimeout(fallbackTimer);
    }
  }, [isVisible, isClient]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Set up a timer to show button 15 seconds after animation completes
  useEffect(() => {
    if (animationCompleted) {
      const buttonTimer = setTimeout(() => {
        setShowButton(true);
      }, 15000); // 15 seconds delay

      return () => clearTimeout(buttonTimer);
    }
  }, [animationCompleted]);

  const handleAnimationComplete = () => {
    setAnimationCompleted(true);
    onComplete();
  };

  const handleDashboardRedirect = () => {
    router.push("/dashboard");
  };

  if (!isClient || !isVisible) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        width: "100%",
        height: "100%",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#ffffff",
        zIndex: 9999,
      }}
    >
      <div style={{ width: "100%", maxWidth: "500px", height: "auto" }}>
        <Lottie
          lottieRef={lottieRef}
          animationData={syncSuccessAnimation}
          style={{ width: "100%", height: "100%" }}
          loop={false}
          onComplete={() => {
            console.log("Animation completed via event");
            handleAnimationComplete();
          }}
        />
      </div>
      <h2
        style={{
          marginTop: "20px",
          color: "#113158",
          fontWeight: "bold",
          textAlign: "center",
          padding: "0 20px",
        }}
      >
        Proprietà sincronizzata con successo!
      </h2>

      {/* Show "Attendere Prego..." after animation completes but before button */}
      {animationCompleted && !showButton && (
        <p
          style={{
            marginTop: "15px",
            color: "#666",
            textAlign: "center",
            padding: "0 20px",
            fontSize: "16px",
          }}
        >
          Attendere Prego...
        </p>
      )}

      {/* Dashboard redirect button - now shows after 15 second delay */}
      {showButton && (
        <button
          onClick={handleDashboardRedirect}
          style={{
            marginTop: "30px",
            padding: "12px 24px",
            backgroundColor: "#113158",
            color: "white",
            border: "none",
            borderRadius: "8px",
            fontWeight: "bold",
            cursor: "pointer",
            boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            transition: "all 0.3s ease",
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = "translateY(-2px)";
            e.currentTarget.style.boxShadow = "0 6px 8px rgba(0, 0, 0, 0.15)";
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = "translateY(0)";
            e.currentTarget.style.boxShadow = "0 4px 6px rgba(0, 0, 0, 0.1)";
          }}
        >
          Vai alla Dashboard
        </button>
      )}
    </div>
  );
};

export default SuccessAnimation;
