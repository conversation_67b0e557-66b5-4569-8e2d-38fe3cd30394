"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = React.useState({
    kids: false,
    childproof: false,
    playground: false,
    crib: false,
    electricsocketcover: false,
    childgate: false,
    highchair: false,
    changingtable: false,
    kidsplates: false,
    toys: false,
    books: false,
    babysitter: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      { name: "Kids", category: "Kids", is_available: data.kids ?? false },
      {
        name: "Childproof",
        category: "Kids",
        is_available: data.childproof ?? false,
      },
      {
        name: "Playground",
        category: "Kids",
        is_available: data.playground ?? false,
      },
      { name: "Crib", category: "Kids", is_available: data.crib ?? false },
      {
        name: "Electricsocketcover",
        category: "Kids",
        is_available: data.electricsocketcover ?? false,
      },
      {
        name: "Childgate",
        category: "Kids",
        is_available: data.childgate ?? false,
      },
      {
        name: "Highchair",
        category: "Kids",
        is_available: data.highchair ?? false,
      },
      {
        name: "Changingtable",
        category: "Kids",
        is_available: data.changingtable ?? false,
      },
      {
        name: "Kidsplates",
        category: "Kids",
        is_available: data.kidsplates ?? false,
      },
      { name: "Toys", category: "Kids", is_available: data.toys ?? false },
      { name: "Books", category: "Kids", is_available: data.books ?? false },
      {
        name: "Babysitter",
        category: "Kids",
        is_available: data.babysitter ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };
  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;

    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Bambini"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Bambini Accettati"
            value={data.kids}
            onChange={(value) => {
              setData({
                ...data,
                kids: value,
              });
              handleUpdateAmenities({
                ...data,
                kids: value,
              });
            }}
          />

          <InputOnOff
            title="A Prova di Bambino"
            description="Attivare, solo se si hanno preso le misure necessarie per la sicurezza dei bambini, cioè: prese elettriche coperte, porte per le scale, angoli arrotondati, ecc."
            value={data.childproof}
            onChange={(value) => {
              setData({
                ...data,
                childproof: value,
              });
              handleUpdateAmenities({
                ...data,
                childproof: value,
              });
            }}
          />

          <InputOnOff
            title="Parcogiochi"
            value={data.playground}
            onChange={(value) => {
              setData({
                ...data,
                playground: value,
              });
              handleUpdateAmenities({
                ...data,
                playground: value,
              });
            }}
          />

          <InputOnOff
            title="Casetta per Bambini"
            value={data.crib}
            onChange={(value) => {
              setData({
                ...data,
                crib: value,
              });
              handleUpdateAmenities({
                ...data,
                crib: value,
              });
            }}
          />

          <InputOnOff
            title="Copri Prese Elettriche"
            value={data.electricsocketcover}
            onChange={(value) => {
              setData({
                ...data,
                electricsocketcover: value,
              });
              handleUpdateAmenities({
                ...data,
                electricsocketcover: value,
              });
            }}
          />

          <InputOnOff
            title="Porte per Bambini"
            value={data.childgate}
            onChange={(value) => {
              setData({
                ...data,
                childgate: value,
              });
              handleUpdateAmenities({
                ...data,
                childgate: value,
              });
            }}
          />

          <InputOnOff
            title="Seggioloni"
            value={data.highchair}
            onChange={(value) => {
              setData({
                ...data,
                highchair: value,
              });
              handleUpdateAmenities({
                ...data,
                highchair: value,
              });
            }}
          />

          <InputOnOff
            title="Fasciatoio"
            value={data.changingtable}
            onChange={(value) => {
              setData({
                ...data,
                changingtable: value,
              });
              handleUpdateAmenities({
                ...data,
                changingtable: value,
              });
            }}
          />

          <InputOnOff
            title="Stoviglie per Bambini"
            value={data.kidsplates}
            onChange={(value) => {
              setData({
                ...data,
                kidsplates: value,
              });
              handleUpdateAmenities({
                ...data,
                kidsplates: value,
              });
            }}
          />

          <InputOnOff
            title="Giocattoli"
            value={data.toys}
            onChange={(value) => {
              setData({
                ...data,
                toys: value,
              });
              handleUpdateAmenities({
                ...data,
                toys: value,
              });
            }}
          />

          <InputOnOff
            title="Libri per Bambini"
            value={data.books}
            onChange={(value) => {
              setData({
                ...data,
                books: value,
              });
              handleUpdateAmenities({
                ...data,
                books: value,
              });
            }}
          />

          <InputOnOff
            title="Babysitter Disponibile"
            value={data.babysitter}
            onChange={(value) => {
              setData({
                ...data,
                babysitter: value,
              });
              handleUpdateAmenities({
                ...data,
                babysitter: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
