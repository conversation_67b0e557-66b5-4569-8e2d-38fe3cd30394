"use client";

import React from "react";
import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import Loader1 from "@/components/loaders/Loader1";
import { LogIn, Info, ShieldCheck } from "lucide-react";
import { motion } from "framer-motion";

// Dynamically load the ChekinWidget so we can show the Loader1 while both the component & SDK payload load
const ChekinWidget = dynamic(
  () => import("@/components/integrations/ChekinWidget"),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center py-16">
        <Loader1 className="w-28 h-28" />
      </div>
    ),
  },
);

interface PageProps {
  params: { propertyId: string };
  searchParams?: { reservationId?: string };
}

export default function PropertyChekinPage({
  params,
  searchParams,
}: PageProps) {
  const router = useRouter();
  const reservationId = searchParams?.reservationId;
  const hasReservation = Boolean(reservationId);

  return (
    <MobilePageStart>
      <HeaderPage
        title="Chekin"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() =>
          router.push(`/dashboard/property/${params.propertyId}`)
        }
      />

      <main className="p-4 space-y-6 bg-gradient-to-br from-slate-50 to-white min-h-[calc(100dvh-5rem)]">
        {/* Introduzione / Contesto */}
        <motion.section
          initial={{ opacity: 0, y: 12 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="max-w-4xl mx-auto"
        >
          <div className="flex items-start gap-3 mb-2">
            <div className="p-2 rounded-xl bg-indigo-100 text-indigo-600">
              <LogIn size={22} />
            </div>
            <div>
              <h1 className="text-xl font-semibold text-slate-800">
                Registrazione ospiti
              </h1>
              <p className="text-sm text-slate-500 leading-relaxed">
                Gestisci le connessioni con Polizia / ISTAT e raccogli i dati
                degli ospiti richiesti per la conformità normativa. Questo
                widget è fornito da Chekin e viene caricato in modo sicuro
                utilizzando le credenziali della tua struttura.
              </p>
            </div>
          </div>
          <div className="flex flex-wrap gap-3 mt-2">
            <StatusBadge
              icon={<ShieldCheck className="w-3.5 h-3.5" />}
              label="Sicuro"
              tone="emerald"
            />
            <StatusBadge
              icon={<Info className="w-3.5 h-3.5" />}
              label={
                hasReservation
                  ? `Prenotazione #${reservationId}`
                  : "Nessun contesto prenotazione"
              }
              tone={hasReservation ? "indigo" : "amber"}
            />
            <StatusBadge
              label={`Proprietà #${params.propertyId}`}
              tone="slate"
            />
          </div>
        </motion.section>

        {/* Scheda widget */}
        <motion.section
          initial={{ opacity: 0, y: 16 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.15, duration: 0.45 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200 shadow-sm overflow-hidden">
            <div className="p-4 md:p-6">
              <ChekinWidget
                propertyId={params.propertyId}
                reservationId={reservationId}
              />
            </div>
          </div>
        </motion.section>

        {/* Suggerimenti utili */}
        <motion.section
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="max-w-4xl mx-auto"
        >
          <ul className="text-xs md:text-sm text-slate-500 space-y-1 list-disc list-inside">
            <li>
              Le connessioni ai sistemi di Polizia / ISTAT avvengono all'interno
              del widget. Gli stati si aggiornano automaticamente.
            </li>
            <li>
              Se il widget non si carica, assicurati che pop‑up e script blocker
              siano disattivati e riprova.
            </li>
            <li>
              Il contesto della prenotazione restringe i dati richiesti. Senza
              prenotazione puoi comunque configurare la conformità della
              struttura.
            </li>
          </ul>
        </motion.section>
      </main>
    </MobilePageStart>
  );
}

// Componente badge interno leggero (co-localizzato per coesione della pagina)
interface StatusBadgeProps {
  label: string;
  tone?: "emerald" | "amber" | "indigo" | "slate";
  icon?: React.ReactNode;
}

function StatusBadge({ label, tone = "slate", icon }: StatusBadgeProps) {
  const toneMap: Record<string, string> = {
    emerald: "bg-emerald-50 text-emerald-600 ring-emerald-500/20",
    amber: "bg-amber-50 text-amber-600 ring-amber-500/20",
    indigo: "bg-indigo-50 text-indigo-600 ring-indigo-500/20",
    slate: "bg-slate-100 text-slate-600 ring-slate-500/10",
  };
  return (
    <span
      className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ring-1 ring-inset ${toneMap[tone]}`}
    >
      {icon}
      {label}
    </span>
  );
}
