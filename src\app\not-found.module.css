.container {
  width: 100%;
  min-height: calc(100vh - 60px);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  margin-top: 20px;
}

.content {
  max-width: 500px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.errorIcon {
  margin-bottom: 1rem;
}

.errorCode {
  font-size: 4.5rem;
  font-weight: 800;
  color: #113158;
  line-height: 1;
  margin-bottom: 0.5rem;
  position: relative;
}

.errorCode::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: #fcbd4c;
  border-radius: 2px;
}

.illustration {
  margin: 1rem 0;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #113158;
  margin-bottom: 1rem;
}

.description {
  font-size: 1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
  max-width: 90%;
}

.buttonContainer {
  width: 100%;
  max-width: 250px;
  margin-top: 0.5rem;
}
