import React from "react";
import { ConnectionStatus } from "@/types/support";

interface ConnectionStatusIndicatorProps {
  status: ConnectionStatus;
  className?: string;
}

export const ConnectionStatusIndicator: React.FC<
  ConnectionStatusIndicatorProps
> = ({ status, className = "" }) => {
  const getStatusColor = () => {
    if (status.isConnected) return "bg-green-500";
    if (status.error) return "bg-red-500";
    return "bg-yellow-500";
  };

  const getStatusIcon = () => {
    if (status.isConnected) {
      return (
        <svg
          className="w-3 h-3 text-white"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clipRule="evenodd"
          />
        </svg>
      );
    }
    if (status.error) {
      return (
        <svg
          className="w-3 h-3 text-white"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fillRule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      );
    }
    return (
      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
    );
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div
        className={`w-6 h-6 rounded-full flex items-center justify-center ${getStatusColor()}`}
      >
        {getStatusIcon()}
      </div>
    </div>
  );
};
