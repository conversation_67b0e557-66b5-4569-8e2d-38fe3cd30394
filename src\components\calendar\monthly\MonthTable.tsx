import React, { useState, useEffect, Change<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import styles from "./monthly.module.css";
import Button from "@/components/buttons/Button";
import { TagIcon } from "@heroicons/react/24/outline";
import { isToday as isTodayUtil } from "@/utils/dateUtils";

interface CalendarProps {
  onCurrentMonth?: (date: Date) => void;
  onSelectDay: (date: Date) => void;
  selectedStartDate?: Date | null;
  selectedEndDate?: Date | null;
  res?: any[];
  rates: any[];
}

const CalendarComponent = ({
  onCurrentMonth,
  onSelectDay,
  selectedEndDate,
  selectedStartDate,
  res,
  rates,
}: CalendarProps) => {
  //States
  const [currentDate, setCurrentDate] = useState(new Date());

  // Track the selected day in the calendar
  const [selectedDay, setSelectedDay] = useState<number | null>(null);
  const [reservations, setReservations] = useState<any[]>(
    res
      ? res.map((reservation) => {
          if (reservation.type === "reservation") {
            return {
              start: reservation.reservation_data.checkin_date.split("T")[0],
              end: reservation.reservation_data.checkout_date.split("T")[0],
              border: "green",
              background: "#46d11f16",
            };
          } else if (reservation.type === "block") {
            return {
              start: reservation.start_date,
              end: reservation.end_date,
              border: "red",
              background: "#ff000016",
            };
          }
        })
      : [],
  );

  //Calendar Handlers
  const getMonthName = (monthIndex: number): string => {
    const months = [
      "Gennaio",
      "Febbraio",
      "Marzo",
      "Aprile",
      "Maggio",
      "Giugno",
      "Luglio",
      "Agosto",
      "Settembre",
      "Ottobre",
      "Novembre",
      "Dicembre",
    ];
    return months[monthIndex];
  };
  const getDaysInMonth = (year: number, month: number): number => {
    return new Date(year, month + 1, 0).getDate();
  };
  const getFirstDayOfMonth = (year: number, month: number): number => {
    return new Date(year, month, 1).getDay();
  };
  const currentMonth = (day: number): boolean => {
    return day !== 0;
  };
  const prevMonth = (
    currentDate: Date,
    setCurrentDate: (date: Date) => void,
  ) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  const nextMonth = (
    currentDate: Date,
    setCurrentDate: (date: Date) => void,
  ) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  const isPast = (day: number, currentDate: Date): boolean => {
    const today = new Date();
    const selectedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
    );

    // create a date for the previous day before the current one (ie. yesterday)
    const previousDay = new Date(today);
    previousDay.setDate(today.getDate() - 1);

    // returns true if it's one day before the current date,
    return selectedDate < previousDay;
  };
  // Use utility function to check if a day is today
  const isToday = (day: number, currentDate: Date): boolean => {
    if (!day) return false;
    // Create a date object for the day we're checking
    const dateToCheck = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day,
    );

    // Use utility function for date comparison
    return isTodayUtil(dateToCheck);
  };
  // Check if a day is fully booked (currently always returns false)
  const isFull = (): boolean => {
    return false;
  };
  const getMonthDays = (year: number, month: number): number[][] => {
    // days of the month splitted up into weeks
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayOfMonth = getFirstDayOfMonth(year, month);
    const days: number[] = [];

    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    for (let i = 0; i < firstDayOfMonth; i++) {
      days.unshift(0);
    }

    const weeks: number[][] = [];
    let week: number[] = [];
    days.forEach((day, index) => {
      week.push(day);
      if ((index + 1) % 7 === 0 || index === days.length - 1) {
        weeks.push(week);
        week = [];
      }
    });

    return weeks;
  };

  useEffect(() => {
    onCurrentMonth && onCurrentMonth(currentDate);
  }, [currentDate]);

  useEffect(() => {
    setReservations(
      res
        ? res.map((reservation) => {
            if (reservation.type === "reservation") {
              return {
                start: reservation.reservation_data.checkin_date.split("T")[0],
                end: reservation.reservation_data.checkout_date.split("T")[0],
                border: "green",
                background: "#46d11f16",
              };
            } else if (reservation.type === "block") {
              return {
                start: reservation.start_date,
                end: reservation.end_date,
                border: "red",
                background: "#ff000016",
              };
            }
          })
        : [],
    );
  }, [res]);

  return (
    <section
      className={styles.section_calendar} /* style={{background: 'red'}} */
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          gap: "1rem",
          justifyContent: "space-between",
          width: "100%",
        }}
      >
        {/* Calendar section */}
        <div className={styles.calendar}>
          <div>
            <div className="flex flex-row justify-between items-center">
              <h2 className="font-bold tracking-wide">
                {`${getMonthName(currentDate.getMonth())} ${currentDate.getFullYear()}`}
              </h2>

              <div className={styles.calendar_controls}>
                <div className="w-[80px]">
                  <Button
                    onClick={() => prevMonth(currentDate, setCurrentDate)}
                    text={"Indietro"}
                    color="white"
                    backgroundColor="#00000070"
                  />
                </div>
                <div className="w-[80px]">
                  <Button
                    onClick={() => nextMonth(currentDate, setCurrentDate)}
                    text={"Avanti"}
                    color="white"
                    backgroundColor="#00000080"
                  />
                </div>
              </div>
            </div>
            <div className={styles.container_calendar}>
              <table className={styles.calendar_table}>
                <thead className={styles.calendar_head}>
                  <tr className={styles.calendar_head_container}>
                    <th className={styles.br}>Dom</th>
                    <th className={styles.th}>Lun</th>
                    <th className={styles.th}>Mar</th>
                    <th className={styles.th}>Mer</th>
                    <th className={styles.th}>Gio</th>
                    <th className={styles.th}>Ven</th>
                    <th className={styles.br_inverted}>Sab</th>
                  </tr>
                </thead>

                <tbody>
                  {getMonthDays(
                    currentDate.getFullYear(),
                    currentDate.getMonth(),
                  ).map((week, weekIndex) => {
                    return (
                      <tr className={styles.cont_days} key={weekIndex}>
                        {week.map((day, dayIndex) => {
                          const startDateSelected =
                            selectedStartDate && new Date(selectedStartDate);
                          startDateSelected &&
                            startDateSelected.setDate(
                              startDateSelected.getDate() + 1,
                            );
                          const dayDateSelected = new Date(
                            currentDate.getFullYear(),
                            currentDate.getMonth(),
                            day,
                          );

                          return (
                            <td
                              key={dayIndex}
                              className={`${
                                day === null || !currentMonth(day)
                                  ? styles.not_current_month
                                  : ""
                              } ${
                                day !== null && isToday(day, currentDate)
                                  ? styles.today
                                  : ""
                              } ${
                                day !== null &&
                                !isPast(day, currentDate) &&
                                isFull()
                                  ? styles.day_full
                                  : ""
                              } ${
                                day !== null && isPast(day, currentDate) // Verifica se il giorno è passato
                                  ? styles.past_day
                                  : styles.validChoice
                              }`}
                              style={{
                                background: selectedDay === day ? "white" : "",
                                color: selectedDay === day ? "black" : "",
                              }}
                            >
                              <div
                                className={`${
                                  day !== null && isPast(day, currentDate)
                                    ? styles.disabled
                                    : ""
                                }`}
                                style={{
                                  position: "relative",

                                  display: "flex",
                                  flexDirection: "column",
                                  justifyContent: "center",
                                  alignItems: "center",
                                }}
                                onClick={() =>
                                  day !== null &&
                                  !isPast(day, currentDate) &&
                                  onSelectDay(
                                    new Date(
                                      currentDate.getFullYear(),
                                      currentDate.getMonth(),
                                      day,
                                    ),
                                  )
                                }
                              >
                                <div
                                  style={{
                                    fontSize: "12px",
                                    transform: "translateY(-3px)",
                                    background: isToday(day, currentDate)
                                      ? "var(--blue)"
                                      : "",
                                    width: "20px",
                                    height: "20px",
                                    borderRadius: "50%",
                                    color: isToday(day, currentDate)
                                      ? "white"
                                      : "black",

                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    zIndex: "5",
                                  }}
                                >
                                  {day === null || day === 0 ? "" : day}
                                </div>

                                <p
                                  style={{
                                    fontSize: "10px",
                                    color: "#00000080",
                                    transform: "translateY(-8px)",
                                  }}
                                  className="mt-1"
                                >
                                  {rates && rates.length > 0
                                    ? rates.map((rate: any, index: number) => {
                                        const { rates } = rate;

                                        if (
                                          rate.date !==
                                          `${new Date(currentDate).getFullYear()}-${(new Date(currentDate).getMonth() + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`
                                        ) {
                                          return null;
                                        }

                                        const firstKey = Object.keys(rates)[0];
                                        const firstValue = rates[firstKey];

                                        return firstValue ? (
                                          <p key={index}>{firstValue} €</p>
                                        ) : null;
                                      })
                                    : "--- €"}
                                </p>

                                {reservations.map((reservation, index) => {
                                  const startDate = new Date(reservation.start);
                                  const endDate = new Date(reservation.end);
                                  endDate.setDate(endDate.getDate() - 1);

                                  const dayDate = new Date(
                                    currentDate.getFullYear(),
                                    currentDate.getMonth(),
                                    day,
                                  );
                                  return (
                                    <div key={index}>
                                      {new Date(reservation.start).getDate() ===
                                        day &&
                                        new Date(
                                          reservation.start,
                                        ).getMonth() ===
                                          currentDate.getMonth() &&
                                        new Date(
                                          reservation.start,
                                        ).getFullYear() ===
                                          currentDate.getFullYear() && (
                                          <div
                                            className={styles.reservationBar}
                                            style={{
                                              borderRadius: "20px 0 0 20px",
                                              width: "60%",
                                              transform:
                                                "translateY(-8px) translateX(20px)",

                                              borderTop: `${reservation.border} 1.5px solid`,
                                              borderBottom: `${reservation.border}  1.5px solid`,
                                              borderLeft: `${reservation.border}  1.5px solid`,

                                              display: "flex",
                                              justifyContent: "center",
                                              alignItems: "center",
                                              background: `${reservation.background}`,
                                            }}
                                          >
                                            <TagIcon
                                              width={12}
                                              color={`${reservation.border}`}
                                              style={{
                                                transform: "translateY",
                                              }}
                                            />
                                            <p
                                              className={styles.nameReservation}
                                            >
                                              {
                                                reservation.reservation_data
                                                  .guest_name
                                              }
                                            </p>
                                          </div>
                                        )}

                                      {
                                        // Check if the current day tile is between the start and end dates (inclusive)
                                        dayDate >= startDate &&
                                          dayDate < endDate &&
                                          // Ensure we're in the same year
                                          dayDate.getFullYear() ===
                                            startDate.getFullYear() && (
                                            <div
                                              className={styles.reservationBar}
                                              style={{
                                                width: "100%",
                                                borderTop: `${reservation.border} 1.5px solid`,
                                                borderBottom: `${reservation.border} 1.5px solid`,
                                                transform: "translateY(-8px)",
                                                background: `${reservation.background}`,
                                              }}
                                            />
                                          )
                                      }
                                      {new Date(reservation.end).getDate() ===
                                        day &&
                                        new Date(reservation.end).getMonth() ===
                                          currentDate.getMonth() &&
                                        new Date(
                                          reservation.end,
                                        ).getFullYear() ===
                                          currentDate.getFullYear() && (
                                          <div
                                            className={styles.reservationBar}
                                            style={{
                                              borderRadius: "0 20px 20px 0",
                                              width: "30%",

                                              borderTop: `${reservation.border} 1.5px solid`,
                                              borderBottom: `${reservation.border} 1.5px solid`,
                                              borderRight: `${reservation.border} 1.5px solid`,

                                              transform: "translateY(-8px)",
                                              background: `${reservation.background}`,
                                            }}
                                          />
                                        )}
                                    </div>
                                  );
                                })}

                                {selectedStartDate &&
                                  !selectedEndDate &&
                                  selectedStartDate.getDate() === day && (
                                    <div
                                      className={styles.selectedRange}
                                      style={{
                                        borderRadius: "10px",
                                      }}
                                    ></div>
                                  )}

                                {
                                  // Check if the current day tile is between the start and end dates (inclusive)
                                  startDateSelected &&
                                    selectedEndDate &&
                                    dayDateSelected >= startDateSelected &&
                                    dayDateSelected < selectedEndDate && (
                                      <div
                                        className={styles.selectedRange}
                                        style={{}}
                                      ></div>
                                    )
                                }
                                {selectedStartDate &&
                                  selectedEndDate &&
                                  selectedStartDate.getDate() === day &&
                                  selectedStartDate.getMonth() ===
                                    currentDate.getMonth() && (
                                    <div
                                      className={styles.selectedRange}
                                      style={{
                                        borderRadius: "10px 0 0 10px",
                                      }}
                                    ></div>
                                  )}

                                {selectedStartDate &&
                                  selectedEndDate &&
                                  selectedEndDate.getDate() === day &&
                                  selectedEndDate.getMonth() ===
                                    currentDate.getMonth() && (
                                    <div
                                      className={styles.selectedRange}
                                      style={{
                                        borderRadius: "0 10px 10px 0",
                                      }}
                                    ></div>
                                  )}
                              </div>
                            </td>
                          );
                        })}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
export default CalendarComponent;
