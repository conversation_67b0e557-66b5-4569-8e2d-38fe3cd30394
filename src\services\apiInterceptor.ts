import {
  getAccessToken,
  getRefreshToken,
  setAccessToken,
  setRefreshToken,
  isTokenExpired,
  clearTokens,
  hasValidTokenSetup,
  canAttemptTokenRefresh,
  incrementRefreshAttempt,
  resetRefreshAttempts,
} from "./tokenService";
import { refreshToken as apiRefreshToken } from "./api";

// Queue for requests that are waiting for token refresh
let isRefreshing = false;
let refreshQueue: Array<(token: string) => void> = [];

// Flag to track if initialization is in progress
let isInitializing = false;
let initializationPromise: Promise<boolean> | null = null;
// Queue for requests waiting for initialization
let initQueue: Array<() => void> = [];

/**
 * Process the queue of requests waiting for a new token
 * @param newToken The new access token
 */
const processQueue = (newToken: string): void => {
  refreshQueue.forEach((callback) => callback(newToken));
  refreshQueue = [];
};

/**
 * Process the queue of requests waiting for initialization
 */
const processInitQueue = (): void => {
  initQueue.forEach((callback) => callback());
  initQueue = [];
};

/**
 * Initialize authentication by checking for refresh token and getting a new access token if needed
 * @returns Promise that resolves to true if initialization was successful, false otherwise
 */
export const initializeAuthentication = async (): Promise<boolean> => {
  // If we already have a valid access token, no need to initialize
  if (getAccessToken() && !isTokenExpired()) {
    return true;
  }

  // If initialization is already in progress, return the existing promise
  if (isInitializing && initializationPromise) {
    return initializationPromise;
  }

  // Check if we have a refresh token
  const refreshTokenValue = getRefreshToken();
  if (!refreshTokenValue) {
    return false;
  }

  // Check if we're allowed to attempt a refresh (not in cooldown and not too many attempts)
  if (!canAttemptTokenRefresh()) {
    return false;
  }

  isInitializing = true;

  // Create a new initialization promise
  initializationPromise = new Promise<boolean>(async (resolve) => {
    try {
      // Increment the refresh attempt counter
      incrementRefreshAttempt();

      // Try to refresh the token
      const response = await apiRefreshToken(refreshTokenValue);

      if (response.access && response.refresh) {
        setAccessToken(response.access);
        setRefreshToken(response.refresh);

        // Reset refresh attempts on success
        resetRefreshAttempts();

        // Process any queued requests
        processInitQueue();

        isInitializing = false;
        resolve(true);
        return true;
      } else {
        // Invalid response
        isInitializing = false;
        resolve(false);
        return false;
      }
    } catch (error) {
      console.error("Authentication initialization failed:", error);
      clearTokens();
      isInitializing = false;
      resolve(false);
      return false;
    }
  });

  return initializationPromise;
};

/**
 * Add authorization header to fetch options
 * @param options Fetch options
 * @returns Updated fetch options with authorization header
 */
const addAuthHeader = (options: RequestInit = {}): RequestInit => {
  const token = getAccessToken();
  if (!token) return options;

  const headers = {
    ...options.headers,
    Authorization: `Bearer ${token}`,
  };

  return { ...options, headers };
};

/**
 * Handle token refresh if needed
 * @returns A promise that resolves to the new access token
 */
const handleTokenRefresh = async (): Promise<string> => {
  const refreshTokenValue = getRefreshToken();

  if (!refreshTokenValue) {
    clearTokens();
    throw new Error("No refresh token available");
  }

  // Check if we're allowed to attempt a refresh (not in cooldown and not too many attempts)
  if (!canAttemptTokenRefresh()) {
    throw new Error("Token refresh on cooldown or max attempts reached");
  }

  if (isRefreshing) {
    // If already refreshing, wait for the new token
    return new Promise((resolve) => {
      refreshQueue.push((token: string) => {
        resolve(token);
      });
    });
  }

  isRefreshing = true;

  try {
    // Increment the refresh attempt counter
    incrementRefreshAttempt();

    const response = await apiRefreshToken(refreshTokenValue);

    if (response.access && response.refresh) {
      setAccessToken(response.access);
      setRefreshToken(response.refresh);

      // Reset refresh attempts on success
      resetRefreshAttempts();

      // Process pending requests
      processQueue(response.access);

      return response.access;
    } else {
      throw new Error("Invalid refresh token response");
    }
  } catch (error) {
    // Clear tokens on refresh failure
    clearTokens();
    refreshQueue.forEach((callback) => callback(""));
    throw error;
  } finally {
    isRefreshing = false;
  }
};

/**
 * Enhanced fetch function with automatic token handling
 * @param url The URL to fetch
 * @param options Fetch options
 * @returns Promise with the fetch response
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
): Promise<Response> => {
  // Skip token refresh for certain endpoints to prevent loops
  const isAuthEndpoint =
    url.includes("/users/login/") ||
    url.includes("/users/refresh-token/") ||
    url.includes("/users/verify-token/");

  if (!isAuthEndpoint) {
    // Only try to refresh token if we have a valid setup and the token is expired or missing
    const hasToken = !!getAccessToken();
    const needsRefresh = hasToken && isTokenExpired();
    const needsInit = !hasToken && !!getRefreshToken();

    if (needsRefresh || needsInit) {
      try {
        // Try to get a valid token, but only if we're not in cooldown
        if (canAttemptTokenRefresh()) {
          if (needsInit) {
            await initializeAuthentication();
          } else if (needsRefresh) {
            await handleTokenRefresh();
          }
        }
      } catch (error) {
        console.error("Token refresh failed:", error);
        // Continue with the request anyway, it will fail with 401 if needed
      }
    }
  }

  // Add auth header and make the request
  const requestOptions = addAuthHeader(options);
  let response = await fetch(url, requestOptions);

  // Only retry on 401 for non-auth endpoints and if we have a refresh token
  // and we're not in cooldown
  if (
    !isAuthEndpoint &&
    response.status === 401 &&
    getRefreshToken() &&
    canAttemptTokenRefresh()
  ) {
    try {
      const newToken = await handleTokenRefresh();

      // Retry the original request with the new token
      const newOptions = {
        ...options,
        headers: {
          ...options.headers,
          Authorization: `Bearer ${newToken}`,
        },
      };

      return fetch(url, newOptions);
    } catch (error) {
      console.error("Token refresh failed on 401:", error);
      // Let the 401 response pass through
    }
  }

  return response;
};
