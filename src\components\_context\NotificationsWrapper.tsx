"use client";
import React, { ReactNode } from "react";
import { NotificationsProvider } from "./NotificationsContext";

interface NotificationsWrapperProps {
  children: ReactNode;
}

/**
 * A wrapper component that provides the NotificationsContext only to components that need it.
 * This helps optimize performance by not loading notifications data in pages where it's not needed.
 */
const NotificationsWrapper = ({ children }: NotificationsWrapperProps) => {
  return <NotificationsProvider>{children}</NotificationsProvider>;
};

export default NotificationsWrapper;
