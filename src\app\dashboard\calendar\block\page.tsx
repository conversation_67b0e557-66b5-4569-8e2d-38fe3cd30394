"use client";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { showNavbarSafeToast } from "@/components/toast/NavbarSafeToast";

import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import InputCalendar from "@/components/inputs/InputCalendar";
import CardAlert from "@/components/cards/CardAlert";
import LoaderWhite from "@/components/loaders/LoaderWhite";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import { createBookingBlock } from "@/services/api";
import { useCalendar } from "@/components/_context/CalendarContext";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Page />
    </Suspense>
  );
}

function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { clearCache } = useCalendar();

  // Get query parameters
  const startDate = searchParams.get("start_date");
  const endDate = searchParams.get("end_date");
  const property = searchParams.get("property");

  // Form states
  const [selectedStartDate, setSelectedStartDate] = useState(startDate ?? null);
  const [selectedEndDate, setSelectedEndDate] = useState(endDate ?? null);
  const [reason, setReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    startDate: "",
    endDate: "",
    reason: "",
    property: "",
  });

  // Validate that required parameters exist on mount
  useEffect(() => {
    if (!property) {
      showNavbarSafeToast({
        message: "Proprietà non specificata",
        type: "error",
      });
    }
  }, [property]);

  // Validate input fields
  const validateInputs = () => {
    const newErrors = {
      startDate: "",
      endDate: "",
      reason: "",
      property: "",
    };
    let isValid = true;

    if (!property) {
      newErrors.property = "Proprietà non specificata";
      isValid = false;
    }

    if (!selectedStartDate) {
      newErrors.startDate = "Data di inizio obbligatoria";
      isValid = false;
    }

    if (!selectedEndDate) {
      newErrors.endDate = "Data di fine obbligatoria";
      isValid = false;
    }

    // Check if start date is before end date
    if (selectedStartDate && selectedEndDate) {
      const start = new Date(selectedStartDate);
      const end = new Date(selectedEndDate);
      if (start > end) {
        newErrors.endDate =
          "La data di fine deve essere successiva alla data di inizio";
        isValid = false;
      }
    }

    // Optional validation for reason
    if (reason.trim().length > 500) {
      newErrors.reason = "Il motivo non può superare i 500 caratteri";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleBlockDates = async () => {
    // Validate inputs before proceeding
    if (!validateInputs()) {
      // Display first error found
      const firstError = Object.values(errors).find((error) => error !== "");
      if (firstError) {
        showNavbarSafeToast({
          message: firstError,
          type: "error",
        });
      }
      return false;
    }

    try {
      setIsLoading(true);

      if (!property || !selectedStartDate || !selectedEndDate) {
        throw new Error("All fields are required");
      }

      const response = await createBookingBlock(
        property,
        selectedStartDate,
        selectedEndDate,
        reason,
      );

      // Handle different response status codes
      if (response.status === 201 || response.status === 200) {
        // Success
        clearCache(); // Clear the calendar cache
        showNavbarSafeToast({
          message: "Blocco date creato con successo",
          type: "success",
        });
        setTimeout(() => {
          router.push("/dashboard/calendar");
        }, 1000);
        return true;
      } else if (response.status === 400) {
        // Bad request - handle validation errors
        const errorData = response.data;

        // Check for specific error messages
        if (errorData.end_date) {
          showNavbarSafeToast({
            message: `Errore: ${errorData.end_date[0]}`,
            type: "error",
          });
        } else if (errorData.start_date) {
          showNavbarSafeToast({
            message: `Errore: ${errorData.start_date[0]}`,
            type: "error",
          });
        } else if (errorData.property) {
          showNavbarSafeToast({
            message: `Errore: ${errorData.property[0]}`,
            type: "error",
          });
        } else if (errorData.reason) {
          showNavbarSafeToast({
            message: `Errore: ${errorData.reason[0]}`,
            type: "error",
          });
        } else {
          // Generic error message if we can't identify the specific error
          showNavbarSafeToast({
            message: "Errore di validazione. Controlla i dati inseriti.",
            type: "error",
          });
        }

        setIsLoading(false);
        return false;
      } else if (response.status === 403) {
        // Forbidden - permission error
        showNavbarSafeToast({
          message: "Non hai i permessi necessari per eseguire questa azione",
          type: "error",
        });
        setIsLoading(false);
        return false;
      } else {
        // Other errors
        showNavbarSafeToast({
          message: "Si è verificato un errore durante la creazione del blocco",
          type: "error",
        });
        setIsLoading(false);
        return false;
      }
    } catch (error) {
      console.error("Error creating booking block:", error);
      showNavbarSafeToast({
        message: "Si è verificato un errore durante la creazione del blocco",
        type: "error",
      });
      setIsLoading(false);
      return false;
    }
  };

  // Check if dates are within a valid range (optional validation)
  const isDateRangeValid = () => {
    if (!selectedStartDate || !selectedEndDate) return true;

    const start = new Date(selectedStartDate);
    const end = new Date(selectedEndDate);
    return start <= end;
  };

  return (
    <MobilePageStart>
      <HeaderPage
        title="Blocco date"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.back()}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col justify-between">
        <div className="flex flex-col gap-4">
          <InputCalendar
            label="Data inizio blocco"
            value={selectedStartDate ?? ""}
            onChange={(value) => {
              setSelectedStartDate(value);
              if (errors.startDate) {
                setErrors({ ...errors, startDate: "" });
              }
            }}
            error={errors.startDate}
            required
          />
          <InputCalendar
            label="Data fine blocco"
            value={selectedEndDate ?? ""}
            onChange={(value) => {
              setSelectedEndDate(value);
              if (errors.endDate) {
                setErrors({ ...errors, endDate: "" });
              }
            }}
            error={errors.endDate}
            required
          />

          {selectedStartDate && selectedEndDate && !isDateRangeValid() && (
            <CardAlert
              title="Errore"
              message="La data di fine deve essere successiva alla data di inizio"
              color="red"
            />
          )}

          <CardAlert
            title="Attenzione"
            message="Un altra riservazione, potrebbe terminare nella stessa data"
            color="orange"
            style={{
              marginTop: "20px",
            }}
          />

          <InputLabelDescription
            label="Motivo del blocco"
            value={reason}
            onChange={(value) => {
              setReason(value);
              if (errors.reason) {
                setErrors({ ...errors, reason: "" });
              }
            }}
            isTextArea
            error={errors.reason}
          />

          <ButtonLoading
            color="white"
            backgroundColor="var(--blue)"
            text="Salva"
            onClick={handleBlockDates}
            isLoading={isLoading}
          />
        </div>
      </div>
    </MobilePageStart>
  );
}
