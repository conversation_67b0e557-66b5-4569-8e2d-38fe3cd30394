import React, { useMemo } from "react";
import styles from "./calendar.module.css";

interface TileEventProps {
  name: string;
  dateStart: string;
  dateEnd: string;

  _id?: string;
  onClick?: () => void;
}

function TileEvent({ name, dateStart, dateEnd, _id, onClick }: TileEventProps) {
  const TILE_HEIGHT = 55; // If changed in CSS the TileHour height, change here too

  // Memoize calculations to improve performance
  const { height, verticalPosition, formattedTimeRange } = useMemo(() => {
    // Calculate height
    const startDate = new Date(dateStart);
    const endDate = new Date(dateEnd);

    const startHours = startDate.getHours() + startDate.getMinutes() / 60;
    const endHours = endDate.getHours() + endDate.getMinutes() / 60;
    const heightValue = endHours - startHours;

    // Calculate vertical position
    const yHours = startDate.getHours() * TILE_HEIGHT;
    const yMinutes = (startDate.getMinutes() * TILE_HEIGHT) / 60;
    const position = `${yHours + yMinutes}`;

    // Format time range
    const timeRange =
      startDate.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
      }) +
      " - " +
      endDate.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
      });

    return {
      height: `calc(${heightValue} * ${TILE_HEIGHT}px)`,
      verticalPosition: position,
      formattedTimeRange: timeRange,
    };
  }, [dateStart, dateEnd, TILE_HEIGHT]);

  return (
    <div
      onClick={onClick}
      className={styles.tileEvent}
      style={{
        top: `${verticalPosition}px`,
        height: height,
      }}
    >
      <div>
        <p className={styles.eventName}>{name}</p>
        <p className={styles.eventTime}>{formattedTimeRange}</p>
      </div>
    </div>
  );
}

export default TileEvent;
