"use client";
import React, { useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import Navbar from "@/components/_globals/navbar";
import HeaderPage from "@/components/_globals/headerPage";
import { useRouter } from "next/navigation";
import Title from "@/components/titles/Title";
import { getTemplates } from "@/services/api";
import { Template } from "@/services/api";
import Loader1 from "@/components/loaders/Loader1";
import TemplateCard from "@/components/templates/TemplateCard";
import styles from "./page.module.css";
import toast from "react-hot-toast";

function Page() {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [downloadingIds, setDownloadingIds] = useState<Set<number>>(new Set());
  const [expandedCards, setExpandedCards] = useState<Set<number>>(new Set());

  const MAX_DESCRIPTION_LENGTH = 80; // Characters limit for description

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const data = await getTemplates();
        console.log("Templates fetched:", data); // Add logging for debugging
        setTemplates(data || []); // Ensure we always set an array
      } catch (error) {
        console.error("Error fetching templates:", error);
        setTemplates([]); // Set empty array on error
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  const handleDownload = async (
    fileUrl: string,
    title: string,
    templateId: number,
  ) => {
    setDownloadingIds((prev) => new Set(prev).add(templateId));

    try {
      // Use our proxy API route to bypass CORS
      const proxyUrl = `/api/download?url=${encodeURIComponent(fileUrl)}&filename=${encodeURIComponent(title + ".pdf")}`;

      const response = await fetch(proxyUrl, {
        method: "GET",
      });

      if (!response.ok) {
        throw new Error("Failed to download file");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `${title}.pdf`;
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading file:", error);
      toast.error("Errore durante il download del file");
    } finally {
      setDownloadingIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(templateId);
        return newSet;
      });
    }
  };

  const toggleDescription = (templateId: number) => {
    setExpandedCards((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(templateId)) {
        newSet.delete(templateId);
      } else {
        newSet.add(templateId);
      }
      return newSet;
    });
  };

  return (
    <MobilePageStart isNavbar>
      <HeaderPage
        title="Templates"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push("/dashboard");
        }}
      />

      <div className={styles.templatesContainer}>
        <Title
          title="I nostri Templates"
          subtitle="Scarica i nostri template per gestire al meglio la tua proprietà"
        />

        {loading ? (
          <div className={styles.loadingContainer}>
            <Loader1 />
          </div>
        ) : (
          <div className={styles.templatesScrollArea}>
            <div className={styles.templatesGrid}>
              {templates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  isExpanded={expandedCards.has(template.id)}
                  isDownloading={downloadingIds.has(template.id)}
                  maxDescriptionLength={MAX_DESCRIPTION_LENGTH}
                  onToggleDescription={toggleDescription}
                  onDownload={handleDownload}
                />
              ))}
            </div>
          </div>
        )}

        {!loading && templates.length === 0 && (
          <div className={styles.emptyState}>
            <p>Nessun template disponibile al momento</p>
          </div>
        )}
      </div>
      <Navbar />
    </MobilePageStart>
  );
}

export default Page;
