"use client";
import React, { useContext, useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderLogo from "@/components/_globals/headerLogo";
import Navbar from "@/components/_globals/navbar";
import EnhancedMonthTable from "@/components/calendar/monthly/EnhancedMonthTable";
import InputSelectLabel from "@/components/inputs/inputSelectLabel";
import Button from "@/components/buttons/Button";
import Modal from "@/components/_globals/modal";
import BookingBlockModal from "@/components/modals/BookingBlockModal";
import { PropertyContext } from "@/components/_context/PropertyContext";
import { getMonthDailyRate } from "@/services/api";
import { useRouter } from "next/navigation";
import { useCalendar } from "@/components/_context/CalendarContext";

function Page() {
  const { properties, isLoadingProperties, fetchProperties } =
    useContext(PropertyContext);
  const router = useRouter();

  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedProperty, setSelectedProperty] = useState<any>(null);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [reservations, setReservations] = useState<any[]>([]);
  const [dayRates, setDayRates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const calculateStartAndEndofCurrentMonth = (currentDate: Date) => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);

    return {
      start: firstDay.toISOString().split("T")[0],
      end: lastDay.toISOString().split("T")[0],
    };
  };

  const handleClickCalendarDay = (date: Date) => {
    // If we don't have a start date yet, set it
    if (!startDate) {
      setStartDate(date);
      return;
    }

    // If we have a start date but no end date
    if (startDate && !endDate) {
      // If the new date is before the start date, swap them
      if (date < startDate) {
        setEndDate(startDate);
        setStartDate(date);
      } else {
        // Otherwise, set the end date normally
        setEndDate(date);
      }
      // Automatically open the edit modal when both dates are selected
      setIsEdit(true);
      return;
    }

    // If we already have both dates, start a new selection
    if (startDate && endDate) {
      setStartDate(date);
      setEndDate(null);
      setIsEdit(false); // Close the modal when starting a new selection
    }
  };

  // Use the calendar context for fetching data
  const { fetchBookings, fetchBlockedDates } = useCalendar();

  const handleFetchPropertyCalendar = async (currentDate: Date) => {
    if (!selectedProperty) return;

    const { start, end } = calculateStartAndEndofCurrentMonth(currentDate);
    const propertyId = selectedProperty.toString(); // Ensure it's a string
    setIsLoading(true);

    // Use the context methods to fetch data with caching
    const [bookings, blocked] = await Promise.all([
      fetchBookings(propertyId, start, end),
      fetchBlockedDates(propertyId, start, end),
    ]);

    // Map over the bookings array to add a 'type' property to each object
    // Filter out cancelled reservations
    const reservationsWithType = bookings
      .filter((booking: any) => booking.status !== "cancelled")
      .map((booking: any) => ({
        ...booking,
        type: "reservation",
      }));

    // Map over the blocked dates array to add a 'type' property to each object
    const blockedWithType = blocked.map((block: any) => ({
      ...block,
      type: "block",
    }));

    // Combine both arrays and update reservations
    setReservations([...reservationsWithType, ...blockedWithType]);
    setIsLoading(false);
  };

  const handleFetchPropertyDayRates = async (currentDate: Date) => {
    if (!selectedProperty) return;

    setIsLoading(true);
    const year = currentDate.getFullYear().toString();
    const month = (currentDate.getMonth() + 1).toString(); // getMonth() returns 0-11, so add 1
    const propertyId = selectedProperty.toString(); // Ensure it's a string

    try {
      const response = await getMonthDailyRate(propertyId, year, month);
      setDayRates(response.data || response);
    } catch (error) {
      console.error("Error fetching day rates:", error);
      setDayRates([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProperties();
  }, []);

  useEffect(() => {
    if (selectedProperty) {
      handleFetchPropertyCalendar(currentDate);
      handleFetchPropertyDayRates(currentDate);
    }
  }, [selectedProperty, currentDate]);

  const formatDate = (date: any) => {
    if (!date) return "";
    const d = new Date(date);
    const formattedDate = d.toLocaleDateString("en-GB", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
    return formattedDate.split("/").reverse().join("-"); // Convert "DD/MM/YYYY" to "YYYY-MM-DD"
  };

  return (
    <MobilePageStart isNavbar>
      <HeaderLogo />

      <div
        style={{
          width: "100%",
          padding: "0 20px",
          marginTop: "30px",
          scrollbarColor: "transparent transparent",
        }}
      >
        {isLoadingProperties ? (
          <div className="h-[400px] w-full flex flex-col items-center justify-center">
            <p className="text-[23px]">Caricamento proprietà...</p>
          </div>
        ) : (
          <>
            <InputSelectLabel
              label="Seleziona una proprietà"
              placeholder="Seleziona una proprietà"
              onSelect={setSelectedProperty}
              options={properties
                .filter(
                  (property: any) =>
                    property.is_onboarded && property.is_active,
                )
                .map((property: any) => ({
                  value: property.id,
                  label: property.name,
                }))}
              value={
                properties.find(
                  (property: any) => property.id === selectedProperty,
                )?.name ?? ""
              }
            />
            <br />

            {selectedProperty ? (
              <EnhancedMonthTable
                onCurrentMonth={(value) => {
                  setDayRates([]);
                  setCurrentDate(value);
                }}
                onSelectDay={handleClickCalendarDay}
                selectedStartDate={startDate}
                selectedEndDate={endDate}
                res={reservations}
                rates={dayRates}
                property={selectedProperty}
              />
            ) : (
              <div className="h-[400px] w-full flex flex-col items-center justify-center">
                <p className="text-[23px]">Seleziona una proprietà</p>
                <p className="text-[13px]">Nessuna proprietà attiva</p>
              </div>
            )}
            <br />
            {isEdit && (
              <Modal isOpen={isEdit} onClose={() => setIsEdit(false)}>
                <div className="flex flex-col gap-2 w-full">
                  <div className="mb-6">
                    <p style={{ fontSize: "13px" }}>
                      Entrata -{" "}
                      {startDate &&
                        startDate.toLocaleDateString("it-IT", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                    </p>

                    <p style={{ fontSize: "13px" }}>
                      Uscita -{" "}
                      {endDate &&
                        endDate.toLocaleDateString("it-IT", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })}
                    </p>
                  </div>

                  {startDate &&
                  endDate &&
                  startDate.getTime() === endDate.getTime() ? (
                    // Only show "Crea Stagione" button when dates are equal
                    <Button
                      text="Crea Stagione"
                      onClick={() => {
                        router.push(
                          `/dashboard/calendar/pricing?start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&property=${selectedProperty}`,
                        );
                      }}
                      color="white"
                      backgroundColor="var(--blue)"
                    />
                  ) : (
                    // Show buttons when dates are different
                    <>
                      <Button
                        text="Blocca Date"
                        onClick={() => {
                          router.push(
                            `/dashboard/calendar/block?start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&property=${selectedProperty}`,
                          );
                        }}
                        color="white"
                        backgroundColor="var(--blue)"
                      />

                      <Button
                        text="Prenotazione Manuale"
                        onClick={() => {
                          router.push(
                            `/dashboard/calendar/manual-reservation?start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&property=${selectedProperty}`,
                          );
                        }}
                        color="white"
                        backgroundColor="var(--blue)"
                      />

                      <Button
                        text="Crea Stagione"
                        onClick={() => {
                          router.push(
                            `/dashboard/calendar/pricing?start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&property=${selectedProperty}`,
                          );
                        }}
                        color="white"
                        backgroundColor="var(--blue)"
                      />
                    </>
                  )}

                  <br />
                  <Button
                    text="Chiudi"
                    onClick={() => {
                      setIsEdit(false);
                      setStartDate(null);
                      setEndDate(null);
                    }}
                    color="white"
                    backgroundColor="tomato"
                  />
                </div>
              </Modal>
            )}
          </>
        )}
      </div>

      <Navbar />
    </MobilePageStart>
  );
}

export default Page;
