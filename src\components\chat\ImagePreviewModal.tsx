import React from "react";
import { SupportAttachment } from "@/types/support";
import { downloadFile, formatFileSize } from "@/utils/fileUtils";

interface ImagePreviewModalProps {
  attachment: SupportAttachment;
  fileUrl: string;
  isOpen: boolean;
  onClose: () => void;
}

export const ImagePreviewModal: React.FC<ImagePreviewModalProps> = ({
  attachment,
  fileUrl,
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;

  const handleDownload = () => {
    downloadFile(fileUrl, attachment.file_name);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200 bg-white">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-medium text-gray-900 truncate">
              {attachment.file_name}
            </h3>
            {attachment.file_size && (
              <p className="text-sm text-gray-500 mt-1">
                {formatFileSize(attachment.file_size)}
              </p>
            )}
          </div>

          <div className="flex items-center gap-2 ml-4">
            <button
              onClick={handleDownload}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
              title="Scarica immagine"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </button>

            <button
              onClick={onClose}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors"
              title="Chiudi"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Image */}
        <div
          className="flex items-center justify-center bg-gray-50"
          style={{ maxHeight: "calc(90vh - 120px)" }}
        >
          <img
            src={fileUrl}
            alt={attachment.file_name}
            className="max-w-full max-h-full object-contain"
            style={{ maxHeight: "calc(90vh - 120px)" }}
          />
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <span>Clicca e trascina per spostare • Scroll per zoom</span>
            <span>
              {new Date(attachment.created_at).toLocaleString("it-IT")}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImagePreviewModal;
