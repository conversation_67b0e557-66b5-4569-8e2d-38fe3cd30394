"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import InputOnOff from "@/components/inputs/inputOnOff";
import Loader1 from "@/components/loaders/Loader1";
import {
  amenitiesCreateAndUpdate,
  amenitiesPropertyAmenities,
} from "@/services/api";
import { useRouter } from "next/navigation";

import React, { useEffect, useState } from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(true);
  const [data, setData] = React.useState({
    quiet: false,
    gardenview: false,
    riverview: false,
    cityview: false,
    landmarkview: false,
    beachview: false,
    waterfront: false,
    mountain: false,
    village: false,
    forest: false,
    countryside: false,
    closewater: false,
    secludedlocation: false,
    beachproximity: false,
  });

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: "quiet",
        category: "Position",
        is_available: data.quiet ?? false,
      },
      {
        name: "gardenview",
        category: "Position",
        is_available: data.gardenview ?? false,
      },
      {
        name: "riverview",
        category: "Position",
        is_available: data.riverview ?? false,
      },
      {
        name: "cityview",
        category: "Position",
        is_available: data.cityview ?? false,
      },
      {
        name: "landmarkview",
        category: "Position",
        is_available: data.landmarkview ?? false,
      },
      {
        name: "beachview",
        category: "Position",
        is_available: data.beachview ?? false,
      },
      {
        name: "waterfront",
        category: "Position",
        is_available: data.waterfront ?? false,
      },
      {
        name: "mountain",
        category: "Position",
        is_available: data.mountain ?? false,
      },
      {
        name: "village",
        category: "Position",
        is_available: data.village ?? false,
      },
      {
        name: "forest",
        category: "Position",
        is_available: data.forest ?? false,
      },
      {
        name: "countryside",
        category: "Position",
        is_available: data.countryside ?? false,
      },
      {
        name: "closewater",
        category: "Position",
        is_available: data.closewater ?? false,
      },
      {
        name: "secludedlocation",
        category: "Position",
        is_available: data.secludedlocation ?? false,
      },
      {
        name: "beachproximity",
        category: "Position",
        is_available: data.beachproximity ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(
      params.propertyId,
      dataAmenities,
    );
  };

  const handleFetchAmenities = async () => {
    if (!params.propertyId) return;

    const call = (await amenitiesPropertyAmenities(params.propertyId)) as any;

    if (call.status === 400) {
      setIsLoading(false);
      return;
    }

    // Update the data state based on the response
    const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) =>
          index === 0 ? match.toLowerCase() : match.toUpperCase(),
        ) // Convert to camelCase
        .replace(/\s+/g, ""); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});

    setData(fetchedData);
    setIsLoading(false);
  };

  useEffect(() => {
    handleFetchAmenities();
  }, []);

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title="Posizione"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
          <InputOnOff
            title="Posto Silenzioso"
            value={data.quiet}
            onChange={(value) => {
              setData({
                ...data,
                quiet: value,
              });
              handleUpdateAmenities({
                ...data,
                quiet: value,
              });
            }}
          />

          <InputOnOff
            title="Vista Giardino"
            value={data.gardenview}
            onChange={(value) => {
              setData({
                ...data,
                gardenview: value,
              });
              handleUpdateAmenities({
                ...data,
                gardenview: value,
              });
            }}
          />

          <InputOnOff
            title="Vista Fiume"
            value={data.riverview}
            onChange={(value) => {
              setData({
                ...data,
                riverview: value,
              });
              handleUpdateAmenities({
                ...data,
                riverview: value,
              });
            }}
          />

          <InputOnOff
            title="Vista Città"
            value={data.cityview}
            onChange={(value) => {
              setData({
                ...data,
                cityview: value,
              });
              handleUpdateAmenities({
                ...data,
                cityview: value,
              });
            }}
          />

          <InputOnOff
            title="Vista Punto di Interesse"
            value={data.landmarkview}
            onChange={(value) => {
              setData({
                ...data,
                landmarkview: value,
              });
              handleUpdateAmenities({
                ...data,
                landmarkview: value,
              });
            }}
          />

          <InputOnOff
            title="Vista Spiaggia"
            value={data.beachview}
            onChange={(value) => {
              setData({
                ...data,
                beachview: value,
              });
              handleUpdateAmenities({
                ...data,
                beachview: value,
              });
            }}
          />

          <InputOnOff
            title="Fronte Mare"
            value={data.waterfront}
            onChange={(value) => {
              setData({
                ...data,
                waterfront: value,
              });
              handleUpdateAmenities({
                ...data,
                waterfront: value,
              });
            }}
          />

          <InputOnOff
            title="Montagna"
            value={data.mountain}
            onChange={(value) => {
              setData({
                ...data,
                mountain: value,
              });
              handleUpdateAmenities({
                ...data,
                mountain: value,
              });
            }}
          />

          <InputOnOff
            title="Villaggio"
            value={data.village}
            onChange={(value) => {
              setData({
                ...data,
                village: value,
              });
              handleUpdateAmenities({
                ...data,
                village: value,
              });
            }}
          />

          <InputOnOff
            title="Foresta"
            value={data.forest}
            onChange={(value) => {
              setData({
                ...data,
                forest: value,
              });
              handleUpdateAmenities({
                ...data,
                forest: value,
              });
            }}
          />

          <InputOnOff
            title="Campagna"
            value={data.countryside}
            onChange={(value) => {
              setData({
                ...data,
                countryside: value,
              });
              handleUpdateAmenities({
                ...data,
                countryside: value,
              });
            }}
          />

          <InputOnOff
            title="Vicino all acqua"
            value={data.closewater}
            onChange={(value) => {
              setData({
                ...data,
                closewater: value,
              });
              handleUpdateAmenities({
                ...data,
                closewater: value,
              });
            }}
          />

          <InputOnOff
            title="Location Esclusiva"
            value={data.secludedlocation}
            onChange={(value) => {
              setData({
                ...data,
                secludedlocation: value,
              });
              handleUpdateAmenities({
                ...data,
                secludedlocation: value,
              });
            }}
          />

          <InputOnOff
            title="Vicino alla spiaggia"
            description="La spiagga è approssivativamente a meno di 3 km di distanza?"
            value={data.beachproximity}
            onChange={(value) => {
              setData({
                ...data,
                beachproximity: value,
              });
              handleUpdateAmenities({
                ...data,
                beachproximity: value,
              });
            }}
          />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
