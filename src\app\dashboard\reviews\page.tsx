"use client";
import React, { useContext, useEffect } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import { ReviewsContext } from "@/components/_context/ReviewsContext";
import CardReview from "@/components/cards/CardReview";
import Loader1 from "@/components/loaders/Loader1";
import EmptyReviews from "@/components/empty-states/EmptyReviews";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

function ReviewsPage() {
  const { reviews, isLoadingReviews, fetchReviews, markAsRead, markAllAsRead } =
    useContext(ReviewsContext);
  const router = useRouter();

  useEffect(() => {
    fetchReviews();
  }, []);

  const handleMarkAsRead = async (reviewId: string) => {
    const success = await markAsRead(reviewId);
    if (success) {
      toast.success("Recensione segnata come letta", {
        duration: 2000,
        style: {
          background: "#113158",
          color: "white",
        },
      });
    }
  };

  const handleMarkAllAsRead = async () => {
    const success = await markAllAsRead();
    if (success) {
      toast.success("Tutte le recensioni sono state segnate come lette", {
        duration: 3000,
        style: {
          background: "#fcbd4c",
          color: "#113158",
          fontWeight: "bold",
        },
      });
    } else {
      toast.error("Errore nel segnare le recensioni come lette", {
        duration: 3000,
      });
    }
  };

  const hasUnreadReviews = reviews.some((review) => !review.is_read);
  const unreadCount = reviews.filter((r) => !r.is_read).length;

  return (
    <MobilePageStart isNavbar>
      <HeaderPage
        title="Recensioni"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push("/dashboard");
        }}
      />
      <div className="px-4 py-6">
        {isLoadingReviews ? (
          <div className="h-full w-full flex items-center justify-center">
            <Loader1 />
          </div>
        ) : reviews.length > 0 ? (
          <div className="space-y-4">
            {hasUnreadReviews && (
              <div
                className="mb-6 p-4 rounded-lg border-l-4 shadow-sm"
                style={{
                  backgroundColor: "#f8f9fa",
                  borderLeftColor: "#fcbd4c",
                }}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p
                      className="text-sm font-bold"
                      style={{ color: "#113158" }}
                    >
                      Hai {unreadCount} nuove recensioni da leggere
                    </p>
                    <p className="text-xs text-gray-600 mt-1">
                      Segna tutte come lette per rimuovere le notifiche
                    </p>
                  </div>
                  <button
                    onClick={handleMarkAllAsRead}
                    className="text-xs font-bold py-2.5 px-5 rounded-lg transition-all duration-200 hover:opacity-90 hover:scale-105 whitespace-nowrap shadow-sm"
                    style={{ backgroundColor: "#fcbd4c", color: "#113158" }}
                  >
                    Segna tutte come lette
                  </button>
                </div>
              </div>
            )}
            {reviews.map((review) => (
              <CardReview
                key={review.id}
                id={review.id}
                publicReview={review.public_review}
                submittedAt={review.submitted_at}
                createdAt={review.created_at}
                reviewerRole={review.reviewer_role}
                ratings={review.ratings}
                isRead={review.is_read}
                onMarkAsRead={handleMarkAsRead}
              />
            ))}
          </div>
        ) : (
          <EmptyReviews />
        )}
      </div>
    </MobilePageStart>
  );
}

export default ReviewsPage;
