import React from 'react';
import { cn } from "@/utils/cn";
import { 
  UserPlus, 
  FileText, 
  Shield, 
  ShieldOff, 
  ShieldCheck, 
  ShieldAlert,
  BarChart3,
  TrendingDown,
  CheckCircle,
  AlertCircle,
  Clock,
  XCircle
} from 'lucide-react';
import type { LucideIcon } from "lucide-react";

export enum IconStatus {
  GUEST_CREATED = "guest_created",
  RESERVATION_UPDATED = "reservation_updated",
  POLICE_CONNECTED = "police_connected",
  POLICE_DISCONNECTED = "police_disconnected",
  POLICE_COMPLETE = "police_complete",
  POLICE_ERROR = "police_error",
  STAT_CONNECTED = "stat_connected",
  STAT_DISCONNECTED = "stat_disconnected",
  STAT_COMPLETE = "stat_complete",
  STAT_ERROR = "stat_error",
  UNKNOWN = "unknown"
}

export enum ProcessingStatus {
  PENDING = "pending",
  PROCESSED = "processed",
  FAILED = "failed"
}

const WEBHOOK_ICONS: Record<string, LucideIcon> = {
  guest_created: UserPlus,
  reservation_updated: FileText,
  police_connected: Shield,
  police_disconnected: ShieldOff,
  police_complete: ShieldCheck,
  police_error: ShieldAlert,
  stat_connected: BarChart3,
  stat_disconnected: TrendingDown,
  stat_complete: CheckCircle,
  stat_error: AlertCircle,
  unknown: Clock
};

const STATUS_VARIANTS = {
  processed: "green",
  pending: "yellow",
  failed: "red"
} as const;

const ICON_VARIANTS = {
  guest_created: "default",
  reservation_updated: "default", 
  police_connected: "green",
  police_disconnected: "red",
  police_complete: "green",
  police_error: "red",
  stat_connected: "green",
  stat_disconnected: "red",
  stat_complete: "green",
  stat_error: "red",
  unknown: "black"
} as const;

const variantStyles = {
  default: "border-blue-500 ring-4 ring-blue-500/20",
  yellow: "border-yellow-400 ring-4 ring-yellow-400/30",
  green: "border-green-500 ring-4 ring-green-500/30",
  black: "border-gray-600 ring-4 ring-gray-600/30",
  red: "border-red-500 ring-4 ring-red-500/30",
};

const iconColors = {
  default: "text-blue-600",
  yellow: "text-yellow-600",
  green: "text-green-600",
  black: "text-gray-600",
  red: "text-red-600",
};

interface ChekinStatusBadgeProps {
  iconStatus: string;
  processingStatus?: string;
  size?: 'sm' | 'md' | 'lg';
  showRing?: boolean;
  className?: string;
}

export const ChekinStatusBadge: React.FC<ChekinStatusBadgeProps> = ({ 
  iconStatus, 
  processingStatus = "processed",
  size = 'md',
  showRing = true,
  className 
}) => {
  const Icon = WEBHOOK_ICONS[iconStatus] || WEBHOOK_ICONS.unknown;
  const variant = ICON_VARIANTS[iconStatus as keyof typeof ICON_VARIANTS] || 'default';
  
  const sizeClasses = {
    sm: "p-2",
    md: "p-4", 
    lg: "p-6"
  };

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  };

  return (
    <div
      className={cn(
        "relative flex items-center justify-center rounded-full border-4 bg-white transition-all",
        sizeClasses[size],
        showRing ? variantStyles[variant] : `border-${variant === 'default' ? 'blue' : variant}-500`,
        className,
      )}
    >
      <Icon 
        className={cn(
          iconSizes[size], 
          iconColors[variant]
        )} 
        strokeWidth={1.5} 
      />
    </div>
  );
};

// Status text helper
export const getStatusText = (iconStatus: string): string => {
  const statusMap = {
    guest_created: 'Ospite Registrato',
    reservation_updated: 'Prenotazione Aggiornata',
    police_connected: 'Polizia Connessa',
    police_disconnected: 'Polizia Disconnessa',
    police_complete: 'Denuncia Alloggiati Completata',
    police_error: 'Errore Denuncia Alloggiati',
    stat_connected: 'ISTAT Connesso',
    stat_disconnected: 'ISTAT Disconnesso',
    stat_complete: 'Comunicazione ISTAT Completata',
    stat_error: 'Errore Comunicazione ISTAT',
    unknown: 'Stato Sconosciuto'
  };
  return statusMap[iconStatus as keyof typeof statusMap] || 'Stato Sconosciuto';
};

export default ChekinStatusBadge;