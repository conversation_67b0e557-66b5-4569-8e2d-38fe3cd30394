import React, { useState } from 'react';
import { BookingChekinStatus } from './BookingChekinStatus';
import { Info, HelpCircle } from 'lucide-react';

interface EnhancedChekinStatusProps {
  bookingId: string;
  propertyId?: string;
  compact?: boolean;
  className?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
}

export const EnhancedChekinStatus: React.FC<EnhancedChekinStatusProps> = ({
  bookingId,
  propertyId,
  compact = false,
  className = "",
  showLegend = true,
  showTooltip = true
}) => {
  const [showInfoModal, setShowInfoModal] = useState(false);

  const statusExplanations = {
    guest: "Indica se l'ospite è stato creato e registrato nel sistema Chekin",
    police: "Mostra lo stato della registrazione agli enti di polizia (Alloggiati)",
    stat: "Indica lo stato della registrazione ISTAT per le statistiche turistiche"
  };

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <BookingChekinStatus 
          bookingId={bookingId}
          propertyId={propertyId}
          compact={compact}
        />
        
        {showTooltip && (
          <button
            onClick={() => setShowInfoModal(true)}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            title="Informazioni sui stati"
          >
            <HelpCircle className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Status Legend */}
      {showLegend && compact && (
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-500">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Completato</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <span>In corso</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            <span>Non richiesto</span>
          </div>
        </div>
      )}

      {/* Info Modal */}
      {showInfoModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center gap-2 mb-4">
              <Info className="w-5 h-5 text-blue-600" />
              <h3 className="text-lg font-semibold text-gray-900">
                Stati Registrazione Chekin
              </h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Ospite</h4>
                <p className="text-sm text-gray-600">
                  {statusExplanations.guest}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Alloggiati</h4>
                <p className="text-sm text-gray-600">
                  {statusExplanations.police}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-1">ISTAT</h4>
                <p className="text-sm text-gray-600">
                  {statusExplanations.stat}
                </p>
              </div>
            </div>

            <div className="mt-6 pt-4 border-t border-gray-200">
              <h4 className="font-medium text-gray-900 mb-2">Legenda Colori:</h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Completato</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <span>In corso</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Errore</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                  <span>Non richiesto</span>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => setShowInfoModal(false)}
              className="mt-6 w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Chiudi
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedChekinStatus;