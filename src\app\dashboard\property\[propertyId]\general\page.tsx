"use client";
import React, { useEffect, useState } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import InputLabelDescription from "@/components/inputs/inputLabelDescription";
import { useRouter } from "next/navigation";
import InputSelectLabel from "@/components/inputs/inputSelectLabel";
import {
  metadataModifyPropertyInfo,
  metadataViewPropertyData,
  propertyUpdateProperty,
} from "@/services/api";
import Link from "next/link";
import toast from "react-hot-toast";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import Loader1 from "@/components/loaders/Loader1";
import CardAlert from "@/components/cards/CardAlert";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  const [name, setName] = useState("");
  const [propertyType, setPropertyType] = useState<number | null>(null);
  const [description, setDescription] = useState("");
  const [regionalId, setRegionalId] = useState("");
  const [propertyId, setPropertyId] = useState("");

  // Add state to track initial values
  const [initialValues, setInitialValues] = useState({
    name: "",
    propertyType: null as number | null,
    description: "",
    regionalId: "",
  });

  // Function to check if form has changes
  const hasChanges = () => {
    return (
      name !== initialValues.name ||
      propertyType !== initialValues.propertyType ||
      description !== initialValues.description ||
      regionalId !== initialValues.regionalId
    );
  };

  const handleFetchGeneral = async () => {
    const meta = await metadataViewPropertyData(params.propertyId);
    const propertyData = meta;

    // Set current values
    setName(propertyData.property.name);
    setPropertyType(propertyData.property.property_type);
    setPropertyId(propertyData.property?.id || params.propertyId);
    setDescription(propertyData.property.description);
    setRegionalId(propertyData.regional_id_code ?? "");

    // Set initial values
    setInitialValues({
      name: propertyData.property.name,
      propertyType: propertyData.property.property_type,
      description: propertyData.property.description,
      regionalId: propertyData.regional_id_code ?? "",
    });

    setIsLoading(false);
  };
  const handleUpdate = async () => {
    const handleUpdateSection1 = async () => {
      const call = await metadataModifyPropertyInfo(propertyId, regionalId);

      if (call.status === 200) {
        toast.success("Dati aggiornati con successo");
        router.push(`/dashboard/property/${params.propertyId}/`);
      }

      return true;
    };
    const handleUpdateSection2 = async () => {
      if (!propertyType) {
        return;
      }

      const call = await propertyUpdateProperty(
        params.propertyId,
        name,
        propertyType,
        description,
      );
    };

    const call1 = await handleUpdateSection1();
    const call2 = await handleUpdateSection2();

    return true;
  };

  useEffect(() => {
    handleFetchGeneral();
  }, []);

  return (
    <MobilePageStart>
      <HeaderPage
        title="Generali"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      {isLoading ? (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className="mt-4 px-4 h-full w-full flex flex-col gap-6">
          <div className="flex flex-col gap-4">
            <InputLabelDescription
              label="Nome Proprietà"
              description="Questo nome sarà visibile sulla piattaforma, e tutti i canali di distribuzione. Non includere testi promozionali"
              placeholder="Es. Tariffa Piena"
              value={name}
              onChange={setName}
            />

            <InputSelectLabel
              label="Tipo di Proprietà"
              value={
                [
                  { value: 1, label: "Hotel" },
                  { value: 2, label: "Motel" },
                  { value: 3, label: "Casa Vacanze" },
                ].find((item) => Number(item.value) == Number(propertyType))
                  ?.label as string
              }
              placeholder="Seleziona"
              onSelect={(value) => {
                setPropertyType(parseInt(value));
              }}
              options={[
                { value: "1", label: "Hotel" },
                { value: "2", label: "Motel" },
                { value: "3", label: "Casa Vacanze" },
              ]}
            />

            <CardAlert
              title="Dichiarazione CIN"
              message="Si dichiara che il CIN sia regolarmente richiesto secondo disposizioni di legge"
              color="orange"
            />

            <InputLabelDescription
              label="CIN (Codice Identificativo Nazionale)"
              isTextArea
              placeholder="Es: IT75CIN12345678"
              value={regionalId}
              onChange={setRegionalId}
              required={true}
              rules={[
                { type: "required", message: "Il codice IUN è obbligatorio" },
              ]}
            />
            <Link
              style={{
                color: "var(--accent)",
                fontSize: "12px",
                transform: "translateY(-10px)",
              }}
              href="/dashboard/chat"
            >
              Richiedi Assistenza per il CIN
            </Link>
          </div>

          <ButtonLoading
            color="white"
            backgroundColor="var(--blue)"
            text="Continua"
            deactive={!hasChanges()}
            onClick={() => {
              return handleUpdate();
            }}
          />
          <br />
        </div>
      )}
    </MobilePageStart>
  );
}

export default Page;
