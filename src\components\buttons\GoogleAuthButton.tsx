"use client";

import { useEffect, useState, useRef } from "react";
import { googleAuth } from "@/services/api";
import styles from "./GoogleAuthButton.module.css";

declare global {
  interface Window {
    google: any;
  }
}

interface GoogleAuthButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  text?: string;
}

const GoogleAuthButton = ({
  onSuccess,
  onError,
  text = "Sign in with Google",
}: GoogleAuthButtonProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleReady, setIsGoogleReady] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Load the Google Sign-In API script
    const script = document.createElement("script");
    script.src = "https://accounts.google.com/gsi/client";
    script.async = true;
    script.defer = true;
    document.head.appendChild(script);

    script.onload = initializeGoogleSignIn;

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, []);

  // Cleanup loading state and timeout on unmount
  useEffect(() => {
    return () => {
      setIsLoading(false);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const initializeGoogleSignIn = () => {
    if (window.google && window.google.accounts && window.google.accounts.id) {
      try {
        window.google.accounts.id.initialize({
          client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
          callback: handleGoogleSignIn,
          auto_select: false,
          // FedCM-compatible configuration
          cancel_on_tap_outside: false,
          // Add context parameter to help with COOP
          context: "signin",
          // Set ux_mode to popup for better COOP handling
          ux_mode: "popup",
          // Add itp_support for better cross-origin support
          itp_support: true,
        });
        setIsGoogleReady(true);
      } catch (error) {
        console.error("Failed to initialize Google Sign-In:", error);
        setIsGoogleReady(false);
      }
    } else {
      // Retry initialization after a short delay
      setTimeout(() => {
        if (
          window.google &&
          window.google.accounts &&
          window.google.accounts.id
        ) {
          initializeGoogleSignIn();
        }
      }, 100);
    }
  };

  const handleGoogleSignIn = async (response: any) => {
    try {
      // Clear the timeout since we got a response
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Note: isLoading is already true from handleClick
      // Google Identity Services provides an ID token that we send to the backend
      const { credential } = response;

      if (!credential) {
        throw new Error("No authentication credential received from Google");
      }

      // Send the ID token to your backend
      const authResponse = await googleAuth(credential);

      if (authResponse.status === 200) {
        onSuccess?.();
      } else {
        onError?.(authResponse.data?.error || "Google authentication failed");
        setIsLoading(false); // Reset loading state on error
      }
    } catch (error) {
      console.error("Google sign-in error:", error);
      onError?.("An error occurred during authentication");
      setIsLoading(false); // Reset loading state on error
    }
    // Note: We don't reset loading on success because the page will likely redirect
  };

  const handleClick = () => {
    // Prevent multiple clicks
    if (isLoading || !isGoogleReady) {
      return;
    }

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set loading state immediately when button is clicked
    setIsLoading(true);

    try {
      if (
        window.google &&
        window.google.accounts &&
        window.google.accounts.id
      ) {
        // Try to directly trigger the authentication without using prompt
        // This avoids the COOP postMessage issues
        try {
          // Use the more direct approach with credential request
          window.google.accounts.id.prompt();
        } catch (promptError) {
          console.warn(
            "Prompt method failed, trying alternative approach:",
            promptError,
          );

          // Alternative: Create a temporary div and render a button
          const tempDiv = document.createElement("div");
          tempDiv.style.position = "fixed";
          tempDiv.style.top = "-1000px";
          tempDiv.style.left = "-1000px";
          tempDiv.style.visibility = "hidden";
          tempDiv.style.pointerEvents = "none";
          document.body.appendChild(tempDiv);

          try {
            window.google.accounts.id.renderButton(tempDiv, {
              theme: "outline",
              size: "large",
              type: "standard",
              width: 300,
            });

            // Find and click the rendered button
            setTimeout(() => {
              const buttonElement = tempDiv.querySelector(
                '[role="button"]',
              ) as HTMLElement;
              if (buttonElement) {
                buttonElement.click();
              }
              // Clean up
              setTimeout(() => {
                if (document.body.contains(tempDiv)) {
                  document.body.removeChild(tempDiv);
                }
              }, 100);
            }, 100);
          } catch (renderError) {
            console.error("Render button failed:", renderError);
            document.body.removeChild(tempDiv);
            setIsLoading(false);
            onError?.("Failed to initialize Google authentication");
            return;
          }
        }

        // Set a timeout fallback to reset loading state
        timeoutRef.current = setTimeout(() => {
          setIsLoading(false);
        }, 8000); // Increased timeout for better UX
      } else {
        setIsLoading(false);
        onError?.("Google Sign-In is not available");
      }
    } catch (error) {
      console.error("Google Sign-In error:", error);
      setIsLoading(false);
      onError?.("Failed to show Google Sign-In");
    }
  };

  return (
    <button
      className={styles.googleButton}
      onClick={handleClick}
      disabled={isLoading || !isGoogleReady}
      type="button"
      aria-label="Sign in with Google"
    >
      {isLoading ? (
        <>
          <div className={styles.spinner}></div>
          <span className={styles.buttonText}>Caricamento...</span>
        </>
      ) : (
        <>
          <div className={styles.googleIconWrapper}>
            <svg
              className={styles.googleIcon}
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
            >
              <path
                fill="#EA4335"
                d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z"
              />
              <path
                fill="#4285F4"
                d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z"
              />
              <path
                fill="#FBBC05"
                d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z"
              />
              <path
                fill="#34A853"
                d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z"
              />
              <path fill="none" d="M0 0h48v48H0z" />
            </svg>
          </div>
          <span className={styles.buttonText}>{text}</span>
        </>
      )}
    </button>
  );
};

export default GoogleAuthButton;
