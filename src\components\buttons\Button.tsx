"use client";

import React, { useState } from "react";
import dynamic from "next/dynamic";
import style from "./button.module.css";

const Lottie = dynamic(() => import("lottie-react"), { ssr: false });
import loadingAnimation from "../../../public/animations/loading.json";

interface ButtonProps extends React.PropsWithChildren {
  /**
   * Function to call when the button is hovered
   */
  onHover?: () => void;

  /**
   * Function to call when the button is clicked
   * Should return a Promise<boolean> if it's an async operation
   * Return true to indicate success, false for error
   */
  onClick: () => void | Promise<boolean> | Promise<void>;

  /**
   * Optional icon to display before the text
   */
  icon?: React.ReactNode;

  /**
   * Text color of the button
   */
  color: string;

  /**
   * Background color of the button
   */
  backgroundColor: string;

  /**
   * Text to display on the button
   */
  text: string;

  /**
   * Font size of the button text
   */
  fontSize?: string;

  /**
   * Whether the button is disabled
   */
  disabled?: boolean;

  /**
   * Additional CSS classes to apply to the button
   */
  className?: string;

  /**
   * Border style for the button
   */
  border?: string;

  /**
   * Whether to show loading state for async operations
   */
  showLoading?: boolean;

  /**
   * Custom loading text to display during loading
   */
  loadingText?: string;
}

/**
 * A reusable button component with customizable styling and loading states
 */
function Button({
  onClick,
  onHover,
  icon,
  color,
  backgroundColor,
  text,
  fontSize,
  disabled = false,
  className = "",
  border,
  showLoading = true,
  loadingText,
}: ButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (isLoading || disabled) return;

    try {
      if (showLoading) {
        setIsLoading(true);
      }

      const result = onClick();

      // If onClick returns a Promise, wait for it
      if (result instanceof Promise) {
        await result;
      }
    } catch (error) {
      console.error("Button onClick error:", error);
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  const isDisabled = disabled || isLoading;

  return (
    <div
      onMouseEnter={onHover}
      onMouseLeave={onHover}
      className={`${style.component} ${className}`}
      style={{
        background: backgroundColor,
        color: color,
        cursor: isDisabled ? "not-allowed" : "pointer",
        opacity: isDisabled ? 0.7 : 1,
        border: border,
        pointerEvents: isDisabled ? "none" : "auto",
      }}
      onClick={handleClick}
    >
      {isLoading ? (
        <div className="flex items-center justify-center gap-2">
          <Lottie
            animationData={loadingAnimation}
            loop={true}
            style={{
              width: "20px",
              height: "20px",
              filter: "brightness(0) invert(1)", // Make it white to match button text
            }}
          />
          {loadingText && (
            <span style={{ fontSize: fontSize ?? "14px" }}>{loadingText}</span>
          )}
        </div>
      ) : (
        <>
          {icon && <span className={style.iconContainer}>{icon}</span>}
          <div
            style={{
              fontSize: fontSize ?? "14px",
            }}
          >
            {text}
          </div>
        </>
      )}
    </div>
  );
}

export default Button;
