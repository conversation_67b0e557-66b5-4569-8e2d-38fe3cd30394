"use client";
import React, { useEffect, useState } from "react";

import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderPage from "@/components/_globals/headerPage";
import { useRouter } from "next/navigation";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import { viewCollaborators, viewPropertyInvites } from "@/services/api";
import toast from "react-hot-toast";
import ButtonSection from "@/components/buttons/ButtonSection";
import Loader1 from "@/components/loaders/Loader1";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();
  const [collaborators, setCollaborators] = useState<any>([]);
  const [pendingInvites, setPendingInvites] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const handleGetCollaborators = async () => {
    try {
      const call = await viewCollaborators(params.propertyId);
      if (call.status === 200) {
        setCollaborators(call.data);
      } else {
        toast.error("Errore del server - Caricamento collaboratori fallito");
      }
    } catch (error) {
      toast.error("Impossibile caricare i collaboratori");
      console.error(error);
    }
  };

  const handleGetInvites = async () => {
    try {
      const call = await viewPropertyInvites(params.propertyId);
      if (call.status === 200) {
        setPendingInvites(call.data);
      } else {
        toast.error("Errore del server - Caricamento inviti fallito");
      }
    } catch (error) {
      toast.error("Impossibile caricare gli inviti");
      console.error(error);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        await Promise.all([handleGetCollaborators(), handleGetInvites()]);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [params.propertyId]);

  if (isLoading) {
    return (
      <MobilePageStart>
        <HeaderPage
          title="Permessi"
          actionLeftIcon={() => router.back()}
          actionRightIcon={() => router.push(`/dashboard`)}
        />
        <div className="flex justify-center items-center h-full w-full">
          <Loader1 />
        </div>
      </MobilePageStart>
    );
  }

  return (
    <MobilePageStart>
      <HeaderPage
        title="Permessi"
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push(`/dashboard`)}
      />

      <div className="mt-6 px-5 h-full w-full flex flex-col gap-8">
        {collaborators.length > 0 && (
          <div className="flex flex-col gap-3 bg-white rounded-lg p-4 shadow-sm">
            <h2 className="text-lg font-semibold text-[#113157]">
              Collaboratori
            </h2>
            <p className="text-sm text-gray-600 mb-1">
              Persone che hanno accesso alla proprietà
            </p>
            <div className="flex flex-col gap-3 mt-1">
              {collaborators.map((collaborator: any, index: number) => (
                <ButtonSection
                  key={collaborator.user_id || index}
                  completed={collaborator.permissions.includes("owner")}
                  deactivate={collaborator.permissions.includes("owner")}
                  title={`${collaborator.name} ${collaborator.permissions.includes("owner") ? "(Proprietario)" : ""}`}
                  desciption={collaborator.email}
                  href={`/dashboard/property/${params.propertyId}/permissions/${collaborator.user_id}`}
                  picture
                  noIcon={collaborator.permissions.includes("owner")}
                />
              ))}
            </div>
          </div>
        )}

        {pendingInvites.length > 0 && (
          <div className="flex flex-col gap-3 bg-white rounded-lg p-4 shadow-sm">
            <h2 className="text-lg font-semibold text-[#113157]">
              Inviti in sospeso
            </h2>
            <p className="text-sm text-gray-600 mb-1">
              Inviti inviati in attesa di accettazione
            </p>
            <div className="flex flex-col gap-3 mt-1">
              {pendingInvites.map((invite: any) => (
                <ButtonSection
                  key={invite.id}
                  completed={false}
                  deactivate={true}
                  title={invite.email}
                  desciption={`Scade il ${new Date(invite.expires_at).toLocaleDateString()}`}
                  picture={false}
                  noIcon={true}
                />
              ))}
            </div>
          </div>
        )}

        {collaborators.length === 0 && pendingInvites.length === 0 && (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 text-[#113157]"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-[#113157] mb-2">
              Nessun collaboratore
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              Aggiungi collaboratori per condividere l'accesso alla proprietà
            </p>
          </div>
        )}

        <div className="sticky bottom-6 px-1">
          <ButtonLoading
            text={"Aggiungi Collaboratore"}
            color="white"
            backgroundColor="var(--blue)"
            onClick={() => {
              router.push(
                `/dashboard/property/${params.propertyId}/permissions/new`,
              );
            }}
          />
        </div>
      </div>
    </MobilePageStart>
  );
}

export default Page;
