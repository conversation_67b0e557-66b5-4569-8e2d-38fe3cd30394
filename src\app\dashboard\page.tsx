"use client";
import React, { useContext, useEffect } from "react";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import HeaderLogo from "@/components/_globals/headerLogo";
import Navbar from "@/components/_globals/navbar";
import UserController from "@/components/_pages/home/<USER>";
import NoProperties from "@/components/_pages/home/<USER>";
import CardProperty from "@/components/cards/cardProperty";
import { UserContext } from "@/components/_context/UserContext";
import { PropertyContext } from "@/components/_context/PropertyContext";
import Button from "@/components/buttons/Button";
import { PlusIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import Loader1 from "@/components/loaders/Loader1";
import { DashboardUnreadProvider } from "@/components/_context/DashboardUnreadContext";
import styles from "./page.module.css";

function Page() {
  const { name, image, fetchProfile } = useContext(UserContext);
  const { properties, isLoadingProperties, fetchProperties } =
    useContext(PropertyContext);

  useEffect(() => {
    fetchProfile();
    fetchProperties();
  }, []);

  return (
    <DashboardUnreadProvider>
      <DashboardContent
        name={name}
        image={image}
        isLoadingProperties={isLoadingProperties}
        properties={properties}
      />
    </DashboardUnreadProvider>
  );
}

// Separate component that uses the DashboardUnreadContext
function DashboardContent({
  name,
  image,
  isLoadingProperties,
  properties,
}: {
  name: string | null;
  image: string | null;
  isLoadingProperties: boolean;
  properties: any[];
}) {
  const router = useRouter();

  return (
    <MobilePageStart isNavbar>
      <HeaderLogo />
      <UserController name={name ?? "--"} picture={image ?? ""} />

      <div className={styles.propertyCardsContainer}>
        {!isLoadingProperties &&
          Array.isArray(properties) &&
          properties.length > 0 &&
          properties.map((property: any) => (
            <CardProperty
              key={property.id}
              name={property.name}
              id={property.id}
              hotel_id={property.hotel_id}
              picture={property.photos}
              is_onboarded={property.is_onboarded}
              cover_image={property.cover_image}
              is_active={property.is_active !== false} // Pass is_active property
            />
          ))}
        {!isLoadingProperties &&
          Array.isArray(properties) &&
          properties.length > 0 && (
            <Button
              color="white"
              backgroundColor="var(--blue)"
              text="Aggiungi proprietà"
              icon={<PlusIcon width={17} />}
              fontSize="14px"
              onClick={() => {
                router.push("/dashboard/setup/name");
              }}
            />
          )}
      </div>
      {!isLoadingProperties &&
        (!Array.isArray(properties) || properties.length === 0) && (
          <NoProperties />
        )}

      {isLoadingProperties && (
        <div className="h-full w-full flex items-center justify-center">
          <Loader1 />
        </div>
      )}

      <Navbar />
    </MobilePageStart>
  );
}

export default Page;
