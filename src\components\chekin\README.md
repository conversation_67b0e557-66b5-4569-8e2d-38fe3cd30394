# Chekin Status Components

This directory contains components for displaying Chekin webhook status indicators based on the backend API documentation.

## Components

### ChekinStatusBadge

A fundamental status badge component that displays an icon with colored border and ring to indicate different Chekin webhook states.

```tsx
import { ChekinStatusBadge } from '@/components/chekin';

<ChekinStatusBadge 
  iconStatus="guest_created" 
  processingStatus="processed"
  size="md"
  showRing={true}
/>
```

**Props:**
- `iconStatus`: The type of Chekin event (guest_created, police_complete, etc.)
- `processingStatus`: Processing state (pending, processed, failed) - optional
- `size`: Badge size - 'sm', 'md', or 'lg'
- `showRing`: Whether to show the colored ring around the badge
- `className`: Additional CSS classes

**Supported Icon Status Values:**
- `guest_created` - Guest registration completed
- `reservation_updated` - Reservation information updated
- `police_connected` - Police reporting system connected
- `police_disconnected` - Police reporting system disconnected
- `police_complete` - Police registration completed
- `police_error` - Error in police registration
- `stat_connected` - ISTAT system connected
- `stat_disconnected` - ISTAT system disconnected
- `stat_complete` - ISTAT registration completed
- `stat_error` - Error in ISTAT registration
- `unknown` - Unknown or pending status

### ChekinStatusIndicator

A compact inline component that combines a status badge with optional text.

```tsx
import { ChekinStatusIndicator } from '@/components/chekin';

<ChekinStatusIndicator 
  iconStatus="police_complete" 
  showText={true}
  size="sm"
/>
```

**Props:**
- Same as ChekinStatusBadge plus:
- `showText`: Whether to display descriptive text next to the icon

### ChekinStatusPanel

A comprehensive panel component that displays all registration statuses for a booking.

```tsx
import { ChekinStatusPanel } from '@/components/chekin';

const status = {
  guest_created: true,
  guest_creation_date: '2024-01-15T10:30:00Z',
  police_connected: true,
  police_registration_status: 'complete',
  stat_connected: true,
  stat_registration_status: 'complete',
  has_active_guest: true,
  latest_status_update: '2024-01-15T14:30:00Z'
};

<ChekinStatusPanel status={status} />
```

**Status Object Properties:**
- `guest_created`: Whether a guest has been registered
- `guest_creation_date`: Date when guest was created (ISO string)
- `police_connected`: Whether police reporting is connected
- `police_registration_status`: Status of police registration ('pending', 'complete', 'error')
- `stat_connected`: Whether ISTAT reporting is connected
- `stat_registration_status`: Status of ISTAT registration ('pending', 'complete', 'error')
- `has_active_guest`: Whether there are active guests
- `latest_status_update`: Timestamp of last status update

## Usage Examples

### Basic Status Badges

```tsx
// Different status types
<ChekinStatusBadge iconStatus="guest_created" />
<ChekinStatusBadge iconStatus="police_complete" />
<ChekinStatusBadge iconStatus="stat_error" />

// Different sizes
<ChekinStatusBadge iconStatus="guest_created" size="sm" />
<ChekinStatusBadge iconStatus="guest_created" size="md" />
<ChekinStatusBadge iconStatus="guest_created" size="lg" />
```

### Inline Indicators

```tsx
// Compact usage in lists
<div className="flex items-center gap-2">
  <ChekinStatusIndicator iconStatus="guest_created" size="sm" />
  <span>Registration Complete</span>
</div>

// With descriptive text
<ChekinStatusIndicator 
  iconStatus="police_complete" 
  showText={true}
  size="md"
/>
```

### Status Panel

```tsx
// Complete registration status
const completeStatus = {
  guest_created: true,
  guest_creation_date: '2024-01-15T10:30:00Z',
  police_connected: true,
  police_registration_status: 'complete',
  stat_connected: true,
  stat_registration_status: 'complete',
  has_active_guest: true,
  latest_status_update: '2024-01-15T14:30:00Z'
};

<ChekinStatusPanel status={completeStatus} />

// Incomplete registration
const incompleteStatus = {
  guest_created: true,
  guest_creation_date: '2024-01-15T10:30:00Z',
  police_connected: false,
  police_registration_status: 'pending',
  stat_connected: false,
  stat_registration_status: 'pending',
  has_active_guest: true,
  latest_status_update: '2024-01-15T11:00:00Z'
};

<ChekinStatusPanel status={incompleteStatus} />
```

## Color Scheme

The components use a consistent color scheme:

- **Blue**: Guest-related events (default)
- **Green**: Successful connections and completions
- **Red**: Errors and disconnections
- **Yellow**: Pending states
- **Gray**: Unknown or inactive states

## Integration with Backend

These components are designed to work with the Chekin webhook backend API. The status data should be fetched from:

- `/api/integrations/chekin/webhooks/events/by_booking/` - For booking-specific status
- `/api/integrations/chekin/webhooks/events/by_property/` - For property-level status
- `/api/integrations/chekin/webhooks/events/` - For all webhook events

## Accessibility

All components include:
- Proper ARIA labels for screen readers
- Color-blind friendly design with icons and text
- Keyboard navigation support
- Semantic HTML structure

## Dependencies

- React 18+
- Lucide React icons
- Tailwind CSS for styling
- TypeScript support

## File Structure

```
src/components/chekin/
├── ChekinStatusBadge.tsx    # Core badge component
├── ChekinStatusIndicator.tsx # Inline indicator component
├── ChekinStatusPanel.tsx    # Comprehensive status panel
├── index.ts                 # Export all components
└── README.md               # This documentation
```