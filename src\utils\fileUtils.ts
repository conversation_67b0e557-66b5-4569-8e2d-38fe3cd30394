export const isImageFile = (
  fileName: string,
  contentType?: string,
): boolean => {
  if (contentType?.startsWith("image/")) {
    return true;
  }

  return fileName.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)$/i) !== null;
};

export const isVideoFile = (
  fileName: string,
  contentType?: string,
): boolean => {
  if (contentType?.startsWith("video/")) {
    return true;
  }

  return fileName.match(/\.(mp4|avi|mov|wmv|flv|webm|mkv|m4v)$/i) !== null;
};

export const isAudioFile = (
  fileName: string,
  contentType?: string,
): boolean => {
  if (contentType?.startsWith("audio/")) {
    return true;
  }

  return fileName.match(/\.(mp3|wav|flac|aac|ogg|wma|m4a)$/i) !== null;
};

export const isDocumentFile = (fileName: string): boolean => {
  return fileName.match(/\.(pdf|doc|docx|txt|rtf|odt)$/i) !== null;
};

export const isSpreadsheetFile = (fileName: string): boolean => {
  return fileName.match(/\.(xls|xlsx|csv|ods)$/i) !== null;
};

export const isPresentationFile = (fileName: string): boolean => {
  return fileName.match(/\.(ppt|pptx|odp)$/i) !== null;
};

export const isArchiveFile = (fileName: string): boolean => {
  return fileName.match(/\.(zip|rar|7z|tar|gz|bz2)$/i) !== null;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

export const downloadFile = (url: string, fileName: string): void => {
  try {
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName;
    link.target = "_blank";
    link.rel = "noopener noreferrer";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("Error downloading file:", error);
    // Fallback: open in new window
    window.open(url, "_blank", "noopener,noreferrer");
  }
};

export const getFileType = (fileName: string): string => {
  const extension = fileName.split(".").pop()?.toLowerCase() || "";

  if (isImageFile(fileName)) return "image";
  if (isVideoFile(fileName)) return "video";
  if (isAudioFile(fileName)) return "audio";
  if (isDocumentFile(fileName)) return "document";
  if (isSpreadsheetFile(fileName)) return "spreadsheet";
  if (isPresentationFile(fileName)) return "presentation";
  if (isArchiveFile(fileName)) return "archive";

  return "file";
};

export const getFileColor = (fileName: string): string => {
  const type = getFileType(fileName);
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (type) {
    case "image":
      return "#10B981"; // green
    case "video":
      return "#8B5CF6"; // purple
    case "audio":
      return "#F59E0B"; // amber
    case "document":
      if (extension === "pdf") return "#EF4444"; // red
      return "#3B82F6"; // blue
    case "spreadsheet":
      return "#059669"; // emerald
    case "presentation":
      return "#DC2626"; // red
    case "archive":
      return "#6B7280"; // gray
    default:
      return "#6B7280"; // gray
  }
};

export const getFileIcon = (fileName: string): string => {
  const extension = fileName.split(".").pop()?.toLowerCase();

  switch (extension) {
    // Documents
    case "pdf":
      return "📄";
    case "doc":
    case "docx":
      return "📝";
    case "txt":
      return "📃";
    case "rtf":
    case "odt":
      return "📄";

    // Spreadsheets
    case "xls":
    case "xlsx":
      return "📊";
    case "csv":
      return "📈";
    case "ods":
      return "📊";

    // Presentations
    case "ppt":
    case "pptx":
    case "odp":
      return "📊";

    // Archives
    case "zip":
    case "rar":
    case "7z":
    case "tar":
    case "gz":
    case "bz2":
      return "🗜️";

    // Audio
    case "mp3":
    case "wav":
    case "flac":
    case "aac":
    case "ogg":
    case "wma":
    case "m4a":
      return "🎵";

    // Video
    case "mp4":
    case "avi":
    case "mov":
    case "wmv":
    case "flv":
    case "webm":
    case "mkv":
    case "m4v":
      return "🎬";

    // Code files
    case "js":
    case "ts":
    case "jsx":
    case "tsx":
      return "💻";
    case "html":
    case "css":
    case "scss":
      return "🌐";
    case "json":
    case "xml":
      return "📋";

    default:
      return "📎";
  }
};

export const canPreview = (fileName: string): boolean => {
  return isImageFile(fileName) || fileName.match(/\.(pdf|txt)$/i) !== null;
};

export const getTruncatedFileName = (
  fileName: string,
  maxLength: number = 20,
): string => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split(".").pop();
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
  const truncatedName =
    nameWithoutExt.substring(0, maxLength - extension!.length - 4) + "...";

  return `${truncatedName}.${extension}`;
};
