"use client";

import HeaderPage from "@/components/_globals/headerPage";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import ButtonSection from "@/components/buttons/ButtonSection";
import { useRouter } from "next/navigation";
import React from "react";

interface PageProps {
  params: {
    propertyId: string;
  };
}

function Page({ params }: PageProps) {
  const router = useRouter();

  return (
    <MobilePageStart>
      <HeaderPage
        title="Panoramica"
        actionLeftIcon={() => {
          router.back();
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`);
        }}
      />

      <div className="mt-4 px-4 h-full w-full flex flex-col gap-3">
        <ButtonSection
          completed={false}
          title="Cucina"
          href={`/dashboard/property/${params.propertyId}/amenities/kitchen`}
        />

        <ButtonSection
          completed={true}
          title="Bagno"
          href={`/dashboard/property/${params.propertyId}/amenities/bathroom`}
        />

        <ButtonSection
          completed={true}
          title="Intrattenimento"
          href={`/dashboard/property/${params.propertyId}/amenities/entertainment`}
        />

        <ButtonSection
          completed={true}
          title="Bambini"
          href={`/dashboard/property/${params.propertyId}/amenities/kids`}
        />

        <ButtonSection
          completed={true}
          title="Piscina & Wellness"
          href={`/dashboard/property/${params.propertyId}/amenities/wellness`}
        />

        <ButtonSection
          completed={true}
          title="Servizi Aggiuntivi"
          href={`/dashboard/property/${params.propertyId}/amenities/miscellaneous`}
        />

        <ButtonSection
          completed={true}
          title="Accessibilità"
          href={`/dashboard/property/${params.propertyId}/amenities/accessibility`}
        />

        <ButtonSection
          completed={true}
          title="Posizione"
          href={`/dashboard/property/${params.propertyId}/amenities/position`}
        />

        <ButtonSection
          completed={true}
          title="Sostenibilità"
          href={`/dashboard/property/${params.propertyId}/amenities/sustainability`}
        />
      </div>
    </MobilePageStart>
  );
}

export default Page;
