"use client";

import styles from "../login/page.module.css";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import Input from "@/components/inputs/input";
import Button from "@/components/buttons/Button";
import Link from "next/link";
import { resetPassword, setPassword } from "@/services/api";
import toast from "react-hot-toast";
import InputHidden from "@/components/inputs/inputHidden";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Home />
    </Suspense>
  );
}

function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();

  //States

  const email = searchParams.get("email");
  const [password, setPasswordvalue] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");

  //Functions
  const handleSetPassword = async () => {
    if (!email) {
      toast.error("Email non valida");
      return;
    }

    if (!password) {
      toast.error("Password Obbligatoria");
      return;
    }

    if (!repeatPassword) {
      toast.error("Ripeti Password Obbligatorio");
      return;
    }

    if (password !== repeatPassword) {
      toast.error("Le password non corrispondono");
      return;
    }

    const response = await setPassword(email, password, repeatPassword);

    if (response) {
      toast.success("Email inviata con successo");
      router.push(`/auth/login?email=${email}`);
    }
  };

  //Effects
  useEffect(() => {}, []);

  return (
    <MobilePage>
      <h1 className={styles.title}>Imposta Password</h1>

      <form action="" className={styles.form}>
        <div className="w-full flex flex-col gap-4">
          <div
            className={styles.link4}
            style={{ textAlign: "center", padding: "0px 30px" }}
          >
            Imposta la password per il tuo account
          </div>
          <InputHidden
            placeholder="Password"
            value={password}
            onChange={setPasswordvalue}
          />
          <InputHidden
            placeholder="Ripeti Password"
            value={repeatPassword}
            onChange={setRepeatPassword}
          />

          <div className="flex justify-center">
            <Button
              text="Invia codice"
              onClick={() => {
                handleSetPassword();
              }}
              color="white"
              backgroundColor="var(--blue)"
              fontSize="15px"
            />
          </div>
        </div>
      </form>

      <div className={styles.link2}>
        Ricordi la password?{" "}
        <Link className={styles.link} href="/auth/login">
          Accedi
        </Link>
      </div>
    </MobilePage>
  );
}
