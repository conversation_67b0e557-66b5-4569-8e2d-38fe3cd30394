@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --card-background: #ebebed;

  --text-faded: #8c8c8d;
  --text: #202021;

  --accent: #fcb51f;
  --blue: #133157;

  --green: #4caf50;
  --red: #f44336;

  --text: #fff;
  --text-secondary: #a1a1a1;

  --status-background: #222;
  --dots-color: #222;
  --cup-color: #fff;
  --steam-color: #333;

  --text-xl: 1.5rem;
  --text-lg: 1.25rem;
  --text-md: 1rem;
  --text-sm: 0.875rem;
  --text-xs: 0.75rem;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  height: 100%;
  min-height: 100dvh;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--background);
  scrollbar-width: none;
  scrollbar-color: transparent transparent;
  -webkit-text-size-adjust: 100%;
}

body {
  color: rgb(var(--foreground-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

@media (max-width: 600px) {
  input {
    font-size: 16px; /* Make sure the font size is not too small for mobile */
  }
}

/* Ensure toasts are visible above modals */
.toast-container {
  z-index: 9999 !important;
}

/* For react-hot-toast */
:where([id^="toast-"]) {
  z-index: 9999 !important;
}
