"use client";

import styles from "../login/page.module.css";
import { Suspense, useEffect, useState } from "react";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import ToastCustom from "@/components/toast/Toast";
import { Mail } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import InputCode from "@/components/inputs/inputCode";
import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import { verifyEmail, resendVerificationEmail } from "@/services/api";
import { formatErrorMessage } from "@/utils/errorHandler";
import { showErrorToast } from "@/components/toast/ErrorToast";

export default function main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Verify />
    </Suspense>
  );
}

function Verify() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const emailFromParams = searchParams.get("email");

  const [isVerifiable, setIsVerifiable] = useState(false); //this checks if the user actuallu needs to verify the email (if not send back to login)
  const [isVerifing, setIsVerifing] = useState(false); //this checks if the user is currently verifying the email (loading the verification call)

  // Email state management
  const [email, setEmail] = useState(emailFromParams || "");
  const [showEmailInput, setShowEmailInput] = useState(!emailFromParams);
  const [emailInputValue, setEmailInputValue] = useState("");

  // Resend verification states
  const [isResending, setIsResending] = useState(false);
  const [showResendEmailInput, setShowResendEmailInput] = useState(false);
  const [resendEmailValue, setResendEmailValue] = useState("");

  const [code, setCode] = useState("");
  const [crono, setCrono] = useState(0);
  const maxTime = 300;

  /**
   * Validates email format
   */
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  /**
   * Validates verification code format (exactly 4 digits)
   */
  const isValidVerificationCode = (code: string): boolean => {
    return /^\d{4}$/.test(code);
  };

  const handleVerifyEmail = async () => {
    if (isVerifing) return;

    // Validate email
    if (!email || !isValidEmail(email)) {
      ToastCustom("error", "Email non valida");
      return;
    }

    // Validate verification code (must be exactly 4 digits)
    if (!isValidVerificationCode(code)) {
      ToastCustom("error", "Il codice deve essere di 4 cifre");
      setCode("");
      return;
    }

    setIsVerifing(true);

    const call = await verifyEmail(email, code);

    if (call.status === 200) {
      ToastCustom("success", "Email verificata con successo");
      setTimeout(() => {
        router.push("/auth/set-password?email=" + email);
      }, 3000);
    } else {
      ToastCustom("error", "Codice di verifica non valido");
      setCode("");
    }

    setIsVerifing(false);
  };

  const handleEmailSubmit = () => {
    if (!emailInputValue || !isValidEmail(emailInputValue)) {
      ToastCustom("error", "Inserisci un indirizzo email valido");
      return;
    }

    setEmail(emailInputValue);
    setShowEmailInput(false);
    setIsVerifiable(true);
  };

  const handleResendVerification = async () => {
    if (isResending) return;

    let emailToUse = email;

    // If resend email input is shown, use that email
    if (showResendEmailInput) {
      if (!resendEmailValue || !isValidEmail(resendEmailValue)) {
        ToastCustom("error", "Inserisci un indirizzo email valido");
        return;
      }
      emailToUse = resendEmailValue;
    }

    // If no email is available, show the input
    if (!emailToUse) {
      setShowResendEmailInput(true);
      return;
    }

    setIsResending(true);

    try {
      const response = await resendVerificationEmail(emailToUse);

      if (response.status === 200) {
        ToastCustom("success", "Email di verifica inviata con successo");
        setShowResendEmailInput(false);
        setResendEmailValue("");
        // Reset the timer
        setCrono(0);
      } else {
        // Handle structured error response
        const errorMessage = formatErrorMessage(response.data || {});
        showErrorToast({ message: errorMessage });
      }
    } catch (error: any) {
      console.error("Resend verification error:", error);
      const errorData = error?.response?.data || error?.message || {};
      const errorMessage = formatErrorMessage(errorData);
      showErrorToast({ message: errorMessage });
    } finally {
      setIsResending(false);
    }
  };

  const handleResendEmailSubmit = () => {
    if (!resendEmailValue || !isValidEmail(resendEmailValue)) {
      ToastCustom("error", "Inserisci un indirizzo email valido");
      return;
    }

    handleResendVerification();
  };

  //Start counting seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (crono >= maxTime) {
        clearInterval(interval);
        return;
      }
      setCrono(crono + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [crono]);

  // Auto-verify when code reaches exactly 4 digits
  useEffect(() => {
    if (isValidVerificationCode(code) && email) {
      handleVerifyEmail();
    }
  }, [code, email]);

  // Set verifiable state when email is available
  useEffect(() => {
    if (email && !showEmailInput) {
      setIsVerifiable(true);
    }
  }, [email, showEmailInput]);

  // Show email input prompt if no email in params
  if (showEmailInput) {
    return (
      <MobilePage>
        <h1 className={styles.title}>Inserisci la tua Email</h1>

        <div className={styles.link4} style={{ textAlign: "center" }}>
          Hai già ricevuto un codice di verifica? Inserisci la tua email per
          continuare.
        </div>

        <div className={styles.containerInputs}>
          <div style={{ marginBottom: "1rem" }}>
            <input
              type="email"
              placeholder="Inserisci la tua email"
              value={emailInputValue}
              onChange={(e) => setEmailInputValue(e.target.value)}
              style={{
                width: "100%",
                padding: "12px",
                borderRadius: "8px",
                border: "1px solid #ccc",
                fontSize: "16px",
              }}
              autoFocus
            />
          </div>

          <ButtonLoading
            text="Continua"
            color="white"
            backgroundColor="var(--blue)"
            onClick={handleEmailSubmit}
            deactive={!emailInputValue}
          />
        </div>
      </MobilePage>
    );
  }

  return (
    <MobilePage>
      <h1 className={styles.title}>Inserisci il Codice</h1>

      <div className={styles.link4} style={{ textAlign: "center" }}>
        Inserisci il codice di 4 cifre inviato al tuo indirizzo email
      </div>

      <div
        style={{
          fontSize: "1.125rem",
          color: "var(--text-secondary)",
          maxWidth: "540px",
          textAlign: "center",
          marginBottom: "2rem",
        }}
      >
        {isVerifiable || isVerifing ? (
          <>
            Codice di verifica inviato a:
            <br />
            <span style={{ borderBottom: ".5px solid #ffffff50" }}>
              {email}
            </span>
          </>
        ) : (
          <LoaderWhite />
        )}
      </div>

      <div className={styles.containerInputs}>
        <InputCode
          value={code}
          onChange={(value: string) => {
            // Ensure only numbers and max 4 digits
            const numericValue = value.replace(/\D/g, "").slice(0, 4);
            setCode(numericValue);
          }}
          onCompletion={() => {
            // Only verify if we have exactly 4 digits
            if (isValidVerificationCode(code)) {
              handleVerifyEmail();
            }
          }}
          autoFocus={true}
        />

        <ButtonLoading
          deactive={crono < maxTime || isResending}
          isLoading={isResending}
          text={
            <div className="flex flex-row gap-2 items-center">
              <Mail width={20} />
              <span className="font-semibold">Invia di nuovo</span>
            </div>
          }
          color="white"
          backgroundColor="var(--steam-color)"
          onClick={handleResendVerification}
        />

        <p
          style={{
            fontSize: ".825rem",
            color: "var(--text-secondary)",
            maxWidth: "540px",
            margin: "0.5rem auto",
            textAlign: "center",
          }}
        >
          {crono < maxTime &&
            `Puoi richiedere un codice nuovo tra ${Math.floor(
              (maxTime - crono) / 60,
            )
              .toString()
              .padStart(
                2,
                "0",
              )}:${((maxTime - crono) % 60).toString().padStart(2, "0")}`}
        </p>

        {/* Resend email input */}
        {showResendEmailInput && (
          <div style={{ marginTop: "1rem", width: "100%" }}>
            <div style={{ marginBottom: "0.5rem" }}>
              <input
                type="email"
                placeholder="Inserisci email per rinviare il codice"
                value={resendEmailValue}
                onChange={(e) => setResendEmailValue(e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px",
                  borderRadius: "8px",
                  border: "1px solid #ccc",
                  fontSize: "16px",
                }}
                autoFocus
              />
            </div>
            <div style={{ display: "flex", gap: "0.5rem" }}>
              <ButtonLoading
                text="Invia"
                color="white"
                backgroundColor="var(--blue)"
                onClick={handleResendEmailSubmit}
                deactive={!resendEmailValue || isResending}
                isLoading={isResending}
              />
              <button
                onClick={() => {
                  setShowResendEmailInput(false);
                  setResendEmailValue("");
                }}
                style={{
                  background: "none",
                  border: "1px solid #ccc",
                  color: "var(--text-secondary)",
                  padding: "12px 16px",
                  borderRadius: "8px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}
              >
                Annulla
              </button>
            </div>
          </div>
        )}

        {/* Option to change email */}
        {email && !showResendEmailInput && (
          <button
            onClick={() => {
              setShowEmailInput(true);
              setEmailInputValue("");
              setCode("");
            }}
            style={{
              background: "none",
              border: "none",
              color: "var(--blue)",
              textDecoration: "underline",
              cursor: "pointer",
              fontSize: "0.875rem",
              marginTop: "1rem",
            }}
          >
            Usa un'altra email
          </button>
        )}

        {/* Option to resend with different email */}
        {!showResendEmailInput && crono >= maxTime && (
          <button
            onClick={() => {
              setShowResendEmailInput(true);
              setResendEmailValue(email || "");
            }}
            style={{
              background: "none",
              border: "none",
              color: "var(--blue)",
              textDecoration: "underline",
              cursor: "pointer",
              fontSize: "0.875rem",
              marginTop: "0.5rem",
            }}
          >
            Invia a un'altra email
          </button>
        )}
      </div>
    </MobilePage>
  );
}
