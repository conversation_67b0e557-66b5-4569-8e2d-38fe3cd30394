import React from "react";
import styles from "./globals.module.css";
import { BookIcon, Calendar, HomeIcon, MoreHorizontal } from "lucide-react";

function Navbar() {
  return (
    <section className={styles.navbar}>
      <Tab icon={<HomeIcon width={18} />} title={"Home"} />

      <Tab icon={<BookIcon width={18} />} title={"Home"} />

      <Tab icon={<Calendar width={18} />} title={"Home"} />

      <Tab icon={<MoreHorizontal width={18} />} title={"Home"} />
    </section>
  );
}

export default Navbar;

interface TabProps {
  icon: React.ReactNode;
  title: string;
}
function Tab({ icon, title }: TabProps) {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        padding: "10px",
        borderRadius: "10px",
        cursor: "pointer",
      }}
    >
      {icon}
      <p style={{ fontSize: "11.5px" }}>{title}</p>
    </div>
  );
}
