export const roomType = [
  { label: "Appartamento", value: "1" },
  { label: "Quadrupla", value: "4" },
  { label: "Suite", value: "5" },
  { label: "<PERSON>la", value: "7" },
  { label: "<PERSON><PERSON><PERSON> con letti singoli", value: "8" },
  { label: "<PERSON><PERSON><PERSON>", value: "9" },
  { label: "Singola", value: "10" },
  { label: "Monolocale", value: "12" },
  { label: "Famiglia", value: "13" },
  { label: "Camera dormitorio", value: "25" },
  { label: "Letto in dormitorio", value: "26" },
  { label: "Bungalow", value: "27" },
  { label: "Chalet", value: "28" },
  { label: "Casa vacanze", value: "29" },
  { label: "Villa", value: "31" },
  { label: "Casa mobile", value: "32" },
  { label: "Tenda", value: "33" },
  { label: "Piazzola con/senza elettricità", value: "34" },
  { label: "King", value: "35" },
  { label: "Queen", value: "36" },
];

// Room types that should only allow one bed configuration (single room)
export const singleRoomTypes = [
  "4", // Quadrupla
  "5", // Suite
  "7", // Tripla
  "9", // Doppia
  "10", // Singola
  "12", // Monolocale
  "25", // Camera dormitorio
  "26", // Letto in dormitorio
  "35", // King
  "36", // Queen
];

// Helper function to check if a room type is restricted to a single room
export const isSingleRoomType = (roomTypeValue: string): boolean => {
  return singleRoomTypes.includes(roomTypeValue);
};
