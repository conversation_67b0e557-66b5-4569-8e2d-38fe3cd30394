import React from "react";
import styles from "./modal.module.css";
import Modal from "../_globals/modal";
import Title from "../titles/Title";
import CardAlert from "../cards/CardAlert";

interface ModalNewUserProps {
  isOpen: boolean;
  onClose: () => void;
}

function ModalNewUser({ isOpen, onClose }: ModalNewUserProps) {
  return (
    <div>
      {isOpen && (
        <Modal isOpen={isOpen} onClose={onClose} width={"50%"}>
          <Title
            title={"Basic information"}
            subtitle={"Fill in the fields below to create a new user"}
          />
          <CardAlert
            title={"User creation"}
            message={
              "Below you can see the fields to create a new user. Fill in all the fields and click on the create button."
            }
            learnMoreLink="/"
            color="green"
          />
        </Modal>
      )}
    </div>
  );
}

export default ModalNewUser;
