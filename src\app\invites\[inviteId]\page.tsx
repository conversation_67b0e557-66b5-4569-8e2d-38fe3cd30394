"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { viewInvite, acceptInvite, deleteToken } from "@/services/api";
import { toast } from "react-hot-toast";
import MobilePageStart from "@/components/_globals/mobilePageStart";
import Header<PERSON><PERSON> from "@/components/_globals/headerLogo";
import Loader1 from "@/components/loaders/Loader1";
import Button from "@/components/buttons/Button";
import {
  Calendar,
  User,
  Building,
  Clock,
  CheckCircle,
  AlertCircle,
} from "lucide-react";

interface Invite {
  id: string;
  property_name: string;
  email: string;
  invited_by: string;
  created_at: string;
  expires_at: string;
  accepted: boolean;
  is_registered: boolean;
  accepted_at: string | null;
}

function Page({ params }: { params: { inviteId: string } }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [invite, setInvite] = useState<Invite | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<string>("");

  useEffect(() => {
    const handleFetchInvite = async () => {
      try {
        const call = await viewInvite(params.inviteId);
        if (call.status === 200) {
          setInvite(call.data);

          // Keep loading state true while handling registration redirect
          if (!call.data.is_registered) {
            router.push(`/auth/register?invite_id=${call.data.id}`);
            return;
          }

          // Calculate time remaining until expiration
          const expiresAt = new Date(call.data.expires_at).getTime();
          const now = new Date().getTime();

          if (expiresAt < now) {
            setError("Questo invito è scaduto");
          }
          setIsLoading(false); // Only set loading to false after all checks
        } else {
          setError("Invito non trovato o scaduto");
          toast.error("Errore durante il caricamento dell'invito");
          setIsLoading(false);
          router.push("/dashboard");
        }
      } catch (err) {
        setError(
          "Si è verificato un errore durante il caricamento dell'invito",
        );
        toast.error("Errore di connessione");
        setIsLoading(false);
      }
    };
    handleFetchInvite();
  }, [params.inviteId, router]);

  // Update the time remaining every minute
  useEffect(() => {
    if (!invite) return;

    const updateTimeLeft = () => {
      const expiresAt = new Date(invite.expires_at).getTime();
      const now = new Date().getTime();
      const diff = expiresAt - now;

      if (diff <= 0) {
        setTimeLeft("Scaduto");
        setError("Questo invito è scaduto");
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
      );
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeLeft(`${days} giorni, ${hours} ore`);
      } else if (hours > 0) {
        setTimeLeft(`${hours} ore, ${minutes} minuti`);
      } else {
        setTimeLeft(`${minutes} minuti`);
      }
    };

    updateTimeLeft();
    const interval = setInterval(updateTimeLeft, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [invite]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("it-IT", {
      day: "numeric",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleAcceptInvite = async () => {
    if (invite) {
      try {
        const response = await acceptInvite(invite.id);
        if (response.status === 200) {
          toast.success("Invito accettato con successo!");
          router.push("/dashboard");
        } else {
          toast.error("Errore durante l'accettazione dell'invito");
        }
      } catch (err) {
        toast.error("Errore di connessione");
      }
    }
  };

  const handleInviteNavigation = async () => {
    if (!invite) return;

    if (invite.is_registered) {
      // For registered users, try to accept invite if authenticated
      try {
        const response = await acceptInvite(invite.id);
        if (response.status === 200) {
          toast.success("Invito accettato con successo!");
          router.push("/dashboard");
        } else if (response.status === 401) {
          // If unauthorized, redirect to login with return path
          const returnPath = encodeURIComponent(`/invites/${params.inviteId}`);
          deleteToken(); // Clear any existing token
          toast.error(
            "Sessione scaduta, effettua il login per accettare l'invito",
          );
          router.push(`/auth/login?redirect=${returnPath}`);
        } else {
          toast.error("Errore durante l'accettazione dell'invito");
        }
      } catch (err) {
        toast.error("Errore di connessione");
      }
    } else {
      // For unregistered users, redirect to register with invite_id
      router.push(`/auth/register?invite_id=${invite.id}`);
    }
  };

  // Loading state
  if (isLoading || !invite) {
    return (
      <MobilePageStart isNavbar={false}>
        <HeaderLogo />
        <div className="w-full min-h-screen flex items-center justify-center px-4 py-6">
          <Loader1 />
        </div>
      </MobilePageStart>
    );
  }

  // Error state
  if (error) {
    return (
      <MobilePageStart isNavbar={false}>
        <HeaderLogo />
        <div className="w-full min-h-screen flex items-center justify-center px-4 py-6">
          <div className="bg-white rounded-xl shadow-lg p-6 w-full max-w-md">
            <div className="flex justify-center mb-6">
              <AlertCircle className="h-16 w-16 text-red-500" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-4 text-center">
              Oops!
            </h1>
            <p className="text-gray-600 mb-8 text-center">{error}</p>
            <Button
              text="Torna alla home"
              color="white"
              backgroundColor="var(--blue)"
              onClick={() => router.push("/dashboard")}
              className="w-full py-3"
            />
          </div>
        </div>
      </MobilePageStart>
    );
  }

  // Success state with invite details
  return (
    <MobilePageStart isNavbar={false}>
      <HeaderLogo />
      <div className="w-full min-h-screen flex flex-col items-center justify-center px-4 py-6">
        <div className="bg-white rounded-xl shadow-lg w-full max-w-md overflow-y-auto max-h-[90vh]">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-6 text-white">
            <div className="flex justify-center mb-3">
              <div className="bg-white/20 rounded-full p-3">
                <CheckCircle className="h-8 w-8" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-center">
              Invito a collaborare
            </h1>
            <p className="text-center text-blue-100 mt-1">
              Sei stato invitato a collaborare su HeiBooky
            </p>
          </div>

          {/* Invitation details */}
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-start">
                <User className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Invitato da
                  </p>
                  <p className="text-gray-800 font-medium break-words">
                    {invite?.invited_by}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <Building className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Proprietà</p>
                  <p className="text-gray-800 font-medium break-words">
                    {invite?.property_name}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <Calendar className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Data di scadenza
                  </p>
                  <p className="text-gray-800 font-medium">
                    {formatDate(invite?.expires_at || "")}
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-gray-500">
                    Tempo rimanente
                  </p>
                  <p
                    className={`font-medium ${timeLeft === "Scaduto" ? "text-red-500" : "text-green-600"}`}
                  >
                    {timeLeft}
                  </p>
                </div>
              </div>
            </div>

            {/* Email information */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-100">
              <p className="text-sm text-gray-500">
                Questo invito è stato inviato a:
              </p>
              <p className="text-gray-800 font-medium mt-1 break-words">
                {invite?.email}
              </p>
            </div>
          </div>

          <div className="px-6 pb-6">
            <Button
              text="Accetta invito"
              color="white"
              backgroundColor="var(--blue)"
              onClick={handleInviteNavigation}
              className="w-full py-4 rounded-lg text-base"
              disabled={!!error}
            />

            <button
              onClick={() => router.push("/auth/login")}
              className="w-full text-center text-sm text-gray-500 mt-4 hover:text-blue-600 py-2"
            >
              Torna alla pagina di login
            </button>
          </div>
        </div>

        <p className="text-xs text-gray-400 mt-8 text-center px-4">
          Se hai problemi con questo invito, contatta l'amministratore del
          sistema
        </p>
      </div>
    </MobilePageStart>
  );
}

export default Page;
