"use client";

import styles from "../login/page.module.css";

import { Suspense, useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import Input from "@/components/inputs/input";
import Button from "@/components/buttons/Button";
import Link from "next/link";
import {
  resetPassword,
  resetPasswordConfirm,
  setPassword,
} from "@/services/api";
import toast from "react-hot-toast";
import InputHidden from "@/components/inputs/inputHidden";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Home />
    </Suspense>
  );
}

function Home() {
  const router = useRouter();
  const searchParams = useSearchParams();

  //States

  const email = searchParams.get("email");

  const [password, setPasswordvalue] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");
  const [code, setCode] = useState("");

  //Functions
  const handleSetPassword = async () => {
    if (!email) {
      toast.error("Parametri query non validi");
      return;
    }

    if (!password || !code) {
      toast.error("Password e Codice Obbligatori");
      return;
    }

    if (!repeatPassword) {
      toast.error("Ripeti Password Obbligatorio");
      return;
    }

    if (password !== repeatPassword) {
      toast.error("Le password non corrispondono");
      return;
    }

    const response = await resetPasswordConfirm(
      email,
      code,
      password,
      repeatPassword,
    );

    if (response.status === 200) {
      toast.success("Password impostata con successo");
      router.push(`/auth/login?email=${email}`);
    }
  };

  //Effects
  useEffect(() => {}, []);

  return (
    <MobilePage>
      <h1 className={styles.title}>Reimposta Password</h1>

      <form action="" className={styles.form}>
        <div className="w-full flex flex-col gap-4">
          <div
            className={styles.link4}
            style={{ textAlign: "center", padding: "0px 30px" }}
          >
            Imposta la password per il tuo account
          </div>
          <InputHidden placeholder="Code" value={code} onChange={setCode} />
          <InputHidden
            placeholder="Password"
            value={password}
            onChange={setPasswordvalue}
          />
          <InputHidden
            placeholder="Ripeti Password"
            value={repeatPassword}
            onChange={setRepeatPassword}
          />

          <div className="flex justify-center">
            <Button
              text="Salva password"
              onClick={() => {
                handleSetPassword();
              }}
              color="white"
              backgroundColor="var(--blue)"
              fontSize="15px"
            />
          </div>
        </div>
      </form>

      <div className={styles.link2}>
        Ricordi la password?{" "}
        <Link className={styles.link} href="/auth/login">
          Accedi
        </Link>
      </div>
    </MobilePage>
  );
}
